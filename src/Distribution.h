/*
 * predixy - A high performance and full features proxy for redis.
 * Copyright (C) 2017 Joyield, Inc. <<EMAIL>>
 * All rights reserved.
 */

#ifndef _PREDIXY_DISTRIBUTION_H_
#define _PREDIXY_DISTRIBUTION_H_

class Distribution
{
public:
    enum Type
    {
        None,
        <PERSON><PERSON><PERSON>,
        Random
    };
public:
    Distribution(Type t = None):
        mType(t)
    {
    }
    operator Type() const
    {
        return mType;
    }
    const char* name() const;
    static Distribution parse(const char* str);
private:
    Type mType;
};

#endif
