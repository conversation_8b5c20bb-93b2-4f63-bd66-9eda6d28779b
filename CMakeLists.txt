cmake_minimum_required(VERSION 3.18)
#set (CMAKE_C_COMPILER "/usr/bin/gcc")
#set (CMAKE_CXX_COMPILER "/usr/bin/c++")
set(CMAKE_CXX_STANDARD 11)

project(predixy)

add_definitions(-D_PREDIXY_BACKTRACE_ -D_KQUEUE_)
add_compile_options(-std=c++11 -Wall -w -g -O3 )

#option(MAC "select to use specified function" ON)
#if(MAC)
#    add_definitions(-D_PREDIXY_BACKTRACE_ -D_KQUEUE_)
#    add_compile_options(-Wall -w -g -O3)
#else()
#endif(MAC)

set(INC_DIR /usr/local/include)

include_directories(${INC_DIR}, src)

add_executable(predixy
        src/AcceptConnection.cpp
        src/AcceptConnection.h
        src/AcceptSocket.cpp
        src/AcceptSocket.h
        src/Alloc.cpp
        src/Alloc.h
        src/Auth.cpp
        src/Auth.h
        src/Backtrace.h
        src/Buffer.cpp
        src/Buffer.h
        src/ClusterNodesParser.cpp
        src/ClusterNodesParser.h
        src/ClusterServerPool.cpp
        src/ClusterServerPool.h
        src/Command.cpp
        src/Command.h
        src/Common.h
        src/Conf.cpp
        src/Conf.h
        src/ConfParser.cpp
        src/ConfParser.h
        src/ConnectConnection.cpp
        src/ConnectConnection.h
        src/ConnectConnectionPool.cpp
        src/ConnectConnectionPool.h
        src/Connection.cpp
        src/Connection.h
        src/ConnectSocket.cpp
        src/ConnectSocket.h
        src/Crc16.cpp
        src/DC.cpp
        src/DC.h
        src/Deque.h
        src/Distribution.cpp
        src/Distribution.h
        src/Enums.cpp
        src/Enums.h
#        src/EpollMultiplexor.cpp
#        src/EpollMultiplexor.h
        src/Exception.h
        src/Handler.cpp
        src/Handler.h
        src/HashFunc.cpp
        src/HashFunc.h
        src/ID.h
        src/IOVec.h
        src/KqueueMultiplexor.cpp
        src/KqueueMultiplexor.h
        src/LatencyMonitor.cpp
        src/LatencyMonitor.h
        src/List.h
        src/ListenSocket.cpp
        src/ListenSocket.h
        src/LogFileSink.cpp
        src/LogFileSink.h
        src/Logger.cpp
        src/Logger.h
        src/main.cpp
        src/Multiplexor.h
#        src/PollMultiplexor.cpp
#        src/PollMultiplexor.h
        src/Predixy.h
        src/Proxy.cpp
        src/Proxy.h
        src/Reply.cpp
        src/Reply.h
        src/Request.cpp
        src/Request.h
        src/RequestParser.cpp
        src/RequestParser.h
        src/Response.cpp
        src/Response.h
        src/ResponseParser.cpp
        src/ResponseParser.h
#        src/SentinelServerPool.cpp
#        src/SentinelServerPool.h
        src/Server.cpp
        src/Server.h
        src/ServerGroup.cpp
        src/ServerGroup.h
        src/ServerPool.cpp
        src/ServerPool.h
        src/Socket.cpp
        src/Socket.h
        src/StandaloneServerPool.cpp
        src/StandaloneServerPool.h
        src/Stats.h
        src/PString.h
        src/Subscribe.cpp
        src/Subscribe.h
        src/Sync.h
        src/Timer.cpp
        src/Timer.h
        src/Transaction.h
        src/Util.h)
