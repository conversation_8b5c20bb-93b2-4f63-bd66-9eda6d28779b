============================================================
🔒 Predixy安全扫描报告
============================================================
扫描时间: 2025-06-09 11:08:46
项目目录: /Users/<USER>/CLionProjects/predixy

🔍 CVE数据库搜索结果
------------------------------
总计找到: 10 个相关CVE

1. CVE-2018-5241 (HIGH)
   评分: 7.5
   发布时间: 2018-05-29T13:29:00.617
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: Symantec Advanced Secure Gateway (ASG) 6.6 and 6.7, and ProxySG 6.5, 6.6, and 6.7 are susceptible to a SAML authentication bypass vulnerability. The products can be configured with a SAML authentication realm to authenticate network users in intercepted proxy traffic. When parsing SAML responses, ASG and ProxySG incorrectly handle XML nodes with comments. A remote attacker can modify a valid SAML response without invalidating its cryptographic signature. This may allow the attacker to bypass user authentication security controls in ASG and ProxySG. This vulnerability only affects authentication of network users in intercepted traffic. It does not affect administrator user authentication for the ASG and ProxySG management consoles.
   参考链接: http://www.securityfocus.com/bid/104282

2. CVE-2009-0630 (HIGH)
   评分: 7.1
   发布时间: 2009-03-27T16:30:02.017
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: The (1) Cisco Unified Communications Manager Express; (2) SIP Gateway Signaling Support Over Transport Layer Security (TLS) Transport; (3) Secure Signaling and Media Encryption; (4) Blocks Extensible Exchange Protocol (BEEP); (5) Network Admission Control HTTP Authentication Proxy; (6) Per-user URL Redirect for EAPoUDP, Dot1x, and MAC Authentication Bypass; (7) Distributed Director with HTTP Redirects; and (8) TCP DNS features in Cisco IOS 12.0 through 12.4 do not properly handle IP sockets, which allows remote attackers to cause a denial of service (outage or resource consumption) via a series of crafted TCP packets.
   参考链接: http://secunia.com/advisories/34438

3. CVE-2020-6750 (MEDIUM)
   评分: 4.3
   发布时间: 2020-01-09T20:15:11.567
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: GSocketClient in GNOME GLib through 2.62.4 may occasionally connect directly to a target address instead of connecting via a proxy server when configured to do so, because the proxy_addr field is mishandled. This bug is timing-dependent and may occur only sporadically depending on network delays. The greatest security relevance is in use cases where a proxy is used to help with privacy/anonymity, even though there is no technical barrier to a direct connection. NOTE: versions before 2.60 are unaffected.
   参考链接: https://bugzilla.suse.com/show_bug.cgi?id=1160668

4. CVE-2020-2035 (LOW)
   评分: 3.5
   发布时间: 2020-08-12T17:15:12.537
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: When SSL/TLS Forward Proxy Decryption mode has been configured to decrypt the web transactions, the PAN-OS URL filtering feature inspects the HTTP Host and URL path headers for policy enforcement on the decrypted HTTPS web transactions but does not consider Server Name Indication (SNI) field within the TLS Client Hello handshake. This allows a compromised host in a protected network to evade any security policy that uses URL filtering on a firewall configured with SSL Decryption in the Forward Proxy mode. A malicious actor can then use this technique to evade detection of communication on the TLS handshake phase between a compromised host and a remote malicious server. This technique does not increase the risk of a host being compromised in the network. It does not impact the confidentiality or availability of a firewall. This is considered to have a low impact on the integrity of the firewall because the firewall fails to enforce a policy on certain traffic that should have been blocked. This issue does not impact the URL filtering policy enforcement on clear text or encrypted web transactions. This technique can be used only after a malicious actor has compromised a host in the protected network and the TLS/SSL Decryption feature is enabled for the traffic that the attacker controls. Palo Alto Networks is not aware of any malware that uses this technique to exfiltrate data. This issue is applicable to all current versions of PAN-OS. This issue does not impact Panorama or WF-500 appliances.
   参考链接: https://security.paloaltonetworks.com/CVE-2020-2035

5. CVE-2023-42261 (Unknown)
   评分: 0
   发布时间: 2023-09-21T22:15:11.823
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: Mobile Security Framework (MobSF) <=v3.7.8 Beta is vulnerable to Insecure Permissions. NOTE: the vendor's position is that authentication is intentionally not implemented because the product is not intended for an untrusted network environment. Use cases requiring authentication could, for example, use a reverse proxy server.
   参考链接: https://github.com/MobSF/Mobile-Security-Framework-MobSF/blob/abb47659a19ac772765934f184c65fe16cb3bee7/docker-compose.yml#L30-L31

6. CVE-2023-41332 (Unknown)
   评分: 0
   发布时间: 2023-09-27T15:19:30.023
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: Cilium is a networking, observability, and security solution with an eBPF-based dataplane. In Cilium clusters where Cilium's Layer 7 proxy has been disabled, creating workloads with `policy.cilium.io/proxy-visibility` annotations (in Cilium >= v1.13) or `io.cilium.proxy-visibility` annotations (in Cilium <= v1.12) causes the Cilium agent to segfault on the node to which the workload is assigned. Existing traffic on the affected node will continue to flow, but the Cilium agent on the node will not able to process changes to workloads running on the node. This will also prevent workloads from being able to start on the affected node. The denial of service will be limited to the node on which the workload is scheduled, however an attacker may be able to schedule workloads on the node of their choosing, which could lead to targeted attacks. This issue has been resolved in Cilium versions 1.14.2, 1.13.7, and 1.12.14. Users unable to upgrade can avoid this denial of service attack by enabling the Layer 7 proxy.


   参考链接: https://github.com/cilium/cilium/pull/27597

7. CVE-2024-2056 (Unknown)
   评分: 0
   发布时间: 2024-03-05T20:16:01.703
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: Services that are running and bound to the loopback interface on the Artica Proxy are accessible through the proxy service. In particular, the "tailon" service is running, running as the root user, is bound to the loopback interface, and is listening on TCP port 7050. Security issues associated with exposing this network service are documented at gvalkov's 'tailon' GitHub repo. Using the tailon service, the contents of any file on the Artica Proxy can be viewed.
   参考链接: http://seclists.org/fulldisclosure/2024/Mar/14

8. CVE-2024-28249 (Unknown)
   评分: 0
   发布时间: 2024-03-18T22:15:08.503
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: Cilium is a networking, observability, and security solution with an eBPF-based dataplane. Prior to versions 1.13.13, 1.14.8, and 1.15.2, in Cilium clusters with IPsec enabled and traffic matching Layer 7 policies, IPsec-eligible traffic between a node's Envoy proxy and pods on other nodes is sent unencrypted and IPsec-eligible traffic between a node's DNS proxy and pods on other nodes is sent unencrypted. This issue has been resolved in Cilium 1.15.2, 1.14.8, and 1.13.13. There is no known workaround for this issue.
   参考链接: https://github.com/cilium/cilium/releases/tag/v1.13.13

9. CVE-2024-28250 (Unknown)
   评分: 0
   发布时间: 2024-03-18T22:15:08.750
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: Cilium is a networking, observability, and security solution with an eBPF-based dataplane. Starting in version 1.14.0 and prior to versions 1.14.8 and 1.15.2, In Cilium clusters with WireGuard enabled and traffic matching Layer 7 policies Wireguard-eligible traffic that is sent between a node's Envoy proxy and pods on other nodes is sent unencrypted and Wireguard-eligible traffic that is sent between a node's DNS proxy and pods on other nodes is sent unencrypted. This issue has been resolved in Cilium 1.14.8 and 1.15.2 in in native routing mode (`routingMode=native`) and in Cilium 1.14.4 in tunneling mode (`routingMode=tunnel`). Not that in tunneling mode, `encryption.wireguard.encapsulate` must be set to `true`. There is no known workaround for this issue.
   参考链接: https://github.com/cilium/cilium/releases/tag/v1.13.13

10. CVE-2023-36456 (Unknown)
   评分: 0
   发布时间: 2023-07-06T19:15:10.633
   搜索关键词: network proxy security
   相关性评分: 1/10
   描述: authentik is an open-source Identity Provider. Prior to versions 2023.4.3 and 2023.5.5, authentik does not verify the source of the X-Forwarded-For and X-Real-IP headers, both in the Python code and the go code. Only authentik setups that are directly accessible by users without a reverse proxy are susceptible to this. Possible spoofing of IP addresses in logs, downstream applications proxied by (built in) outpost, IP bypassing in custom flows if used.

This poses a possible security risk when someone has flows or policies that check the user's IP address, e.g. when they want to ignore the user's 2 factor authentication when the user is connected to the company network. A second security risk is that the IP addresses in the logfiles and user sessions are not reliable anymore. Anybody can spoof this address and one cannot verify that the user has logged in from the IP address that is in their account's log. A third risk is that this header is passed on to the proxied application behind an outpost. The application may do any kind of verification, logging, blocking or rate limiting based on the IP address, and this IP address can be overridden by anybody that want to.

Versions 2023.4.3 and 2023.5.5 contain a patch for this issue.

   参考链接: https://github.com/goauthentik/authentik/commit/15026748d19d490eb2baf9a9566ead4f805f7dff

🔧 CppCheck代码扫描结果
------------------------------
总计发现: 327 个问题

问题分类统计:
  information: 31 个
  warning: 60 个
  style: 233 个
  portability: 1 个
  error: 1 个
  performance: 1 个

详细问题列表:
1. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/AcceptConnection.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

2. [WARNING] Member variable 'ServerPool::mServerTimeout' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/ServerPool.h:108:5
   详细说明: Member variable 'ServerPool::mServerTimeout' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

3. [WARNING] Member variable 'ServerPool::mKeepAlive' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/ServerPool.h:108:5
   详细说明: Member variable 'ServerPool::mKeepAlive' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

4. [WARNING] Member variable 'SString < Const :: MaxAddrLen >::mBuf' is not initialized in the copy constructor.
   问题ID: uninitMemberVar
   位置: src/PString.h:185:5
   详细说明: Member variable 'SString < Const :: MaxAddrLen >::mBuf' is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

5. [WARNING] Member variable 'SString < Const :: MaxServNameLen >::mBuf' is not initialized in the copy constructor.
   问题ID: uninitMemberVar
   位置: src/PString.h:185:5
   详细说明: Member variable 'SString < Const :: MaxServNameLen >::mBuf' is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

6. [WARNING] The class 'ConnectSocket' defines member function with name 'close' also defined in its parent class 'Socket'.
   问题ID: duplInheritedMember
   位置: src/ConnectSocket.h:27:10
   位置: src/Socket.h:59:10
   详细说明: The class 'ConnectSocket' defines member function with name 'close' also defined in its parent class 'Socket'.

7. [WARNING] The class 'AcceptConnection' defines member function with name 'close' also defined in its parent class 'Socket'.
   问题ID: duplInheritedMember
   位置: src/AcceptConnection.cpp:27:24
   位置: src/Socket.h:59:10
   详细说明: The class 'AcceptConnection' defines member function with name 'close' also defined in its parent class 'Socket'.

8. [WARNING] The class 'ClusterServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/ClusterServerPool.h:29:13
   位置: src/ServerPool.h:89:13
   详细说明: The class 'ClusterServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.

9. [WARNING] The class 'ClusterServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/ClusterServerPool.h:30:10
   位置: src/ServerPool.h:97:10
   详细说明: The class 'ClusterServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.

10. [WARNING] The class 'ClusterServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/ClusterServerPool.h:31:10
   位置: src/ServerPool.h:101:10
   详细说明: The class 'ClusterServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.

11. [WARNING] The class 'ClusterServerPool' defines member function with name 'iter' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/ClusterServerPool.h:37:13
   位置: src/ServerPool.h:93:13
   详细说明: The class 'ClusterServerPool' defines member function with name 'iter' also defined in its parent class 'ServerPool'.

12. [WARNING] The class 'StandaloneServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/StandaloneServerPool.h:22:13
   位置: src/ServerPool.h:89:13
   详细说明: The class 'StandaloneServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.

13. [WARNING] The class 'StandaloneServerPool' defines member function with name 'iter' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/StandaloneServerPool.h:23:13
   位置: src/ServerPool.h:93:13
   详细说明: The class 'StandaloneServerPool' defines member function with name 'iter' also defined in its parent class 'ServerPool'.

14. [WARNING] The class 'StandaloneServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/StandaloneServerPool.h:27:10
   位置: src/ServerPool.h:97:10
   详细说明: The class 'StandaloneServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.

15. [WARNING] The class 'StandaloneServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/StandaloneServerPool.h:28:10
   位置: src/ServerPool.h:101:10
   详细说明: The class 'StandaloneServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.

16. [STYLE] Class 'Hash' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/HashFunc.h:28:5
   详细说明: Class 'Hash' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

17. [STYLE] Class 'IDUnique' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/ID.h:64:5
   详细说明: Class 'IDUnique' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

18. [STYLE] Class 'StrErrorImpl' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Util.h:33:5
   详细说明: Class 'StrErrorImpl' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

19. [STYLE] Class 'String' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:24:5
   详细说明: Class 'String' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

20. [STYLE] Class 'String' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:34:5
   详细说明: Class 'String' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

21. [STYLE] Class 'TimerPoint' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Timer.h:17:5
   详细说明: Class 'TimerPoint' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

22. [STYLE] Class 'Timer' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Timer.h:56:5
   详细说明: Class 'Timer' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

23. [STYLE] Class 'Logger' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Logger.h:58:5
   详细说明: Class 'Logger' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

24. [STYLE] Class 'Socket' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Socket.h:52:5
   详细说明: Class 'Socket' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

25. [STYLE] Class 'SegmentStr' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Buffer.h:211:5
   详细说明: Class 'SegmentStr' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

26. [STYLE] Class 'Request' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Request.h:56:5
   详细说明: Class 'Request' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

27. [STYLE] Class 'Request' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Request.h:57:5
   详细说明: Class 'Request' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

28. [STYLE] Class 'Response' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Response.h:49:5
   详细说明: Class 'Response' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

29. [STYLE] Class 'Distribution' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Distribution.h:20:5
   详细说明: Class 'Distribution' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

30. [STYLE] Class 'ConfParser' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/ConfParser.h:73:5
   详细说明: Class 'ConfParser' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

31. [STYLE] Class 'Auth' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Auth.h:18:5
   详细说明: Class 'Auth' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

32. [STYLE] Class 'Auth' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Auth.h:19:5
   详细说明: Class 'Auth' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

33. [STYLE] Class 'ServerPoolRefreshMethod' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Enums.h:68:5
   详细说明: Class 'ServerPoolRefreshMethod' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

34. [STYLE] Class 'ClusterServerPool' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/ClusterServerPool.h:20:5
   详细说明: Class 'ClusterServerPool' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

35. [STYLE] Class 'StandaloneServerPool' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/StandaloneServerPool.h:19:5
   详细说明: Class 'StandaloneServerPool' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

36. [STYLE] Class 'Handler' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Handler.h:24:5
   详细说明: Class 'Handler' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

37. [STYLE] Class 'EnumBase < ServerPoolRefreshMethod >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Enums.h:25:5
   详细说明: Class 'EnumBase < ServerPoolRefreshMethod >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

38. [STYLE] Class 'SharePtr < Buffer >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Alloc.h:171:5
   详细说明: Class 'SharePtr < Buffer >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

39. [STYLE] Class 'SharePtr < Request >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Alloc.h:171:5
   详细说明: Class 'SharePtr < Request >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

40. [STYLE] Class 'SharePtr < Response >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Alloc.h:171:5
   详细说明: Class 'SharePtr < Response >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

41. [STYLE] Class 'SharePtr < AcceptConnection >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Alloc.h:171:5
   详细说明: Class 'SharePtr < AcceptConnection >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

42. [STYLE] Class 'SString < Const :: MaxAddrLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:171:5
   详细说明: Class 'SString < Const :: MaxAddrLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

43. [STYLE] Class 'SString < Const :: MaxAddrLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:181:5
   详细说明: Class 'SString < Const :: MaxAddrLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

44. [STYLE] Class 'SString < Const :: MaxAddrLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:190:5
   详细说明: Class 'SString < Const :: MaxAddrLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

45. [STYLE] Class 'SString < Const :: MaxServNameLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:171:5
   详细说明: Class 'SString < Const :: MaxServNameLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

46. [STYLE] Class 'SString < Const :: MaxServNameLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:181:5
   详细说明: Class 'SString < Const :: MaxServNameLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

47. [STYLE] Class 'SString < Const :: MaxServNameLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:190:5
   详细说明: Class 'SString < Const :: MaxServNameLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

48. [STYLE] The destructor '~ClusterServerPool' overrides a destructor in a base class but is not marked with a 'override' specifier.
   问题ID: missingOverride
   位置: src/ClusterServerPool.h:21:6
   位置: src/ServerPool.h:28:14
   详细说明: The destructor '~ClusterServerPool' overrides a destructor in a base class but is not marked with a 'override' specifier.

49. [STYLE] The destructor '~StandaloneServerPool' overrides a destructor in a base class but is not marked with a 'override' specifier.
   问题ID: missingOverride
   位置: src/StandaloneServerPool.h:20:6
   位置: src/ServerPool.h:28:14
   详细说明: The destructor '~StandaloneServerPool' overrides a destructor in a base class but is not marked with a 'override' specifier.

50. [STYLE] Expression is always false because 'else if' condition matches previous condition at line 128.
   问题ID: multiCondition
   位置: src/PString.h:130:32
   详细说明: Expression is always false because 'else if' condition matches previous condition at line 128.

51. [STYLE] Return value 'mLen==len' is always false
   问题ID: knownConditionTrueFalse
   位置: src/PString.h:242:21
   位置: src/PString.h:241:14
   详细说明: Return value 'mLen==len' is always false

52. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/Request.h:123:58
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

53. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/ServerPool.h:156:29
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

54. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/Alloc.h:61:22
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

55. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/Alloc.h:70:39
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

56. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/Alloc.h:111:31
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

57. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/Deque.h:98:32
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

58. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/Deque.h:46:52
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

59. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/List.h:71:32
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

60. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/List.h:93:28
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

61. [STYLE] The comparison 'i > 0' is always false.
   问题ID: knownConditionTrueFalse
   位置: src/PString.h:127:15
   位置: src/PString.h:125:17
   详细说明: Finding the same expression on both sides of an operator is suspicious and might indicate a cut and paste or logic error. Please examine this code carefully to determine if it is correct.

62. [STYLE] Local variable 'prev' shadows outer function
   问题ID: shadowFunction
   位置: src/Deque.h:119:14
   位置: src/Deque.h:81:7
   详细说明: Local variable 'prev' shadows outer function

63. [STYLE] Local variable 'next' shadows outer function
   问题ID: shadowFunction
   位置: src/Deque.h:120:14
   位置: src/Deque.h:85:7
   详细说明: Local variable 'next' shadows outer function

64. [STYLE] Local variable 'next' shadows outer function
   问题ID: shadowFunction
   位置: src/Deque.h:143:18
   位置: src/Deque.h:85:7
   详细说明: Local variable 'next' shadows outer function

65. [STYLE] Local variable 'prev' shadows outer function
   问题ID: shadowFunction
   位置: src/Deque.h:144:22
   位置: src/Deque.h:81:7
   详细说明: Local variable 'prev' shadows outer function

66. [STYLE] Variable 'res' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/AcceptConnection.cpp:74:14
   详细说明: Variable 'res' can be declared as pointer to const

67. [STYLE] Parameter 'serv' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/Handler.h:58:33
   详细说明: Parameter 'serv' can be declared as pointer to const

68. [STYLE] Parameter 'serv' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/Handler.h:65:37
   详细说明: Parameter 'serv' can be declared as pointer to const

69. [STYLE] Parameter 'serv' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/Handler.h:70:38
   详细说明: Parameter 'serv' can be declared as pointer to const

70. [STYLE] Parameter 'oth' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/DC.h:31:18
   详细说明: Parameter 'oth' can be declared as pointer to const

71. [STYLE] Parameter 'oth' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/DC.h:36:29
   详细说明: Parameter 'oth' can be declared as pointer to const

72. [STYLE] Parameter 'oth' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/DC.h:40:27
   详细说明: Parameter 'oth' can be declared as pointer to const

73. [STYLE] Parameter 'oth' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/DC.h:44:43
   详细说明: Parameter 'oth' can be declared as pointer to const

74. [STYLE] Consider using std::find_if algorithm instead of a raw loop.
   问题ID: useStlAlgorithm
   位置: src/Enums.h:48:47
   详细说明: Consider using std::find_if algorithm instead of a raw loop.

75. [STYLE] The destructor '~EpollMultiplexor' overrides a destructor in a base class but is not marked with a 'override' specifier.
   问题ID: missingOverride
   位置: src/EpollMultiplexor.h:23:6
   位置: src/Multiplexor.h:24:14
   详细说明: The destructor '~EpollMultiplexor' overrides a destructor in a base class but is not marked with a 'override' specifier.

76. [STYLE] The destructor '~KqueueMultiplexor' overrides a destructor in a base class but is not marked with a 'override' specifier.
   问题ID: missingOverride
   位置: src/KqueueMultiplexor.h:25:6
   位置: src/Multiplexor.h:24:14
   详细说明: The destructor '~KqueueMultiplexor' overrides a destructor in a base class but is not marked with a 'override' specifier.

77. [STYLE] Class 'PollMultiplexor' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PollMultiplexor.h:21:5
   详细说明: Class 'PollMultiplexor' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

78. [STYLE] The destructor '~PollMultiplexor' overrides a destructor in a base class but is not marked with a 'override' specifier.
   问题ID: missingOverride
   位置: src/PollMultiplexor.h:22:6
   位置: src/Multiplexor.h:24:14
   详细说明: The destructor '~PollMultiplexor' overrides a destructor in a base class but is not marked with a 'override' specifier.

79. [STYLE] Variable 'lck' is assigned a value that is never used.
   问题ID: unreadVariable
   位置: src/ServerPool.h:85:30
   详细说明: Variable 'lck' is assigned a value that is never used.

80. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/AcceptSocket.cpp:19:31
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

81. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/AcceptSocket.cpp:20:32
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

82. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/AcceptSocket.cpp:28:32
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

83. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/AcceptSocket.cpp:29:33
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

84. [STYLE] Class 'SharePtr' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Alloc.h:171:5
   详细说明: Class 'SharePtr' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

85. [PORTABILITY] Undefined behaviour, when 'n' is -3 the pointer arithmetic 'mBuf+n' is out of bounds.
   问题ID: pointerOutOfBoundsCond
   位置: src/PString.h:308:35
   位置: src/PString.h:307:34
   详细说明: Undefined behaviour, when 'n' is -3 the pointer arithmetic 'mBuf+n' is out of bounds.

86. [WARNING] Member variable 'SString::mBuf' is not initialized in the copy constructor.
   问题ID: uninitMemberVar
   位置: src/PString.h:185:5
   详细说明: Member variable 'SString::mBuf' is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

87. [WARNING] Class 'Auth' does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s).
   问题ID: noCopyConstructor
   位置: src/Auth.cpp:27:9
   详细说明: Class 'Auth' does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s).

88. [WARNING] Class 'Auth' does not have a operator= which is recommended since it has dynamic memory/resource allocation(s).
   问题ID: noOperatorEq
   位置: src/Auth.cpp:27:9
   详细说明: Class 'Auth' does not have a operator= which is recommended since it has dynamic memory/resource allocation(s).

89. [STYLE] Class 'SString' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:171:5
   详细说明: Class 'SString' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

90. [STYLE] Class 'SString' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:181:5
   详细说明: Class 'SString' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

91. [STYLE] Class 'SString' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:190:5
   详细说明: Class 'SString' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

92. [ERROR] Memory leak: kp
   问题ID: memleak
   位置: src/Auth.cpp:40:5
   详细说明: Memory leak: kp

93. [STYLE] Variable 'i' can be declared as reference to const
   问题ID: constVariableReference
   位置: src/Auth.cpp:88:16
   详细说明: Variable 'i' can be declared as reference to const

94. [STYLE] Parameter 'req' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/Auth.cpp:53:32
   详细说明: Parameter 'req' can be declared as pointer to const

95. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/Auth.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

96. [WARNING] Member variable 'Buffer::mDat' is not initialized in the constructor.
   问题ID: uninitMemberVarPrivate
   位置: src/Buffer.cpp:14:9
   详细说明: Member variable 'Buffer::mDat' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

97. [STYLE] Local variable 'end' shadows outer function
   问题ID: shadowFunction
   位置: src/Buffer.cpp:339:10
   位置: src/Buffer.h:183:16
   详细说明: Local variable 'end' shadows outer function

98. [STYLE] Local variable 'end' shadows outer function
   问题ID: shadowFunction
   位置: src/Buffer.cpp:365:10
   位置: src/Buffer.h:183:16
   详细说明: Local variable 'end' shadows outer function

99. [STYLE] Variable 'buf' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Buffer.cpp:218:13
   详细说明: Variable 'buf' can be declared as pointer to const

100. [WARNING] Member variable 'ClusterNodesParser::mRole' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/ClusterNodesParser.cpp:11:21
   详细说明: Member variable 'ClusterNodesParser::mRole' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

101. [WARNING] Member variable 'ClusterNodesParser::mFieldCnt' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/ClusterNodesParser.cpp:11:21
   详细说明: Member variable 'ClusterNodesParser::mFieldCnt' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

102. [WARNING] Member variable 'ClusterNodesParser::mSlotBegin' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/ClusterNodesParser.cpp:11:21
   详细说明: Member variable 'ClusterNodesParser::mSlotBegin' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

103. [WARNING] Member variable 'ClusterNodesParser::mSlotEnd' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/ClusterNodesParser.cpp:11:21
   详细说明: Member variable 'ClusterNodesParser::mSlotEnd' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

104. [WARNING] Member variable 'SString < NodeIdLen >::mBuf' is not initialized in the copy constructor.
   问题ID: uninitMemberVar
   位置: src/PString.h:185:5
   详细说明: Member variable 'SString < NodeIdLen >::mBuf' is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

105. [WARNING] Member variable 'SString < AddrLen >::mBuf' is not initialized in the copy constructor.
   问题ID: uninitMemberVar
   位置: src/PString.h:185:5
   详细说明: Member variable 'SString < AddrLen >::mBuf' is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

106. [WARNING] Member variable 'SString < FlagsLen >::mBuf' is not initialized in the copy constructor.
   问题ID: uninitMemberVar
   位置: src/PString.h:185:5
   详细说明: Member variable 'SString < FlagsLen >::mBuf' is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

107. [STYLE] Class 'SString < NodeIdLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:171:5
   详细说明: Class 'SString < NodeIdLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

108. [STYLE] Class 'SString < NodeIdLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:181:5
   详细说明: Class 'SString < NodeIdLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

109. [STYLE] Class 'SString < NodeIdLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:190:5
   详细说明: Class 'SString < NodeIdLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

110. [STYLE] Class 'SString < AddrLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:171:5
   详细说明: Class 'SString < AddrLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

111. [STYLE] Class 'SString < AddrLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:181:5
   详细说明: Class 'SString < AddrLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

112. [STYLE] Class 'SString < AddrLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:190:5
   详细说明: Class 'SString < AddrLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

113. [STYLE] Class 'SString < FlagsLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:171:5
   详细说明: Class 'SString < FlagsLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

114. [STYLE] Class 'SString < FlagsLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:181:5
   详细说明: Class 'SString < FlagsLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

115. [STYLE] Class 'SString < FlagsLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:190:5
   详细说明: Class 'SString < FlagsLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

116. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/ClusterNodesParser.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

117. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/ClusterServerPool.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

118. [WARNING] The class 'AcceptConnection' defines member function with name 'close' also defined in its parent class 'Socket'.
   问题ID: duplInheritedMember
   位置: src/AcceptConnection.h:36:10
   位置: src/Socket.h:59:10
   详细说明: The class 'AcceptConnection' defines member function with name 'close' also defined in its parent class 'Socket'.

119. [WARNING] The class 'ClusterServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/ClusterServerPool.cpp:36:28
   位置: src/ServerPool.h:89:13
   详细说明: The class 'ClusterServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.

120. [WARNING] The class 'ClusterServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/ClusterServerPool.cpp:82:25
   位置: src/ServerPool.h:97:10
   详细说明: The class 'ClusterServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.

121. [WARNING] The class 'ClusterServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/ClusterServerPool.cpp:89:25
   位置: src/ServerPool.h:101:10
   详细说明: The class 'ClusterServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.

122. [PERFORMANCE] Function 'name()' should return member 'mName' by const reference.
   问题ID: returnByReference
   位置: src/ServerGroup.h:23:12
   详细说明: Function 'name()' should return member 'mName' by const reference.

123. [STYLE] Variable 'sc' can be declared as reference to const
   问题ID: constVariableReference
   位置: src/ClusterServerPool.cpp:74:16
   详细说明: Variable 'sc' can be declared as reference to const

124. [STYLE] Variable 'g' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/ClusterServerPool.cpp:48:18
   详细说明: Variable 'g' can be declared as pointer to const

125. [STYLE] Parameter 'old' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/ClusterServerPool.cpp:55:65
   详细说明: Parameter 'old' can be declared as pointer to const

126. [STYLE] Parameter 's' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/ClusterServerPool.cpp:89:71
   详细说明: Parameter 's' can be declared as pointer to const

127. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/Command.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

128. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/Conf.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

129. [WARNING] Member variable 'Conf::mStandaloneServerPool' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/Conf.cpp:43:7
   详细说明: Member variable 'Conf::mStandaloneServerPool' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

130. [STYLE] Local variable 'clusterServerPool' shadows outer function
   问题ID: shadowFunction
   位置: src/Conf.cpp:127:29
   位置: src/Conf.h:176:34
   详细说明: Local variable 'clusterServerPool' shadows outer function

131. [STYLE] Local variable 'standaloneServerPool' shadows outer function
   问题ID: shadowFunction
   位置: src/Conf.cpp:128:29
   位置: src/Conf.h:180:37
   详细说明: Local variable 'standaloneServerPool' shadows outer function

132. [STYLE] Local variable 'latencyMonitors' shadows outer function
   问题ID: shadowFunction
   位置: src/Conf.cpp:130:42
   位置: src/Conf.h:192:44
   详细说明: Local variable 'latencyMonitors' shadows outer function

133. [STYLE] Variable 'g' can be declared as reference to const
   问题ID: constVariableReference
   位置: src/Conf.cpp:360:20
   详细说明: Variable 'g' can be declared as reference to const

134. [STYLE] Variable 'dc' can be declared as reference to const
   问题ID: constVariableReference
   位置: src/Conf.cpp:576:20
   详细说明: Variable 'dc' can be declared as reference to const

135. [STYLE] Variable 'dc' can be declared as reference to const
   问题ID: constVariableReference
   位置: src/Conf.cpp:586:20
   详细说明: Variable 'dc' can be declared as reference to const

136. [STYLE] Variable 'rp' can be declared as reference to const
   问题ID: constVariableReference
   位置: src/Conf.cpp:587:24
   详细说明: Variable 'rp' can be declared as reference to const

137. [STYLE] Variable 'i' can be declared as reference to const
   问题ID: constVariableReference
   位置: src/Conf.cpp:589:28
   详细说明: Variable 'i' can be declared as reference to const

138. [STYLE] Variable 'n' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Conf.cpp:104:23
   详细说明: Variable 'n' can be declared as pointer to const

139. [STYLE] Variable 'v' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Conf.cpp:111:26
   详细说明: Variable 'v' can be declared as pointer to const

140. [STYLE] Consider using std::find_if algorithm instead of a raw loop.
   问题ID: useStlAlgorithm
   位置: src/Conf.cpp:361:36
   详细说明: Consider using std::find_if algorithm instead of a raw loop.

141. [STYLE] Consider using std::any_of algorithm instead of a raw loop.
   问题ID: useStlAlgorithm
   位置: src/Conf.cpp:577:38
   详细说明: Consider using std::any_of algorithm instead of a raw loop.

142. [STYLE] Consider using std::any_of algorithm instead of a raw loop.
   问题ID: useStlAlgorithm
   位置: src/Conf.cpp:590:44
   详细说明: Consider using std::any_of algorithm instead of a raw loop.

143. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/ConfParser.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

144. [STYLE] Variable 'pre' can be declared as reference to const
   问题ID: constVariableReference
   位置: src/ConfParser.cpp:119:32
   详细说明: Variable 'pre' can be declared as reference to const

145. [STYLE] Consider using std::any_of algorithm instead of a raw loop.
   问题ID: useStlAlgorithm
   位置: src/ConfParser.cpp:120:67
   详细说明: Consider using std::any_of algorithm instead of a raw loop.

146. [STYLE] Consider using std::count_if algorithm instead of a raw loop.
   问题ID: useStlAlgorithm
   位置: src/ConfParser.cpp:296:19
   详细说明: Consider using std::count_if algorithm instead of a raw loop.

147. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/ConnectConnection.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

148. [STYLE] Variable 'req' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/ConnectConnection.cpp:193:30
   详细说明: Variable 'req' can be declared as pointer to const

149. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/ConnectConnectionPool.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

150. [STYLE] Variable 'sp' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/ConnectConnectionPool.cpp:125:10
   详细说明: Variable 'sp' can be declared as pointer to const

151. [WARNING] The class 'ConnectSocket' defines member function with name 'close' also defined in its parent class 'Socket'.
   问题ID: duplInheritedMember
   位置: src/ConnectSocket.cpp:62:21
   位置: src/Socket.h:59:10
   详细说明: The class 'ConnectSocket' defines member function with name 'close' also defined in its parent class 'Socket'.

152. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/ConnectSocket.cpp:17:40
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

153. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/ConnectSocket.cpp:18:20
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

154. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/ConnectSocket.cpp:32:36
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

155. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/ConnectSocket.cpp:56:20
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

156. [STYLE] Variable 'in' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/ConnectSocket.cpp:18:15
   详细说明: Variable 'in' can be declared as pointer to const

157. [STYLE] Variable 'in' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/ConnectSocket.cpp:56:15
   详细说明: Variable 'in' can be declared as pointer to const

158. [STYLE] Parameter 'h' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/Connection.cpp:17:42
   详细说明: Parameter 'h' can be declared as pointer to const

159. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/DC.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

160. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/EpollMultiplexor.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

161. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/Handler.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

162. [WARNING] Member variable 'SString < Const :: MaxKeyLen >::mBuf' is not initialized in the copy constructor.
   问题ID: uninitMemberVar
   位置: src/PString.h:185:5
   详细说明: Member variable 'SString < Const :: MaxKeyLen >::mBuf' is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

163. [WARNING] Member variable 'SString < 32 >::mBuf' is not initialized in the copy constructor.
   问题ID: uninitMemberVar
   位置: src/PString.h:185:5
   详细说明: Member variable 'SString < 32 >::mBuf' is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

164. [WARNING] Member variable 'SString < 512 >::mBuf' is not initialized in the copy constructor.
   问题ID: uninitMemberVar
   位置: src/PString.h:185:5
   详细说明: Member variable 'SString < 512 >::mBuf' is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

165. [WARNING] Class 'Handler' does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s).
   问题ID: noCopyConstructor
   位置: src/Handler.cpp:27:5
   详细说明: Class 'Handler' does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s).

166. [WARNING] Class 'Handler' does not have a operator= which is recommended since it has dynamic memory/resource allocation(s).
   问题ID: noOperatorEq
   位置: src/Handler.cpp:27:5
   详细说明: Class 'Handler' does not have a operator= which is recommended since it has dynamic memory/resource allocation(s).

167. [WARNING] The class 'SentinelServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/SentinelServerPool.h:22:13
   位置: src/ServerPool.h:89:13
   详细说明: The class 'SentinelServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.

168. [WARNING] The class 'SentinelServerPool' defines member function with name 'iter' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/SentinelServerPool.h:23:13
   位置: src/ServerPool.h:93:13
   详细说明: The class 'SentinelServerPool' defines member function with name 'iter' also defined in its parent class 'ServerPool'.

169. [WARNING] The class 'SentinelServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/SentinelServerPool.h:27:10
   位置: src/ServerPool.h:97:10
   详细说明: The class 'SentinelServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.

170. [WARNING] The class 'SentinelServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/SentinelServerPool.h:28:10
   位置: src/ServerPool.h:101:10
   详细说明: The class 'SentinelServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.

171. [STYLE] Class 'SentinelServerPool' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/SentinelServerPool.h:19:5
   详细说明: Class 'SentinelServerPool' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

172. [STYLE] Class 'SegmentStr < Const :: MaxKeyLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Buffer.h:211:5
   详细说明: Class 'SegmentStr < Const :: MaxKeyLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

173. [STYLE] Class 'SegmentStr < 64 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Buffer.h:211:5
   详细说明: Class 'SegmentStr < 64 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

174. [STYLE] Class 'SegmentStr < 128 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Buffer.h:211:5
   详细说明: Class 'SegmentStr < 128 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

175. [STYLE] Class 'SegmentStr < 256 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Buffer.h:211:5
   详细说明: Class 'SegmentStr < 256 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

176. [STYLE] Class 'SString < Const :: MaxKeyLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:171:5
   详细说明: Class 'SString < Const :: MaxKeyLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

177. [STYLE] Class 'SString < Const :: MaxKeyLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:181:5
   详细说明: Class 'SString < Const :: MaxKeyLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

178. [STYLE] Class 'SString < Const :: MaxKeyLen >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:190:5
   详细说明: Class 'SString < Const :: MaxKeyLen >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

179. [STYLE] Class 'SString < 32 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:171:5
   详细说明: Class 'SString < 32 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

180. [STYLE] Class 'SString < 32 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:181:5
   详细说明: Class 'SString < 32 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

181. [STYLE] Class 'SString < 32 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:190:5
   详细说明: Class 'SString < 32 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

182. [STYLE] Class 'SString < 512 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:171:5
   详细说明: Class 'SString < 512 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

183. [STYLE] Class 'SString < 512 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:181:5
   详细说明: Class 'SString < 512 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

184. [STYLE] Class 'SString < 512 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:190:5
   详细说明: Class 'SString < 512 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

185. [STYLE] The destructor '~SentinelServerPool' overrides a destructor in a base class but is not marked with a 'override' specifier.
   问题ID: missingOverride
   位置: src/SentinelServerPool.h:20:6
   位置: src/ServerPool.h:28:14
   详细说明: The destructor '~SentinelServerPool' overrides a destructor in a base class but is not marked with a 'override' specifier.

186. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/Handler.cpp:295:32
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

187. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/Handler.cpp:298:37
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

188. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/Handler.cpp:1269:22
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

189. [STYLE] Consecutive return, break, continue, goto or throw statements are unnecessary.
   问题ID: duplicateBreak
   位置: src/Handler.cpp:675:9
   详细说明: Consecutive return, break, continue, goto or throw statements are unnecessary. The second statement can never be executed, and so should be removed.

190. [STYLE] The scope of the variable 'slot' can be reduced.
   问题ID: variableScope
   位置: src/Handler.cpp:1432:9
   详细说明: The scope of the variable 'slot' can be reduced. Warning: Be careful when fixing this message, especially when there are inner loops. Here is an example where cppcheck will write that the scope for 'i' can be reduced:\012void f(int x)\012{\012    int i = 0;\012    if (x) {\012        // it's safe to move 'int i = 0;' here\012        for (int n = 0; n < 10; ++n) {\012            // it is possible but not safe to move 'int i = 0;' here\012            do_something(&i);\012        }\012    }\012}\012When you see this message it is always safe to reduce the variable scope 1 level.

191. [STYLE] Variable 'conf' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:39:11
   详细说明: Variable 'conf' can be declared as pointer to const

192. [STYLE] Variable 'conf' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:51:10
   详细说明: Variable 'conf' can be declared as pointer to const

193. [STYLE] Variable 'auth' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:336:14
   详细说明: Variable 'auth' can be declared as pointer to const

194. [STYLE] Parameter 'req' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/Handler.cpp:439:59
   详细说明: Parameter 'req' can be declared as pointer to const

195. [STYLE] Variable 'c' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:460:10
   详细说明: Variable 'c' can be declared as pointer to const

196. [STYLE] Variable 'sp' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:520:10
   详细说明: Variable 'sp' can be declared as pointer to const

197. [STYLE] Variable 'sp' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:611:18
   详细说明: Variable 'sp' can be declared as pointer to const

198. [STYLE] Variable 'g' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:652:18
   详细说明: Variable 'g' can be declared as pointer to const

199. [STYLE] Parameter 'req' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/Handler.cpp:682:42
   详细说明: Parameter 'req' can be declared as pointer to const

200. [STYLE] Variable 'g' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:816:18
   详细说明: Variable 'g' can be declared as pointer to const

201. [STYLE] Variable 'h' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:975:19
   详细说明: Variable 'h' can be declared as pointer to const

202. [STYLE] Variable 'sp' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:994:14
   详细说明: Variable 'sp' can be declared as pointer to const

203. [STYLE] Variable 'serv' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:995:24
   详细说明: Variable 'serv' can be declared as pointer to const

204. [STYLE] Variable 'h' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:997:23
   详细说明: Variable 'h' can be declared as pointer to const

205. [STYLE] Variable 'g' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:1004:18
   详细说明: Variable 'g' can be declared as pointer to const

206. [STYLE] Variable 'serv' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:1079:20
   详细说明: Variable 'serv' can be declared as pointer to const

207. [STYLE] Variable 'h' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:1082:19
   详细说明: Variable 'h' can be declared as pointer to const

208. [STYLE] Variable 'h' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:1141:19
   详细说明: Variable 'h' can be declared as pointer to const

209. [STYLE] Variable 'h' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:1154:23
   详细说明: Variable 'h' can be declared as pointer to const

210. [STYLE] Parameter 'c' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/Handler.cpp:1426:43
   详细说明: Parameter 'c' can be declared as pointer to const

211. [STYLE] Parameter 'res' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/Handler.cpp:1426:70
   详细说明: Parameter 'res' can be declared as pointer to const

212. [STYLE] Variable 'p' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:1443:10
   详细说明: Variable 'p' can be declared as pointer to const

213. [STYLE] Variable 'auth' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Handler.cpp:1481:25
   详细说明: Variable 'auth' can be declared as pointer to const

214. [STYLE] Consider using std::accumulate algorithm instead of a raw loop.
   问题ID: useStlAlgorithm
   位置: src/Handler.cpp:999:24
   详细说明: Consider using std::accumulate algorithm instead of a raw loop.

215. [STYLE] Consider using std::accumulate algorithm instead of a raw loop.
   问题ID: useStlAlgorithm
   位置: src/Handler.cpp:1084:20
   详细说明: Consider using std::accumulate algorithm instead of a raw loop.

216. [STYLE] Consider using std::accumulate algorithm instead of a raw loop.
   问题ID: useStlAlgorithm
   位置: src/Handler.cpp:1143:20
   详细说明: Consider using std::accumulate algorithm instead of a raw loop.

217. [STYLE] Consider using std::accumulate algorithm instead of a raw loop.
   问题ID: useStlAlgorithm
   位置: src/Handler.cpp:1156:24
   详细说明: Consider using std::accumulate algorithm instead of a raw loop.

218. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/HashFunc.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

219. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/KqueueMultiplexor.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

220. [STYLE] Local variable 'i' shadows outer variable
   问题ID: shadowVariable
   位置: src/LatencyMonitor.cpp:55:18
   位置: src/LatencyMonitor.cpp:41:9
   详细说明: Local variable 'i' shadows outer variable

221. [STYLE] Variable 's' can be declared as reference to const
   问题ID: constVariableReference
   位置: src/LatencyMonitor.cpp:12:16
   详细说明: Variable 's' can be declared as reference to const

222. [STYLE] Consider using std::accumulate algorithm instead of a raw loop.
   问题ID: useStlAlgorithm
   位置: src/LatencyMonitor.cpp:13:12
   详细说明: Consider using std::accumulate algorithm instead of a raw loop.

223. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/LatencyMonitor.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

224. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/ListenSocket.cpp:17:40
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

225. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/ListenSocket.cpp:18:20
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

226. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/LogFileSink.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

227. [WARNING] Member variable 'LogFileSink::mFilePath' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/LogFileSink.cpp:18:14
   详细说明: Member variable 'LogFileSink::mFilePath' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

228. [STYLE] int result is assigned to long & variable. If the variable is long & to avoid loss of information, then you have loss of information.
   问题ID: truncLongCastAssignment
   位置: src/LogFileSink.cpp:70:19
   详细说明: int result is assigned to long & variable. If the variable is long & to avoid loss of information, then there is loss of information. To avoid loss of information you must cast a calculation operand to long &, for example 'l = a * b;' => 'l = (long &)a * b;'.

229. [STYLE] int result is assigned to long & variable. If the variable is long & to avoid loss of information, then you have loss of information.
   问题ID: truncLongCastAssignment
   位置: src/LogFileSink.cpp:72:19
   详细说明: int result is assigned to long & variable. If the variable is long & to avoid loss of information, then there is loss of information. To avoid loss of information you must cast a calculation operand to long &, for example 'l = a * b;' => 'l = (long &)a * b;'.

230. [STYLE] Variable 'rotate' is assigned a value that is never used.
   问题ID: unreadVariable
   位置: src/LogFileSink.cpp:176:20
   详细说明: Variable 'rotate' is assigned a value that is never used.

231. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/Logger.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

232. [WARNING] Member variable 'LogUnit::mBuf' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/Logger.cpp:26:10
   详细说明: Member variable 'LogUnit::mBuf' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

233. [WARNING] Member variable 'Logger::mThread' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/Logger.cpp:72:9
   详细说明: Member variable 'Logger::mThread' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

234. [STYLE] Condition '!mStop' is always true
   问题ID: knownConditionTrueFalse
   位置: src/Logger.cpp:127:37
   位置: src/Logger.cpp:123:12
   详细说明: Condition '!mStop' is always true

235. [STYLE] Local variable 'log' shadows outer function
   问题ID: shadowFunction
   位置: src/Logger.cpp:136:23
   位置: src/Logger.h:79:14
   详细说明: Local variable 'log' shadows outer function

236. [STYLE] Local variable 'log' shadows outer function
   问题ID: shadowFunction
   位置: src/Logger.cpp:144:23
   位置: src/Logger.h:79:14
   详细说明: Local variable 'log' shadows outer function

237. [STYLE] Local variable 'log' shadows outer function
   问题ID: shadowFunction
   位置: src/Logger.cpp:151:21
   位置: src/Logger.h:79:14
   详细说明: Local variable 'log' shadows outer function

238. [STYLE] Local variable 'log' shadows outer function
   问题ID: shadowFunction
   位置: src/Logger.cpp:173:14
   位置: src/Logger.h:79:14
   详细说明: Local variable 'log' shadows outer function

239. [STYLE] Consider using std::copy algorithm instead of a raw loop.
   问题ID: useStlAlgorithm
   位置: src/Logger.cpp:145:23
   详细说明: Consider using std::copy algorithm instead of a raw loop.

240. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/PollMultiplexor.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

241. [STYLE] Parameter 's' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/PollMultiplexor.cpp:36:41
   详细说明: Parameter 's' can be declared as pointer to const

242. [STYLE] Parameter 's' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/PollMultiplexor.cpp:52:40
   详细说明: Parameter 's' can be declared as pointer to const

243. [STYLE] Parameter 's' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/PollMultiplexor.cpp:61:40
   详细说明: Parameter 's' can be declared as pointer to const

244. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/Proxy.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

245. [WARNING] Member variable 'Proxy::mConf' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/Proxy.cpp:46:8
   详细说明: Member variable 'Proxy::mConf' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

246. [STYLE] Variable 'ac' can be declared as reference to const
   问题ID: constVariableReference
   位置: src/Proxy.cpp:92:16
   详细说明: Variable 'ac' can be declared as reference to const

247. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/Request.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

248. [STYLE] Variable 'r' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Request.cpp:94:10
   详细说明: Variable 'r' can be declared as pointer to const

249. [STYLE] Variable 'leaderRes' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Request.cpp:350:27
   详细说明: Variable 'leaderRes' can be declared as pointer to const

250. [STYLE] Variable 'leaderRes' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Request.cpp:359:27
   详细说明: Variable 'leaderRes' can be declared as pointer to const

251. [STYLE] Variable 'leaderRes' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Request.cpp:380:27
   详细说明: Variable 'leaderRes' can be declared as pointer to const

252. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/RequestParser.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

253. [WARNING] Member variable 'RequestParser::mCmd' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/RequestParser.cpp:9:16
   详细说明: Member variable 'RequestParser::mCmd' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

254. [WARNING] Member variable 'SString < 64 >::mBuf' is not initialized in the copy constructor.
   问题ID: uninitMemberVar
   位置: src/PString.h:185:5
   详细说明: Member variable 'SString < 64 >::mBuf' is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

255. [WARNING] Member variable 'SString < 16 >::mBuf' is not initialized in the copy constructor.
   问题ID: uninitMemberVar
   位置: src/PString.h:185:5
   详细说明: Member variable 'SString < 16 >::mBuf' is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

256. [STYLE] Class 'SString < 64 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:171:5
   详细说明: Class 'SString < 64 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

257. [STYLE] Class 'SString < 64 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:181:5
   详细说明: Class 'SString < 64 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

258. [STYLE] Class 'SString < 64 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:190:5
   详细说明: Class 'SString < 64 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

259. [STYLE] Class 'SString < 16 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:171:5
   详细说明: Class 'SString < 16 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

260. [STYLE] Class 'SString < 16 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:181:5
   详细说明: Class 'SString < 16 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

261. [STYLE] Class 'SString < 16 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:190:5
   详细说明: Class 'SString < 16 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

262. [STYLE] Variable 'cursor' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/RequestParser.cpp:74:11
   详细说明: Variable 'cursor' can be declared as pointer to const

263. [STYLE] Variable 'end' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/RequestParser.cpp:75:11
   详细说明: Variable 'end' can be declared as pointer to const

264. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/Response.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

265. [STYLE] Class 'SegmentStr < Const :: MaxAddrLen + 16 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Buffer.h:211:5
   详细说明: Class 'SegmentStr < Const :: MaxAddrLen + 16 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

266. [STYLE] Variable 'r' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Response.cpp:64:10
   详细说明: Variable 'r' can be declared as pointer to const

267. [STYLE] Parameter 'req' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/Response.cpp:107:41
   详细说明: Parameter 'req' can be declared as pointer to const

268. [STYLE] Variable 'leader' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Response.cpp:109:18
   详细说明: Variable 'leader' can be declared as pointer to const

269. [WARNING] Member variable 'ResponseParser::mArrayNum' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/ResponseParser.cpp:10:17
   详细说明: Member variable 'ResponseParser::mArrayNum' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

270. [WARNING] Member variable 'ResponseParser::mElementCnt' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/ResponseParser.cpp:10:17
   详细说明: Member variable 'ResponseParser::mElementCnt' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

271. [STYLE] Variable 'end' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/ResponseParser.cpp:35:11
   详细说明: Variable 'end' can be declared as pointer to const

272. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/ResponseParser.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

273. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/SentinelServerPool.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

274. [WARNING] Member variable 'SString < 4 >::mBuf' is not initialized in the copy constructor.
   问题ID: uninitMemberVar
   位置: src/PString.h:185:5
   详细说明: Member variable 'SString < 4 >::mBuf' is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

275. [WARNING] The class 'SentinelServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/SentinelServerPool.cpp:58:29
   位置: src/ServerPool.h:89:13
   详细说明: The class 'SentinelServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.

276. [WARNING] The class 'SentinelServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/SentinelServerPool.cpp:103:26
   位置: src/ServerPool.h:97:10
   详细说明: The class 'SentinelServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.

277. [WARNING] The class 'SentinelServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/SentinelServerPool.cpp:122:26
   位置: src/ServerPool.h:101:10
   详细说明: The class 'SentinelServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.

278. [STYLE] Class 'AddrParser' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/SentinelServerPool.cpp:148:5
   详细说明: Class 'AddrParser' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

279. [STYLE] Class 'SegmentStr < Const :: MaxAddrLen + 32 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/Buffer.h:211:5
   详细说明: Class 'SegmentStr < Const :: MaxAddrLen + 32 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

280. [STYLE] Class 'SString < 4 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:171:5
   详细说明: Class 'SString < 4 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

281. [STYLE] Class 'SString < 4 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:181:5
   详细说明: Class 'SString < 4 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

282. [STYLE] Class 'SString < 4 >' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/PString.h:190:5
   详细说明: Class 'SString < 4 >' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

283. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/SentinelServerPool.cpp:337:22
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

284. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/SentinelServerPool.cpp:411:22
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

285. [STYLE] Consecutive return, break, continue, goto or throw statements are unnecessary.
   问题ID: duplicateBreak
   位置: src/SentinelServerPool.cpp:89:13
   详细说明: Consecutive return, break, continue, goto or throw statements are unnecessary. The second statement can never be executed, and so should be removed.

286. [STYLE] Consecutive return, break, continue, goto or throw statements are unnecessary.
   问题ID: duplicateBreak
   位置: src/SentinelServerPool.cpp:95:13
   详细说明: Consecutive return, break, continue, goto or throw statements are unnecessary. The second statement can never be executed, and so should be removed.

287. [STYLE] Parameter 'h' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/SentinelServerPool.cpp:290:51
   详细说明: Parameter 'h' can be declared as pointer to const

288. [STYLE] Parameter 'req' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/SentinelServerPool.cpp:332:85
   详细说明: Parameter 'req' can be declared as pointer to const

289. [STYLE] Parameter 'req' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/SentinelServerPool.cpp:406:82
   详细说明: Parameter 'req' can be declared as pointer to const

290. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/Server.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

291. [STYLE] Variable 'dataCenter' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Server.cpp:31:14
   详细说明: Variable 'dataCenter' can be declared as pointer to const

292. [STYLE] The comparison 'mNextActivateTime == v' is always true because 'mNextActivateTime' and 'v' represent the same value.
   问题ID: knownConditionTrueFalse
   位置: src/Server.cpp:47:12
   位置: src/Server.cpp:42:14
   详细说明: Finding the same expression on both sides of an operator is suspicious and might indicate a cut and paste or logic error. Please examine this code carefully to determine if it is correct.

293. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/ServerGroup.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

294. [STYLE] Parameter 'req' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/ServerGroup.cpp:46:53
   详细说明: Parameter 'req' can be declared as pointer to const

295. [STYLE] Variable 'dataCenter' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/ServerGroup.cpp:62:21
   详细说明: Variable 'dataCenter' can be declared as pointer to const

296. [STYLE] Parameter 'localDC' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/ServerGroup.cpp:136:52
   详细说明: Parameter 'localDC' can be declared as pointer to const

297. [STYLE] Variable 'dc' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/ServerGroup.cpp:203:9
   详细说明: Variable 'dc' can be declared as pointer to const

298. [STYLE] Parameter 's' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/ServerGroup.cpp:298:34
   详细说明: Parameter 's' can be declared as pointer to const

299. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/ServerPool.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

300. [WARNING] The class 'ClusterServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/ClusterServerPool.h:31:10
   位置: src/ServerPool.cpp:39:18
   详细说明: The class 'ClusterServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.

301. [WARNING] The class 'StandaloneServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/StandaloneServerPool.h:28:10
   位置: src/ServerPool.cpp:39:18
   详细说明: The class 'StandaloneServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.

302. [STYLE] The comparison 'mLastRefreshTime == last' is always true because 'mLastRefreshTime' and 'last' represent the same value.
   问题ID: knownConditionTrueFalse
   位置: src/ServerPool.cpp:36:12
   位置: src/ServerPool.cpp:31:17
   详细说明: Finding the same expression on both sides of an operator is suspicious and might indicate a cut and paste or logic error. Please examine this code carefully to determine if it is correct.

303. [WARNING] Either the condition 'mStatus<CustomStatus' is redundant or the array 'strs[6]' is accessed at index 99, which is out of bounds.
   问题ID: arrayIndexOutOfBoundsCond
   位置: src/Socket.cpp:77:41
   位置: src/Socket.cpp:77:20
   详细说明: Either the condition 'mStatus<CustomStatus' is redundant or the array 'strs[6]' is accessed at index 99, which is out of bounds.

304. [WARNING] Member variable 'Socket::mEvts' is not initialized in the constructor.
   问题ID: uninitMemberVar
   位置: src/Socket.cpp:30:9
   详细说明: Member variable 'Socket::mEvts' is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior.

305. [STYLE] Local variable 'fd' shadows outer function
   问题ID: shadowFunction
   位置: src/Socket.cpp:82:9
   位置: src/Socket.h:70:9
   详细说明: Local variable 'fd' shadows outer function

306. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/StandaloneServerPool.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

307. [WARNING] The class 'StandaloneServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/StandaloneServerPool.cpp:72:31
   位置: src/ServerPool.h:89:13
   详细说明: The class 'StandaloneServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.

308. [WARNING] The class 'StandaloneServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/StandaloneServerPool.cpp:117:28
   位置: src/ServerPool.h:97:10
   详细说明: The class 'StandaloneServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.

309. [WARNING] The class 'StandaloneServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   问题ID: duplInheritedMember
   位置: src/StandaloneServerPool.cpp:142:28
   位置: src/ServerPool.h:101:10
   详细说明: The class 'StandaloneServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.

310. [STYLE] Class 'AddrParser' has a constructor with 1 argument that is not explicit.
   问题ID: noExplicitConstructor
   位置: src/StandaloneServerPool.cpp:168:5
   详细说明: Class 'AddrParser' has a constructor with 1 argument that is not explicit. Such, so called "Converting constructors", should in general be explicit for type safety reasons as that prevents unintended implicit conversions.

311. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/StandaloneServerPool.cpp:357:22
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

312. [STYLE] C-style pointer casting
   问题ID: cstyleCast
   位置: src/StandaloneServerPool.cpp:431:22
   详细说明: C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected.

313. [STYLE] Consecutive return, break, continue, goto or throw statements are unnecessary.
   问题ID: duplicateBreak
   位置: src/StandaloneServerPool.cpp:103:13
   详细说明: Consecutive return, break, continue, goto or throw statements are unnecessary. The second statement can never be executed, and so should be removed.

314. [STYLE] Consecutive return, break, continue, goto or throw statements are unnecessary.
   问题ID: duplicateBreak
   位置: src/StandaloneServerPool.cpp:109:13
   详细说明: Consecutive return, break, continue, goto or throw statements are unnecessary. The second statement can never be executed, and so should be removed.

315. [STYLE] Variable 'sc' can be declared as reference to const
   问题ID: constVariableReference
   位置: src/StandaloneServerPool.cpp:37:20
   详细说明: Variable 'sc' can be declared as reference to const

316. [STYLE] Variable 'gc' can be declared as reference to const
   问题ID: constVariableReference
   位置: src/StandaloneServerPool.cpp:47:16
   详细说明: Variable 'gc' can be declared as reference to const

317. [STYLE] Variable 'sc' can be declared as reference to const
   问题ID: constVariableReference
   位置: src/StandaloneServerPool.cpp:51:20
   详细说明: Variable 'sc' can be declared as reference to const

318. [STYLE] Parameter 'h' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/StandaloneServerPool.cpp:310:53
   详细说明: Parameter 'h' can be declared as pointer to const

319. [STYLE] Parameter 'req' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/StandaloneServerPool.cpp:352:87
   详细说明: Parameter 'req' can be declared as pointer to const

320. [STYLE] Parameter 'req' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/StandaloneServerPool.cpp:426:84
   详细说明: Parameter 'req' can be declared as pointer to const

321. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/Subscribe.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

322. [STYLE] Parameter 'p1' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/Timer.cpp:49:28
   详细说明: Parameter 'p1' can be declared as pointer to const

323. [STYLE] Parameter 'p2' can be declared as pointer to const
   问题ID: constParameterPointer
   位置: src/Timer.cpp:49:44
   详细说明: Parameter 'p2' can be declared as pointer to const

324. [STYLE] Variable 'p' can be declared as pointer to const
   问题ID: constVariablePointer
   位置: src/Timer.cpp:53:14
   详细说明: Variable 'p' can be declared as pointer to const

325. [STYLE] Variable 'lck' is assigned a value that is never used.
   问题ID: unreadVariable
   位置: src/Timer.cpp:23:26
   详细说明: Variable 'lck' is assigned a value that is never used.

326. [INFORMATION] Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.
   问题ID: normalCheckLevelMaxBranches
   位置: src/main.cpp:0:0
   详细说明: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches.

327. [INFORMATION] Active checkers: 167/856 (use --checkers-report=<filename> to see details)
   问题ID: checkersReport
   详细说明: Active checkers: 167/856 (use --checkers-report=<filename> to see details)

💡 修复建议
---------------
总计建议: 61 条

1. Member variable 'ServerPool::mServerTimeout' is not initialized in the constructor.
   位置: src/ServerPool.h:108
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

2. Member variable 'ServerPool::mKeepAlive' is not initialized in the constructor.
   位置: src/ServerPool.h:108
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

3. Member variable 'SString < Const :: MaxAddrLen >::mBuf' is not initialized in the copy constructor.
   位置: src/PString.h:185
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

4. Member variable 'SString < Const :: MaxServNameLen >::mBuf' is not initialized in the copy constructor.
   位置: src/PString.h:185
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

5. The class 'ConnectSocket' defines member function with name 'close' also defined in its parent class 'Socket'.
   位置: src/ConnectSocket.h:27
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

6. The class 'AcceptConnection' defines member function with name 'close' also defined in its parent class 'Socket'.
   位置: src/AcceptConnection.cpp:27
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

7. The class 'ClusterServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.
   位置: src/ClusterServerPool.h:29
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

8. The class 'ClusterServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.
   位置: src/ClusterServerPool.h:30
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

9. The class 'ClusterServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   位置: src/ClusterServerPool.h:31
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

10. The class 'ClusterServerPool' defines member function with name 'iter' also defined in its parent class 'ServerPool'.
   位置: src/ClusterServerPool.h:37
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

11. The class 'StandaloneServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.
   位置: src/StandaloneServerPool.h:22
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

12. The class 'StandaloneServerPool' defines member function with name 'iter' also defined in its parent class 'ServerPool'.
   位置: src/StandaloneServerPool.h:23
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

13. The class 'StandaloneServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.
   位置: src/StandaloneServerPool.h:27
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

14. The class 'StandaloneServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   位置: src/StandaloneServerPool.h:28
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

15. Member variable 'SString::mBuf' is not initialized in the copy constructor.
   位置: src/PString.h:185
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

16. Class 'Auth' does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s).
   位置: src/Auth.cpp:27
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: noCopyConstructor

17. Class 'Auth' does not have a operator= which is recommended since it has dynamic memory/resource allocation(s).
   位置: src/Auth.cpp:27
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: noOperatorEq

18. Memory leak: kp
   位置: src/Auth.cpp:40
   严重性: error
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: memleak

19. Member variable 'Buffer::mDat' is not initialized in the constructor.
   位置: src/Buffer.cpp:14
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVarPrivate

20. Member variable 'ClusterNodesParser::mRole' is not initialized in the constructor.
   位置: src/ClusterNodesParser.cpp:11
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

21. Member variable 'ClusterNodesParser::mFieldCnt' is not initialized in the constructor.
   位置: src/ClusterNodesParser.cpp:11
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

22. Member variable 'ClusterNodesParser::mSlotBegin' is not initialized in the constructor.
   位置: src/ClusterNodesParser.cpp:11
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

23. Member variable 'ClusterNodesParser::mSlotEnd' is not initialized in the constructor.
   位置: src/ClusterNodesParser.cpp:11
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

24. Member variable 'SString < NodeIdLen >::mBuf' is not initialized in the copy constructor.
   位置: src/PString.h:185
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

25. Member variable 'SString < AddrLen >::mBuf' is not initialized in the copy constructor.
   位置: src/PString.h:185
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

26. Member variable 'SString < FlagsLen >::mBuf' is not initialized in the copy constructor.
   位置: src/PString.h:185
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

27. The class 'AcceptConnection' defines member function with name 'close' also defined in its parent class 'Socket'.
   位置: src/AcceptConnection.h:36
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

28. The class 'ClusterServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.
   位置: src/ClusterServerPool.cpp:36
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

29. The class 'ClusterServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.
   位置: src/ClusterServerPool.cpp:82
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

30. The class 'ClusterServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   位置: src/ClusterServerPool.cpp:89
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

31. Member variable 'Conf::mStandaloneServerPool' is not initialized in the constructor.
   位置: src/Conf.cpp:43
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

32. The class 'ConnectSocket' defines member function with name 'close' also defined in its parent class 'Socket'.
   位置: src/ConnectSocket.cpp:62
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

33. Member variable 'SString < Const :: MaxKeyLen >::mBuf' is not initialized in the copy constructor.
   位置: src/PString.h:185
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

34. Member variable 'SString < 32 >::mBuf' is not initialized in the copy constructor.
   位置: src/PString.h:185
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

35. Member variable 'SString < 512 >::mBuf' is not initialized in the copy constructor.
   位置: src/PString.h:185
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

36. Class 'Handler' does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s).
   位置: src/Handler.cpp:27
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: noCopyConstructor

37. Class 'Handler' does not have a operator= which is recommended since it has dynamic memory/resource allocation(s).
   位置: src/Handler.cpp:27
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: noOperatorEq

38. The class 'SentinelServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.
   位置: src/SentinelServerPool.h:22
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

39. The class 'SentinelServerPool' defines member function with name 'iter' also defined in its parent class 'ServerPool'.
   位置: src/SentinelServerPool.h:23
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

40. The class 'SentinelServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.
   位置: src/SentinelServerPool.h:27
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

41. The class 'SentinelServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   位置: src/SentinelServerPool.h:28
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

42. Member variable 'LogFileSink::mFilePath' is not initialized in the constructor.
   位置: src/LogFileSink.cpp:18
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

43. Member variable 'LogUnit::mBuf' is not initialized in the constructor.
   位置: src/Logger.cpp:26
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

44. Member variable 'Logger::mThread' is not initialized in the constructor.
   位置: src/Logger.cpp:72
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

45. Member variable 'Proxy::mConf' is not initialized in the constructor.
   位置: src/Proxy.cpp:46
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

46. Member variable 'RequestParser::mCmd' is not initialized in the constructor.
   位置: src/RequestParser.cpp:9
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

47. Member variable 'SString < 64 >::mBuf' is not initialized in the copy constructor.
   位置: src/PString.h:185
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

48. Member variable 'SString < 16 >::mBuf' is not initialized in the copy constructor.
   位置: src/PString.h:185
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

49. Member variable 'ResponseParser::mArrayNum' is not initialized in the constructor.
   位置: src/ResponseParser.cpp:10
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

50. Member variable 'ResponseParser::mElementCnt' is not initialized in the constructor.
   位置: src/ResponseParser.cpp:10
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

51. Member variable 'SString < 4 >::mBuf' is not initialized in the copy constructor.
   位置: src/PString.h:185
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

52. The class 'SentinelServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.
   位置: src/SentinelServerPool.cpp:58
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

53. The class 'SentinelServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.
   位置: src/SentinelServerPool.cpp:103
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

54. The class 'SentinelServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   位置: src/SentinelServerPool.cpp:122
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

55. The class 'ClusterServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   位置: src/ClusterServerPool.h:31
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

56. The class 'StandaloneServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   位置: src/StandaloneServerPool.h:28
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

57. Either the condition 'mStatus<CustomStatus' is redundant or the array 'strs[6]' is accessed at index 99, which is out of bounds.
   位置: src/Socket.cpp:77
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: arrayIndexOutOfBoundsCond

58. Member variable 'Socket::mEvts' is not initialized in the constructor.
   位置: src/Socket.cpp:30
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: uninitMemberVar

59. The class 'StandaloneServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'.
   位置: src/StandaloneServerPool.cpp:72
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

60. The class 'StandaloneServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'.
   位置: src/StandaloneServerPool.cpp:117
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

61. The class 'StandaloneServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'.
   位置: src/StandaloneServerPool.cpp:142
   严重性: warning
   建议: 请查看CppCheck文档了解详细修复方法
   问题ID: duplInheritedMember

📦 检测到的依赖库版本
-------------------------
总计检测到: 1 个依赖库

📚 openssl:
   版本: 3.4.0
   检测方式: system_command
   详细信息: OpenSSL 3.4.0 22 Oct 2024 (Library: OpenSSL 3.4.0 22 Oct 2024)

🔍 依赖库CVE检查结果
-------------------------
未发现版本相关的依赖库CVE

📊 扫描总结
---------------
CVE搜索结果: 10 个
CppCheck问题: 327 个
依赖库CVE: 0 个
修复建议: 61 条

风险评估:
⚠️ 发现 63 个高风险问题，建议优先处理

============================================================
报告生成完成
