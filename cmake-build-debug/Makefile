# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Default target executed when no arguments are given to make.
default_target: all

.PHONY : default_target

# Allow only one "make -f Makefile2" at a time, but pass parallelism.
.NOTPARALLEL:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/Cellar/cmake/3.18.2/bin/cmake

# The command to remove a file.
RM = /usr/local/Cellar/cmake/3.18.2/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/predixy

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/predixy/cmake-build-debug

#=============================================================================
# Targets provided globally by CMake.

# Special rule for the target rebuild_cache
rebuild_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "Running CMake to regenerate build system..."
	/usr/local/Cellar/cmake/3.18.2/bin/cmake --regenerate-during-build -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR)
.PHONY : rebuild_cache

# Special rule for the target rebuild_cache
rebuild_cache/fast: rebuild_cache

.PHONY : rebuild_cache/fast

# Special rule for the target edit_cache
edit_cache:
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --cyan "No interactive CMake dialog available..."
	/usr/local/Cellar/cmake/3.18.2/bin/cmake -E echo No\ interactive\ CMake\ dialog\ available.
.PHONY : edit_cache

# Special rule for the target edit_cache
edit_cache/fast: edit_cache

.PHONY : edit_cache/fast

# The main all target
all: cmake_check_build_system
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/predixy/cmake-build-debug/CMakeFiles /Users/<USER>/CLionProjects/predixy/cmake-build-debug//CMakeFiles/progress.marks
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 all
	$(CMAKE_COMMAND) -E cmake_progress_start /Users/<USER>/CLionProjects/predixy/cmake-build-debug/CMakeFiles 0
.PHONY : all

# The main clean target
clean:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 clean
.PHONY : clean

# The main clean target
clean/fast: clean

.PHONY : clean/fast

# Prepare targets for installation.
preinstall: all
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall

# Prepare targets for installation.
preinstall/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 preinstall
.PHONY : preinstall/fast

# clear depends
depend:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 1
.PHONY : depend

#=============================================================================
# Target rules for targets named predixy

# Build rule for target.
predixy: cmake_check_build_system
	$(MAKE) $(MAKESILENT) -f CMakeFiles/Makefile2 predixy
.PHONY : predixy

# fast build rule for target.
predixy/fast:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/build
.PHONY : predixy/fast

src/AcceptConnection.o: src/AcceptConnection.cpp.o

.PHONY : src/AcceptConnection.o

# target to build an object file
src/AcceptConnection.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/AcceptConnection.cpp.o
.PHONY : src/AcceptConnection.cpp.o

src/AcceptConnection.i: src/AcceptConnection.cpp.i

.PHONY : src/AcceptConnection.i

# target to preprocess a source file
src/AcceptConnection.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/AcceptConnection.cpp.i
.PHONY : src/AcceptConnection.cpp.i

src/AcceptConnection.s: src/AcceptConnection.cpp.s

.PHONY : src/AcceptConnection.s

# target to generate assembly for a file
src/AcceptConnection.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/AcceptConnection.cpp.s
.PHONY : src/AcceptConnection.cpp.s

src/AcceptSocket.o: src/AcceptSocket.cpp.o

.PHONY : src/AcceptSocket.o

# target to build an object file
src/AcceptSocket.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/AcceptSocket.cpp.o
.PHONY : src/AcceptSocket.cpp.o

src/AcceptSocket.i: src/AcceptSocket.cpp.i

.PHONY : src/AcceptSocket.i

# target to preprocess a source file
src/AcceptSocket.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/AcceptSocket.cpp.i
.PHONY : src/AcceptSocket.cpp.i

src/AcceptSocket.s: src/AcceptSocket.cpp.s

.PHONY : src/AcceptSocket.s

# target to generate assembly for a file
src/AcceptSocket.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/AcceptSocket.cpp.s
.PHONY : src/AcceptSocket.cpp.s

src/Alloc.o: src/Alloc.cpp.o

.PHONY : src/Alloc.o

# target to build an object file
src/Alloc.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Alloc.cpp.o
.PHONY : src/Alloc.cpp.o

src/Alloc.i: src/Alloc.cpp.i

.PHONY : src/Alloc.i

# target to preprocess a source file
src/Alloc.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Alloc.cpp.i
.PHONY : src/Alloc.cpp.i

src/Alloc.s: src/Alloc.cpp.s

.PHONY : src/Alloc.s

# target to generate assembly for a file
src/Alloc.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Alloc.cpp.s
.PHONY : src/Alloc.cpp.s

src/Auth.o: src/Auth.cpp.o

.PHONY : src/Auth.o

# target to build an object file
src/Auth.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Auth.cpp.o
.PHONY : src/Auth.cpp.o

src/Auth.i: src/Auth.cpp.i

.PHONY : src/Auth.i

# target to preprocess a source file
src/Auth.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Auth.cpp.i
.PHONY : src/Auth.cpp.i

src/Auth.s: src/Auth.cpp.s

.PHONY : src/Auth.s

# target to generate assembly for a file
src/Auth.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Auth.cpp.s
.PHONY : src/Auth.cpp.s

src/Buffer.o: src/Buffer.cpp.o

.PHONY : src/Buffer.o

# target to build an object file
src/Buffer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Buffer.cpp.o
.PHONY : src/Buffer.cpp.o

src/Buffer.i: src/Buffer.cpp.i

.PHONY : src/Buffer.i

# target to preprocess a source file
src/Buffer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Buffer.cpp.i
.PHONY : src/Buffer.cpp.i

src/Buffer.s: src/Buffer.cpp.s

.PHONY : src/Buffer.s

# target to generate assembly for a file
src/Buffer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Buffer.cpp.s
.PHONY : src/Buffer.cpp.s

src/ClusterNodesParser.o: src/ClusterNodesParser.cpp.o

.PHONY : src/ClusterNodesParser.o

# target to build an object file
src/ClusterNodesParser.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.o
.PHONY : src/ClusterNodesParser.cpp.o

src/ClusterNodesParser.i: src/ClusterNodesParser.cpp.i

.PHONY : src/ClusterNodesParser.i

# target to preprocess a source file
src/ClusterNodesParser.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.i
.PHONY : src/ClusterNodesParser.cpp.i

src/ClusterNodesParser.s: src/ClusterNodesParser.cpp.s

.PHONY : src/ClusterNodesParser.s

# target to generate assembly for a file
src/ClusterNodesParser.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.s
.PHONY : src/ClusterNodesParser.cpp.s

src/ClusterServerPool.o: src/ClusterServerPool.cpp.o

.PHONY : src/ClusterServerPool.o

# target to build an object file
src/ClusterServerPool.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.o
.PHONY : src/ClusterServerPool.cpp.o

src/ClusterServerPool.i: src/ClusterServerPool.cpp.i

.PHONY : src/ClusterServerPool.i

# target to preprocess a source file
src/ClusterServerPool.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.i
.PHONY : src/ClusterServerPool.cpp.i

src/ClusterServerPool.s: src/ClusterServerPool.cpp.s

.PHONY : src/ClusterServerPool.s

# target to generate assembly for a file
src/ClusterServerPool.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.s
.PHONY : src/ClusterServerPool.cpp.s

src/Command.o: src/Command.cpp.o

.PHONY : src/Command.o

# target to build an object file
src/Command.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Command.cpp.o
.PHONY : src/Command.cpp.o

src/Command.i: src/Command.cpp.i

.PHONY : src/Command.i

# target to preprocess a source file
src/Command.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Command.cpp.i
.PHONY : src/Command.cpp.i

src/Command.s: src/Command.cpp.s

.PHONY : src/Command.s

# target to generate assembly for a file
src/Command.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Command.cpp.s
.PHONY : src/Command.cpp.s

src/Conf.o: src/Conf.cpp.o

.PHONY : src/Conf.o

# target to build an object file
src/Conf.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Conf.cpp.o
.PHONY : src/Conf.cpp.o

src/Conf.i: src/Conf.cpp.i

.PHONY : src/Conf.i

# target to preprocess a source file
src/Conf.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Conf.cpp.i
.PHONY : src/Conf.cpp.i

src/Conf.s: src/Conf.cpp.s

.PHONY : src/Conf.s

# target to generate assembly for a file
src/Conf.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Conf.cpp.s
.PHONY : src/Conf.cpp.s

src/ConfParser.o: src/ConfParser.cpp.o

.PHONY : src/ConfParser.o

# target to build an object file
src/ConfParser.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ConfParser.cpp.o
.PHONY : src/ConfParser.cpp.o

src/ConfParser.i: src/ConfParser.cpp.i

.PHONY : src/ConfParser.i

# target to preprocess a source file
src/ConfParser.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ConfParser.cpp.i
.PHONY : src/ConfParser.cpp.i

src/ConfParser.s: src/ConfParser.cpp.s

.PHONY : src/ConfParser.s

# target to generate assembly for a file
src/ConfParser.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ConfParser.cpp.s
.PHONY : src/ConfParser.cpp.s

src/ConnectConnection.o: src/ConnectConnection.cpp.o

.PHONY : src/ConnectConnection.o

# target to build an object file
src/ConnectConnection.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ConnectConnection.cpp.o
.PHONY : src/ConnectConnection.cpp.o

src/ConnectConnection.i: src/ConnectConnection.cpp.i

.PHONY : src/ConnectConnection.i

# target to preprocess a source file
src/ConnectConnection.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ConnectConnection.cpp.i
.PHONY : src/ConnectConnection.cpp.i

src/ConnectConnection.s: src/ConnectConnection.cpp.s

.PHONY : src/ConnectConnection.s

# target to generate assembly for a file
src/ConnectConnection.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ConnectConnection.cpp.s
.PHONY : src/ConnectConnection.cpp.s

src/ConnectConnectionPool.o: src/ConnectConnectionPool.cpp.o

.PHONY : src/ConnectConnectionPool.o

# target to build an object file
src/ConnectConnectionPool.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.o
.PHONY : src/ConnectConnectionPool.cpp.o

src/ConnectConnectionPool.i: src/ConnectConnectionPool.cpp.i

.PHONY : src/ConnectConnectionPool.i

# target to preprocess a source file
src/ConnectConnectionPool.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.i
.PHONY : src/ConnectConnectionPool.cpp.i

src/ConnectConnectionPool.s: src/ConnectConnectionPool.cpp.s

.PHONY : src/ConnectConnectionPool.s

# target to generate assembly for a file
src/ConnectConnectionPool.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.s
.PHONY : src/ConnectConnectionPool.cpp.s

src/ConnectSocket.o: src/ConnectSocket.cpp.o

.PHONY : src/ConnectSocket.o

# target to build an object file
src/ConnectSocket.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ConnectSocket.cpp.o
.PHONY : src/ConnectSocket.cpp.o

src/ConnectSocket.i: src/ConnectSocket.cpp.i

.PHONY : src/ConnectSocket.i

# target to preprocess a source file
src/ConnectSocket.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ConnectSocket.cpp.i
.PHONY : src/ConnectSocket.cpp.i

src/ConnectSocket.s: src/ConnectSocket.cpp.s

.PHONY : src/ConnectSocket.s

# target to generate assembly for a file
src/ConnectSocket.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ConnectSocket.cpp.s
.PHONY : src/ConnectSocket.cpp.s

src/Connection.o: src/Connection.cpp.o

.PHONY : src/Connection.o

# target to build an object file
src/Connection.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Connection.cpp.o
.PHONY : src/Connection.cpp.o

src/Connection.i: src/Connection.cpp.i

.PHONY : src/Connection.i

# target to preprocess a source file
src/Connection.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Connection.cpp.i
.PHONY : src/Connection.cpp.i

src/Connection.s: src/Connection.cpp.s

.PHONY : src/Connection.s

# target to generate assembly for a file
src/Connection.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Connection.cpp.s
.PHONY : src/Connection.cpp.s

src/Crc16.o: src/Crc16.cpp.o

.PHONY : src/Crc16.o

# target to build an object file
src/Crc16.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Crc16.cpp.o
.PHONY : src/Crc16.cpp.o

src/Crc16.i: src/Crc16.cpp.i

.PHONY : src/Crc16.i

# target to preprocess a source file
src/Crc16.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Crc16.cpp.i
.PHONY : src/Crc16.cpp.i

src/Crc16.s: src/Crc16.cpp.s

.PHONY : src/Crc16.s

# target to generate assembly for a file
src/Crc16.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Crc16.cpp.s
.PHONY : src/Crc16.cpp.s

src/DC.o: src/DC.cpp.o

.PHONY : src/DC.o

# target to build an object file
src/DC.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/DC.cpp.o
.PHONY : src/DC.cpp.o

src/DC.i: src/DC.cpp.i

.PHONY : src/DC.i

# target to preprocess a source file
src/DC.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/DC.cpp.i
.PHONY : src/DC.cpp.i

src/DC.s: src/DC.cpp.s

.PHONY : src/DC.s

# target to generate assembly for a file
src/DC.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/DC.cpp.s
.PHONY : src/DC.cpp.s

src/Distribution.o: src/Distribution.cpp.o

.PHONY : src/Distribution.o

# target to build an object file
src/Distribution.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Distribution.cpp.o
.PHONY : src/Distribution.cpp.o

src/Distribution.i: src/Distribution.cpp.i

.PHONY : src/Distribution.i

# target to preprocess a source file
src/Distribution.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Distribution.cpp.i
.PHONY : src/Distribution.cpp.i

src/Distribution.s: src/Distribution.cpp.s

.PHONY : src/Distribution.s

# target to generate assembly for a file
src/Distribution.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Distribution.cpp.s
.PHONY : src/Distribution.cpp.s

src/Enums.o: src/Enums.cpp.o

.PHONY : src/Enums.o

# target to build an object file
src/Enums.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Enums.cpp.o
.PHONY : src/Enums.cpp.o

src/Enums.i: src/Enums.cpp.i

.PHONY : src/Enums.i

# target to preprocess a source file
src/Enums.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Enums.cpp.i
.PHONY : src/Enums.cpp.i

src/Enums.s: src/Enums.cpp.s

.PHONY : src/Enums.s

# target to generate assembly for a file
src/Enums.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Enums.cpp.s
.PHONY : src/Enums.cpp.s

src/Handler.o: src/Handler.cpp.o

.PHONY : src/Handler.o

# target to build an object file
src/Handler.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Handler.cpp.o
.PHONY : src/Handler.cpp.o

src/Handler.i: src/Handler.cpp.i

.PHONY : src/Handler.i

# target to preprocess a source file
src/Handler.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Handler.cpp.i
.PHONY : src/Handler.cpp.i

src/Handler.s: src/Handler.cpp.s

.PHONY : src/Handler.s

# target to generate assembly for a file
src/Handler.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Handler.cpp.s
.PHONY : src/Handler.cpp.s

src/HashFunc.o: src/HashFunc.cpp.o

.PHONY : src/HashFunc.o

# target to build an object file
src/HashFunc.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/HashFunc.cpp.o
.PHONY : src/HashFunc.cpp.o

src/HashFunc.i: src/HashFunc.cpp.i

.PHONY : src/HashFunc.i

# target to preprocess a source file
src/HashFunc.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/HashFunc.cpp.i
.PHONY : src/HashFunc.cpp.i

src/HashFunc.s: src/HashFunc.cpp.s

.PHONY : src/HashFunc.s

# target to generate assembly for a file
src/HashFunc.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/HashFunc.cpp.s
.PHONY : src/HashFunc.cpp.s

src/KqueueMultiplexor.o: src/KqueueMultiplexor.cpp.o

.PHONY : src/KqueueMultiplexor.o

# target to build an object file
src/KqueueMultiplexor.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.o
.PHONY : src/KqueueMultiplexor.cpp.o

src/KqueueMultiplexor.i: src/KqueueMultiplexor.cpp.i

.PHONY : src/KqueueMultiplexor.i

# target to preprocess a source file
src/KqueueMultiplexor.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.i
.PHONY : src/KqueueMultiplexor.cpp.i

src/KqueueMultiplexor.s: src/KqueueMultiplexor.cpp.s

.PHONY : src/KqueueMultiplexor.s

# target to generate assembly for a file
src/KqueueMultiplexor.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.s
.PHONY : src/KqueueMultiplexor.cpp.s

src/LatencyMonitor.o: src/LatencyMonitor.cpp.o

.PHONY : src/LatencyMonitor.o

# target to build an object file
src/LatencyMonitor.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.o
.PHONY : src/LatencyMonitor.cpp.o

src/LatencyMonitor.i: src/LatencyMonitor.cpp.i

.PHONY : src/LatencyMonitor.i

# target to preprocess a source file
src/LatencyMonitor.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.i
.PHONY : src/LatencyMonitor.cpp.i

src/LatencyMonitor.s: src/LatencyMonitor.cpp.s

.PHONY : src/LatencyMonitor.s

# target to generate assembly for a file
src/LatencyMonitor.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.s
.PHONY : src/LatencyMonitor.cpp.s

src/ListenSocket.o: src/ListenSocket.cpp.o

.PHONY : src/ListenSocket.o

# target to build an object file
src/ListenSocket.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ListenSocket.cpp.o
.PHONY : src/ListenSocket.cpp.o

src/ListenSocket.i: src/ListenSocket.cpp.i

.PHONY : src/ListenSocket.i

# target to preprocess a source file
src/ListenSocket.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ListenSocket.cpp.i
.PHONY : src/ListenSocket.cpp.i

src/ListenSocket.s: src/ListenSocket.cpp.s

.PHONY : src/ListenSocket.s

# target to generate assembly for a file
src/ListenSocket.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ListenSocket.cpp.s
.PHONY : src/ListenSocket.cpp.s

src/LogFileSink.o: src/LogFileSink.cpp.o

.PHONY : src/LogFileSink.o

# target to build an object file
src/LogFileSink.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/LogFileSink.cpp.o
.PHONY : src/LogFileSink.cpp.o

src/LogFileSink.i: src/LogFileSink.cpp.i

.PHONY : src/LogFileSink.i

# target to preprocess a source file
src/LogFileSink.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/LogFileSink.cpp.i
.PHONY : src/LogFileSink.cpp.i

src/LogFileSink.s: src/LogFileSink.cpp.s

.PHONY : src/LogFileSink.s

# target to generate assembly for a file
src/LogFileSink.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/LogFileSink.cpp.s
.PHONY : src/LogFileSink.cpp.s

src/Logger.o: src/Logger.cpp.o

.PHONY : src/Logger.o

# target to build an object file
src/Logger.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Logger.cpp.o
.PHONY : src/Logger.cpp.o

src/Logger.i: src/Logger.cpp.i

.PHONY : src/Logger.i

# target to preprocess a source file
src/Logger.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Logger.cpp.i
.PHONY : src/Logger.cpp.i

src/Logger.s: src/Logger.cpp.s

.PHONY : src/Logger.s

# target to generate assembly for a file
src/Logger.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Logger.cpp.s
.PHONY : src/Logger.cpp.s

src/Proxy.o: src/Proxy.cpp.o

.PHONY : src/Proxy.o

# target to build an object file
src/Proxy.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Proxy.cpp.o
.PHONY : src/Proxy.cpp.o

src/Proxy.i: src/Proxy.cpp.i

.PHONY : src/Proxy.i

# target to preprocess a source file
src/Proxy.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Proxy.cpp.i
.PHONY : src/Proxy.cpp.i

src/Proxy.s: src/Proxy.cpp.s

.PHONY : src/Proxy.s

# target to generate assembly for a file
src/Proxy.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Proxy.cpp.s
.PHONY : src/Proxy.cpp.s

src/Reply.o: src/Reply.cpp.o

.PHONY : src/Reply.o

# target to build an object file
src/Reply.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Reply.cpp.o
.PHONY : src/Reply.cpp.o

src/Reply.i: src/Reply.cpp.i

.PHONY : src/Reply.i

# target to preprocess a source file
src/Reply.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Reply.cpp.i
.PHONY : src/Reply.cpp.i

src/Reply.s: src/Reply.cpp.s

.PHONY : src/Reply.s

# target to generate assembly for a file
src/Reply.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Reply.cpp.s
.PHONY : src/Reply.cpp.s

src/Request.o: src/Request.cpp.o

.PHONY : src/Request.o

# target to build an object file
src/Request.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Request.cpp.o
.PHONY : src/Request.cpp.o

src/Request.i: src/Request.cpp.i

.PHONY : src/Request.i

# target to preprocess a source file
src/Request.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Request.cpp.i
.PHONY : src/Request.cpp.i

src/Request.s: src/Request.cpp.s

.PHONY : src/Request.s

# target to generate assembly for a file
src/Request.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Request.cpp.s
.PHONY : src/Request.cpp.s

src/RequestParser.o: src/RequestParser.cpp.o

.PHONY : src/RequestParser.o

# target to build an object file
src/RequestParser.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/RequestParser.cpp.o
.PHONY : src/RequestParser.cpp.o

src/RequestParser.i: src/RequestParser.cpp.i

.PHONY : src/RequestParser.i

# target to preprocess a source file
src/RequestParser.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/RequestParser.cpp.i
.PHONY : src/RequestParser.cpp.i

src/RequestParser.s: src/RequestParser.cpp.s

.PHONY : src/RequestParser.s

# target to generate assembly for a file
src/RequestParser.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/RequestParser.cpp.s
.PHONY : src/RequestParser.cpp.s

src/Response.o: src/Response.cpp.o

.PHONY : src/Response.o

# target to build an object file
src/Response.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Response.cpp.o
.PHONY : src/Response.cpp.o

src/Response.i: src/Response.cpp.i

.PHONY : src/Response.i

# target to preprocess a source file
src/Response.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Response.cpp.i
.PHONY : src/Response.cpp.i

src/Response.s: src/Response.cpp.s

.PHONY : src/Response.s

# target to generate assembly for a file
src/Response.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Response.cpp.s
.PHONY : src/Response.cpp.s

src/ResponseParser.o: src/ResponseParser.cpp.o

.PHONY : src/ResponseParser.o

# target to build an object file
src/ResponseParser.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ResponseParser.cpp.o
.PHONY : src/ResponseParser.cpp.o

src/ResponseParser.i: src/ResponseParser.cpp.i

.PHONY : src/ResponseParser.i

# target to preprocess a source file
src/ResponseParser.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ResponseParser.cpp.i
.PHONY : src/ResponseParser.cpp.i

src/ResponseParser.s: src/ResponseParser.cpp.s

.PHONY : src/ResponseParser.s

# target to generate assembly for a file
src/ResponseParser.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ResponseParser.cpp.s
.PHONY : src/ResponseParser.cpp.s

src/Server.o: src/Server.cpp.o

.PHONY : src/Server.o

# target to build an object file
src/Server.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Server.cpp.o
.PHONY : src/Server.cpp.o

src/Server.i: src/Server.cpp.i

.PHONY : src/Server.i

# target to preprocess a source file
src/Server.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Server.cpp.i
.PHONY : src/Server.cpp.i

src/Server.s: src/Server.cpp.s

.PHONY : src/Server.s

# target to generate assembly for a file
src/Server.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Server.cpp.s
.PHONY : src/Server.cpp.s

src/ServerGroup.o: src/ServerGroup.cpp.o

.PHONY : src/ServerGroup.o

# target to build an object file
src/ServerGroup.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ServerGroup.cpp.o
.PHONY : src/ServerGroup.cpp.o

src/ServerGroup.i: src/ServerGroup.cpp.i

.PHONY : src/ServerGroup.i

# target to preprocess a source file
src/ServerGroup.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ServerGroup.cpp.i
.PHONY : src/ServerGroup.cpp.i

src/ServerGroup.s: src/ServerGroup.cpp.s

.PHONY : src/ServerGroup.s

# target to generate assembly for a file
src/ServerGroup.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ServerGroup.cpp.s
.PHONY : src/ServerGroup.cpp.s

src/ServerPool.o: src/ServerPool.cpp.o

.PHONY : src/ServerPool.o

# target to build an object file
src/ServerPool.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ServerPool.cpp.o
.PHONY : src/ServerPool.cpp.o

src/ServerPool.i: src/ServerPool.cpp.i

.PHONY : src/ServerPool.i

# target to preprocess a source file
src/ServerPool.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ServerPool.cpp.i
.PHONY : src/ServerPool.cpp.i

src/ServerPool.s: src/ServerPool.cpp.s

.PHONY : src/ServerPool.s

# target to generate assembly for a file
src/ServerPool.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/ServerPool.cpp.s
.PHONY : src/ServerPool.cpp.s

src/Socket.o: src/Socket.cpp.o

.PHONY : src/Socket.o

# target to build an object file
src/Socket.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Socket.cpp.o
.PHONY : src/Socket.cpp.o

src/Socket.i: src/Socket.cpp.i

.PHONY : src/Socket.i

# target to preprocess a source file
src/Socket.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Socket.cpp.i
.PHONY : src/Socket.cpp.i

src/Socket.s: src/Socket.cpp.s

.PHONY : src/Socket.s

# target to generate assembly for a file
src/Socket.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Socket.cpp.s
.PHONY : src/Socket.cpp.s

src/StandaloneServerPool.o: src/StandaloneServerPool.cpp.o

.PHONY : src/StandaloneServerPool.o

# target to build an object file
src/StandaloneServerPool.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.o
.PHONY : src/StandaloneServerPool.cpp.o

src/StandaloneServerPool.i: src/StandaloneServerPool.cpp.i

.PHONY : src/StandaloneServerPool.i

# target to preprocess a source file
src/StandaloneServerPool.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.i
.PHONY : src/StandaloneServerPool.cpp.i

src/StandaloneServerPool.s: src/StandaloneServerPool.cpp.s

.PHONY : src/StandaloneServerPool.s

# target to generate assembly for a file
src/StandaloneServerPool.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.s
.PHONY : src/StandaloneServerPool.cpp.s

src/Subscribe.o: src/Subscribe.cpp.o

.PHONY : src/Subscribe.o

# target to build an object file
src/Subscribe.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Subscribe.cpp.o
.PHONY : src/Subscribe.cpp.o

src/Subscribe.i: src/Subscribe.cpp.i

.PHONY : src/Subscribe.i

# target to preprocess a source file
src/Subscribe.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Subscribe.cpp.i
.PHONY : src/Subscribe.cpp.i

src/Subscribe.s: src/Subscribe.cpp.s

.PHONY : src/Subscribe.s

# target to generate assembly for a file
src/Subscribe.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Subscribe.cpp.s
.PHONY : src/Subscribe.cpp.s

src/Timer.o: src/Timer.cpp.o

.PHONY : src/Timer.o

# target to build an object file
src/Timer.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Timer.cpp.o
.PHONY : src/Timer.cpp.o

src/Timer.i: src/Timer.cpp.i

.PHONY : src/Timer.i

# target to preprocess a source file
src/Timer.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Timer.cpp.i
.PHONY : src/Timer.cpp.i

src/Timer.s: src/Timer.cpp.s

.PHONY : src/Timer.s

# target to generate assembly for a file
src/Timer.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/Timer.cpp.s
.PHONY : src/Timer.cpp.s

src/main.o: src/main.cpp.o

.PHONY : src/main.o

# target to build an object file
src/main.cpp.o:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/main.cpp.o
.PHONY : src/main.cpp.o

src/main.i: src/main.cpp.i

.PHONY : src/main.i

# target to preprocess a source file
src/main.cpp.i:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/main.cpp.i
.PHONY : src/main.cpp.i

src/main.s: src/main.cpp.s

.PHONY : src/main.s

# target to generate assembly for a file
src/main.cpp.s:
	$(MAKE) $(MAKESILENT) -f CMakeFiles/predixy.dir/build.make CMakeFiles/predixy.dir/src/main.cpp.s
.PHONY : src/main.cpp.s

# Help Target
help:
	@echo "The following are some of the valid targets for this Makefile:"
	@echo "... all (the default if no target is provided)"
	@echo "... clean"
	@echo "... depend"
	@echo "... edit_cache"
	@echo "... rebuild_cache"
	@echo "... predixy"
	@echo "... src/AcceptConnection.o"
	@echo "... src/AcceptConnection.i"
	@echo "... src/AcceptConnection.s"
	@echo "... src/AcceptSocket.o"
	@echo "... src/AcceptSocket.i"
	@echo "... src/AcceptSocket.s"
	@echo "... src/Alloc.o"
	@echo "... src/Alloc.i"
	@echo "... src/Alloc.s"
	@echo "... src/Auth.o"
	@echo "... src/Auth.i"
	@echo "... src/Auth.s"
	@echo "... src/Buffer.o"
	@echo "... src/Buffer.i"
	@echo "... src/Buffer.s"
	@echo "... src/ClusterNodesParser.o"
	@echo "... src/ClusterNodesParser.i"
	@echo "... src/ClusterNodesParser.s"
	@echo "... src/ClusterServerPool.o"
	@echo "... src/ClusterServerPool.i"
	@echo "... src/ClusterServerPool.s"
	@echo "... src/Command.o"
	@echo "... src/Command.i"
	@echo "... src/Command.s"
	@echo "... src/Conf.o"
	@echo "... src/Conf.i"
	@echo "... src/Conf.s"
	@echo "... src/ConfParser.o"
	@echo "... src/ConfParser.i"
	@echo "... src/ConfParser.s"
	@echo "... src/ConnectConnection.o"
	@echo "... src/ConnectConnection.i"
	@echo "... src/ConnectConnection.s"
	@echo "... src/ConnectConnectionPool.o"
	@echo "... src/ConnectConnectionPool.i"
	@echo "... src/ConnectConnectionPool.s"
	@echo "... src/ConnectSocket.o"
	@echo "... src/ConnectSocket.i"
	@echo "... src/ConnectSocket.s"
	@echo "... src/Connection.o"
	@echo "... src/Connection.i"
	@echo "... src/Connection.s"
	@echo "... src/Crc16.o"
	@echo "... src/Crc16.i"
	@echo "... src/Crc16.s"
	@echo "... src/DC.o"
	@echo "... src/DC.i"
	@echo "... src/DC.s"
	@echo "... src/Distribution.o"
	@echo "... src/Distribution.i"
	@echo "... src/Distribution.s"
	@echo "... src/Enums.o"
	@echo "... src/Enums.i"
	@echo "... src/Enums.s"
	@echo "... src/Handler.o"
	@echo "... src/Handler.i"
	@echo "... src/Handler.s"
	@echo "... src/HashFunc.o"
	@echo "... src/HashFunc.i"
	@echo "... src/HashFunc.s"
	@echo "... src/KqueueMultiplexor.o"
	@echo "... src/KqueueMultiplexor.i"
	@echo "... src/KqueueMultiplexor.s"
	@echo "... src/LatencyMonitor.o"
	@echo "... src/LatencyMonitor.i"
	@echo "... src/LatencyMonitor.s"
	@echo "... src/ListenSocket.o"
	@echo "... src/ListenSocket.i"
	@echo "... src/ListenSocket.s"
	@echo "... src/LogFileSink.o"
	@echo "... src/LogFileSink.i"
	@echo "... src/LogFileSink.s"
	@echo "... src/Logger.o"
	@echo "... src/Logger.i"
	@echo "... src/Logger.s"
	@echo "... src/Proxy.o"
	@echo "... src/Proxy.i"
	@echo "... src/Proxy.s"
	@echo "... src/Reply.o"
	@echo "... src/Reply.i"
	@echo "... src/Reply.s"
	@echo "... src/Request.o"
	@echo "... src/Request.i"
	@echo "... src/Request.s"
	@echo "... src/RequestParser.o"
	@echo "... src/RequestParser.i"
	@echo "... src/RequestParser.s"
	@echo "... src/Response.o"
	@echo "... src/Response.i"
	@echo "... src/Response.s"
	@echo "... src/ResponseParser.o"
	@echo "... src/ResponseParser.i"
	@echo "... src/ResponseParser.s"
	@echo "... src/Server.o"
	@echo "... src/Server.i"
	@echo "... src/Server.s"
	@echo "... src/ServerGroup.o"
	@echo "... src/ServerGroup.i"
	@echo "... src/ServerGroup.s"
	@echo "... src/ServerPool.o"
	@echo "... src/ServerPool.i"
	@echo "... src/ServerPool.s"
	@echo "... src/Socket.o"
	@echo "... src/Socket.i"
	@echo "... src/Socket.s"
	@echo "... src/StandaloneServerPool.o"
	@echo "... src/StandaloneServerPool.i"
	@echo "... src/StandaloneServerPool.s"
	@echo "... src/Subscribe.o"
	@echo "... src/Subscribe.i"
	@echo "... src/Subscribe.s"
	@echo "... src/Timer.o"
	@echo "... src/Timer.i"
	@echo "... src/Timer.s"
	@echo "... src/main.o"
	@echo "... src/main.i"
	@echo "... src/main.s"
.PHONY : help



#=============================================================================
# Special targets to cleanup operation of make.

# Special rule to run CMake to check the build system integrity.
# No rule that depends on this can have commands that come from listfiles
# because they might be regenerated.
cmake_check_build_system:
	$(CMAKE_COMMAND) -S$(CMAKE_SOURCE_DIR) -B$(CMAKE_BINARY_DIR) --check-build-system CMakeFiles/Makefile.cmake 0
.PHONY : cmake_check_build_system

