<?xml version="1.0" encoding="UTF-8"?>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="6"/>
	<Project>
		<Option title="predixy"/>
		<Option makefile_is_custom="1"/>
		<Option compiler="gcc"/>
		<Option virtualFolders="CMake Files\;"/>
		<Build>
			<Target title="all">
				<Option working_dir="/Users/<USER>/CLionProjects/predixy/cmake-build-debug"/>
				<Option type="4"/>
				<MakeCommands>
					<Build command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 all"/>
					<CompileFile command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 &quot;$file&quot;"/>
					<Clean command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 clean"/>
					<DistClean command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 clean"/>
				</MakeCommands>
			</Target>
			<Target title="rebuild_cache">
				<Option working_dir="/Users/<USER>/CLionProjects/predixy/cmake-build-debug"/>
				<Option type="4"/>
				<MakeCommands>
					<Build command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 rebuild_cache"/>
					<CompileFile command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 &quot;$file&quot;"/>
					<Clean command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 clean"/>
					<DistClean command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 clean"/>
				</MakeCommands>
			</Target>
			<Target title="edit_cache">
				<Option working_dir="/Users/<USER>/CLionProjects/predixy/cmake-build-debug"/>
				<Option type="4"/>
				<MakeCommands>
					<Build command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 edit_cache"/>
					<CompileFile command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 &quot;$file&quot;"/>
					<Clean command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 clean"/>
					<DistClean command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 clean"/>
				</MakeCommands>
			</Target>
			<Target title="predixy">
				<Option output="/Users/<USER>/CLionProjects/predixy/cmake-build-debug/predixy" prefix_auto="0" extension_auto="0"/>
				<Option working_dir="/Users/<USER>/CLionProjects/predixy/cmake-build-debug"/>
				<Option object_output="./"/>
				<Option type="1"/>
				<Option compiler="gcc"/>
				<Compiler>
					<Add option="-D_PREDIXY_BACKTRACE_"/>
					<Add option="-D_KQUEUE_"/>
					<Add directory="/usr/local/include,"/>
					<Add directory="/Users/<USER>/CLionProjects/predixy/src"/>
					<Add directory="/Library/Developer/CommandLineTools/usr/bin/../include/c++/v1"/>
					<Add directory="/usr/local/include"/>
					<Add directory="/Library/Developer/CommandLineTools/usr/lib/clang/11.0.0/include"/>
					<Add directory="/Library/Developer/CommandLineTools/usr/include"/>
					<Add directory="/System/Library/Frameworks"/>
					<Add directory="/Library/Frameworks"/>
				</Compiler>
				<MakeCommands>
					<Build command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 predixy"/>
					<CompileFile command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 &quot;$file&quot;"/>
					<Clean command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 clean"/>
					<DistClean command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 clean"/>
				</MakeCommands>
			</Target>
			<Target title="predixy/fast">
				<Option output="/Users/<USER>/CLionProjects/predixy/cmake-build-debug/predixy" prefix_auto="0" extension_auto="0"/>
				<Option working_dir="/Users/<USER>/CLionProjects/predixy/cmake-build-debug"/>
				<Option object_output="./"/>
				<Option type="1"/>
				<Option compiler="gcc"/>
				<Compiler>
					<Add option="-D_PREDIXY_BACKTRACE_"/>
					<Add option="-D_KQUEUE_"/>
					<Add directory="/usr/local/include,"/>
					<Add directory="/Users/<USER>/CLionProjects/predixy/src"/>
					<Add directory="/Library/Developer/CommandLineTools/usr/bin/../include/c++/v1"/>
					<Add directory="/usr/local/include"/>
					<Add directory="/Library/Developer/CommandLineTools/usr/lib/clang/11.0.0/include"/>
					<Add directory="/Library/Developer/CommandLineTools/usr/include"/>
					<Add directory="/System/Library/Frameworks"/>
					<Add directory="/Library/Frameworks"/>
				</Compiler>
				<MakeCommands>
					<Build command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 predixy/fast"/>
					<CompileFile command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 &quot;$file&quot;"/>
					<Clean command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 clean"/>
					<DistClean command="/usr/bin/make -j4 -f &quot;/Users/<USER>/CLionProjects/predixy/cmake-build-debug/Makefile&quot;  VERBOSE=1 clean"/>
				</MakeCommands>
			</Target>
		</Build>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/AcceptConnection.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/AcceptConnection.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/AcceptSocket.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/AcceptSocket.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Alloc.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Alloc.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Auth.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Auth.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Backtrace.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Buffer.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Buffer.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ClusterNodesParser.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ClusterNodesParser.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ClusterServerPool.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ClusterServerPool.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Command.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Command.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Common.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Conf.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Conf.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ConfParser.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ConfParser.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ConnectConnection.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ConnectConnection.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ConnectConnectionPool.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ConnectConnectionPool.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ConnectSocket.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ConnectSocket.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Connection.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Connection.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Crc16.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/DC.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/DC.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Deque.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Distribution.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Distribution.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Enums.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Enums.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Exception.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Handler.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Handler.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/HashFunc.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/HashFunc.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ID.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/IOVec.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/KqueueMultiplexor.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/KqueueMultiplexor.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/LatencyMonitor.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/LatencyMonitor.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/List.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ListenSocket.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ListenSocket.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/LogFileSink.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/LogFileSink.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Logger.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Logger.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Multiplexor.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/PString.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Predixy.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Proxy.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Proxy.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Reply.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Reply.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Request.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Request.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/RequestParser.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/RequestParser.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Response.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Response.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ResponseParser.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ResponseParser.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Server.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Server.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ServerGroup.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ServerGroup.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ServerPool.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/ServerPool.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Socket.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Socket.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/StandaloneServerPool.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/StandaloneServerPool.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Stats.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Subscribe.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Subscribe.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Sync.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Timer.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Timer.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Transaction.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/Util.h">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/src/main.cpp">
			<Option target="predixy"/>
		</Unit>
		<Unit filename="/Users/<USER>/CLionProjects/predixy/CMakeLists.txt">
			<Option virtualFolder="CMake Files\"/>
		</Unit>
	</Project>
</CodeBlocks_project_file>
