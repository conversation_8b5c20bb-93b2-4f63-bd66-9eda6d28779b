<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CLionExternalBuildManager">
    <target id="a12f44e8-9743-4c88-8cd3-ccbc72cfd993" name="make" projectName="predixy">
      <configuration id="b02b81c8-b602-44bf-aad9-e37fcb8d76da" name="make" toolchainName="Default">
        <build type="TOOL">
          <tool actionId="Tool_External Tools_build_with_make" />
        </build>
        <clean type="TOOL">
          <tool actionId="Tool_External Tools_clean_with_make" />
        </clean>
      </configuration>
    </target>
  </component>
</project>