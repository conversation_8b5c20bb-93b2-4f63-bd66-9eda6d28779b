# 🔒 Predixy专用安全扫描器

一个专门为Predixy项目设计的综合安全扫描工具，集成CVE数据库搜索、代码静态分析和依赖库安全检查。

## 📁 项目结构

```
security/
├── README.md                    # 📖 使用指南（本文件）
└── predixy_security_scanner.py  # 🔒 核心安全扫描器
```

## 🎯 核心功能

### 1. CVE数据库关键字检索
- ✅ 专门搜索包含"predixy"品牌的CVE
- ✅ Redis代理相关漏洞检索
- ✅ 智能相关性评分和排序
- ✅ 支持自定义搜索关键词

### 2. CppCheck代码静态分析
- ✅ 全面的C++代码扫描
- ✅ 内存泄漏、缓冲区溢出检测
- ✅ 具体的修复建议
- ✅ 按严重性分类问题

### 3. 依赖库CVE检查
- ✅ 检查常用依赖库安全状况
- ✅ hiredis、openssl、libevent等
- ✅ 依赖库特定CVE搜索

## 🚀 快速开始

### 基础使用

```bash
# 运行完整扫描（默认文本报告）
python3 security/predixy_security_scanner.py

# 指定项目目录
python3 security/predixy_security_scanner.py --project /path/to/predixy
```

### 报告格式选择

```bash
# 生成文本格式报告（推荐，易读）
python3 security/predixy_security_scanner.py --format text

# 生成JSON格式报告（用于程序处理）
python3 security/predixy_security_scanner.py --format json
```

### 自定义搜索关键词

```bash
# 使用自定义关键词搜索CVE
python3 security/predixy_security_scanner.py --keywords "predixy" "redis proxy" "network security"
```

### 查看帮助信息

```bash
# 查看所有可用选项
python3 security/predixy_security_scanner.py --help
```

## � 输出示例

### 终端实时输出
```
🔒 Predixy专用安全扫描器
==================================================
📁 项目目录: /Users/<USER>/CLionProjects/predixy
🛠️ CppCheck: ✅ 可用
⏰ 扫描时间: 2024-12-08 18:30:22
==================================================

🔍 CVE数据库关键字检索
-------------------------
  🔎 搜索关键字: 'predixy'
    ✅ 找到 2 个相关CVE
  🔎 搜索关键字: 'redis proxy vulnerability'
    ✅ 找到 5 个相关CVE

  📊 总计找到: 7 个唯一CVE
  📋 CVE搜索结果 (按相关性排序):
    1. CVE-2023-12345 (HIGH, 评分: 8.1)
       📝 Redis proxy buffer overflow vulnerability...
       🔍 搜索词: redis proxy vulnerability
       🎯 相关性: 8/10

🔧 CppCheck代码扫描
------------------
  🔄 正在扫描代码...
  ✅ 扫描完成，发现 8 个问题
  📊 问题分类:
     error: 2 个
     warning: 3 个
  💡 修复建议:
     1. 数组越界访问
        📍 位置: src/Buffer.cpp:45
        🔧 建议: 添加边界检查

📦 依赖库CVE检查
--------------
  🔍 检查 hiredis...
    ⚠️ 找到 2 个相关CVE

📄 扫描报告已保存: predixy_security_scan_20241208_183022.txt
```

## 📋 环境要求

### 必需依赖
- Python 3.6+
- requests库: `pip install requests`

### 可选依赖
- CppCheck: 用于代码静态分析
  ```bash
  # macOS
  brew install cppcheck

  # Ubuntu/Debian
  sudo apt-get install cppcheck

  # CentOS/RHEL
  sudo yum install cppcheck
  ```

### 网络要求
- 需要访问NVD CVE数据库 (https://services.nvd.nist.gov)
- 如果网络受限，CVE查询功能会跳过，但代码扫描仍可正常工作

## 🎯 默认搜索策略

工具使用以下默认关键词搜索CVE：

1. **品牌专属搜索**
   - `predixy` - 直接搜索predixy品牌
   - `predixy vulnerability` - predixy漏洞

2. **技术栈相关**
   - `redis proxy vulnerability` - Redis代理漏洞
   - `redis proxy buffer overflow` - Redis代理缓冲区溢出
   - `c++ redis proxy` - C++ Redis代理

3. **通用安全**
   - `network proxy security` - 网络代理安全

## 📈 相关性评分算法

CVE相关性评分规则：
- 直接提到"predixy": +10分
- Redis代理相关: +8分
- Redis相关: +5分
- 代理相关: +3分
- C++相关: +3分
- 缓冲区溢出: +2分
- 内存相关: +2分
- 网络相关: +1分

排除规则：
- JavaScript/Python/Java相关: -3分

## 🔧 依赖库检查列表

工具会检查以下常见依赖库的CVE：
- `hiredis` - Redis C客户端
- `libevent` - 事件库
- `openssl` - SSL/TLS库
- `zlib` - 压缩库
- `pthread` - 线程库
- `glibc` - C标准库
- `gcc` - 编译器
- `cmake` - 构建工具

## 🛠️ 常见问题解决

### Q1: CppCheck未找到
```bash
# macOS
brew install cppcheck

# Ubuntu/Debian
sudo apt-get install cppcheck

# CentOS/RHEL
sudo yum install cppcheck
```

### Q2: Python依赖安装失败
```bash
# 安装requests库
pip install requests

# 使用国内镜像（如果网络慢）
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple requests
```

### Q3: CVE查询失败
- 检查网络连接
- 确认防火墙设置
- 使用代理（如需要）：
  ```bash
  export https_proxy=http://your-proxy:port
  python3 security/predixy_security_scanner.py
  ```

## � 使用建议

### 日常开发
```bash
# 快速本地扫描
python3 security/predixy_security_scanner.py --format text
```

### 发布前检查
```bash
# 完整扫描并保存详细报告
python3 security/predixy_security_scanner.py --format text > security_check.txt
```

### CI/CD集成
```bash
# 在构建脚本中使用
python3 security/predixy_security_scanner.py --format json > security_report.json
```

### 安全事件响应
```bash
# 针对特定漏洞类型搜索
python3 security/predixy_security_scanner.py --keywords "buffer overflow" "memory corruption"
```

## �🔄 定期扫描建议

### 扫描频率
- **开发阶段**: 每周一次
- **发布前**: 必须扫描
- **生产环境**: 每月一次
- **安全事件后**: 立即扫描

### 自动化集成

#### GitHub Actions
```yaml
name: Security Scan
on:
  schedule:
    - cron: '0 2 * * 1'  # 每周一凌晨2点
  push:
    branches: [ main ]

jobs:
  security-scan:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Install Python dependencies
      run: pip install requests
    - name: Run Security Scan
      run: python3 security/predixy_security_scanner.py --format json
    - name: Upload Reports
      uses: actions/upload-artifact@v2
      with:
        name: security-reports
        path: predixy_security_scan_*.json
```

## � 注意事项

1. **网络连接**: CVE查询需要网络连接，如果网络不可用会跳过CVE查询
2. **API限制**: NVD API有请求频率限制，工具已内置延迟机制
3. **CppCheck版本**: 建议使用CppCheck 2.0+版本以获得最佳效果
4. **误报处理**: 静态分析可能产生误报，需要人工判断
5. **定期更新**: 建议定期运行扫描以发现新的安全问题

## 🔗 相关资源

### CVE数据库
- [NVD - National Vulnerability Database](https://nvd.nist.gov/)
- [CVE Details](https://www.cvedetails.com/)
- [MITRE CVE](https://cve.mitre.org/)

### 安全工具
- [CppCheck](http://cppcheck.sourceforge.net/)
- [Clang Static Analyzer](https://clang-analyzer.llvm.org/)

### 安全指南
- [OWASP C++ Security](https://owasp.org/www-project-secure-coding-practices-quick-reference-guide/)
- [SEI CERT C++ Coding Standard](https://wiki.sei.cmu.edu/confluence/pages/viewpage.action?pageId=88046682)

---

**🔒 保护Predixy，从安全扫描开始！**

本工具专门为Predixy项目设计，提供精准的安全扫描和CVE检索功能。建议定期使用以确保项目安全。
