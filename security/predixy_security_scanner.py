#!/usr/bin/env python3
"""
Predixy专用安全扫描器
功能：
1. CVE数据库关键字检索（包含predixy品牌）
2. CppCheck代码扫描和修复建议
3. 依赖库CVE检查
"""

import os
import re
import sys
import json
import requests
import subprocess
import argparse
import xml.etree.ElementTree as ET
from pathlib import Path
from datetime import datetime
import time

class PredixySecurityScanner:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.scan_results = {
            'scan_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'cve_search_results': [],
            'cppcheck_results': [],
            'dependency_cves': [],
            'recommendations': []
        }
        
        # CVE API配置
        self.nvd_api_base = "https://services.nvd.nist.gov/rest/json/cves/2.0"
        
        # 检查CppCheck可用性
        self.cppcheck_available = self.check_cppcheck()
    
    def check_cppcheck(self):
        """检查CppCheck是否可用"""
        try:
            result = subprocess.run(['cppcheck', '--version'], 
                                  capture_output=True, text=True, timeout=5)
            return result.returncode == 0
        except:
            return False
    
    def print_header(self):
        """打印工具头部信息"""
        print("🔒 Predixy专用安全扫描器")
        print("=" * 50)
        print(f"📁 项目目录: {self.project_root.absolute()}")
        print(f"🛠️ CppCheck: {'✅ 可用' if self.cppcheck_available else '❌ 不可用'}")
        print(f"⏰ 扫描时间: {self.scan_results['scan_time']}")
        print("=" * 50)
        print()
    
    def search_cve_by_keywords(self, keywords):
        """通过关键字搜索CVE"""
        print("🔍 CVE数据库关键字检索")
        print("-" * 25)
        
        all_results = []
        
        for keyword in keywords:
            print(f"  🔎 搜索关键字: '{keyword}'")
            
            try:
                params = {
                    'keywordSearch': keyword,
                    'resultsPerPage': 10
                }
                
                response = requests.get(self.nvd_api_base, params=params, timeout=15)
                
                if response.status_code == 200:
                    data = response.json()
                    cves = self.parse_cve_response(data)
                    
                    if cves:
                        print(f"    ✅ 找到 {len(cves)} 个相关CVE")
                        for cve in cves:
                            cve['search_keyword'] = keyword
                        all_results.extend(cves)
                    else:
                        print(f"    ❌ 未找到相关CVE")
                        
                elif response.status_code == 429:
                    print(f"    ⏳ API限制，跳过此查询")
                else:
                    print(f"    ❌ 查询失败: HTTP {response.status_code}")
                    
            except requests.exceptions.Timeout:
                print(f"    ⏰ 查询超时")
            except Exception as e:
                print(f"    ❌ 查询错误: {e}")
            
            time.sleep(1)  # 避免API限制
        
        # 去重并按相关性排序
        unique_cves = self.deduplicate_and_rank_cves(all_results)
        self.scan_results['cve_search_results'] = unique_cves
        
        print(f"\n  📊 总计找到: {len(unique_cves)} 个唯一CVE")
        self.display_cve_summary(unique_cves)
        print()
    
    def parse_cve_response(self, data):
        """解析CVE API响应"""
        cves = []
        
        for vuln in data.get('vulnerabilities', []):
            cve_data = vuln.get('cve', {})
            
            # 提取CVSS信息
            metrics = cve_data.get('metrics', {})
            cvss_v3 = metrics.get('cvssMetricV3', [{}])[0].get('cvssData', {})
            cvss_v2 = metrics.get('cvssMetricV2', [{}])[0].get('cvssData', {})
            
            if cvss_v3:
                severity = cvss_v3.get('baseSeverity', 'Unknown')
                score = cvss_v3.get('baseScore', 0)
                vector = cvss_v3.get('attackVector', 'Unknown')
            elif cvss_v2:
                score = cvss_v2.get('baseScore', 0)
                if score >= 7.0:
                    severity = 'HIGH'
                elif score >= 4.0:
                    severity = 'MEDIUM'
                else:
                    severity = 'LOW'
                vector = cvss_v2.get('accessVector', 'Unknown')
            else:
                severity = 'Unknown'
                score = 0
                vector = 'Unknown'
            
            cve_info = {
                'cve_id': cve_data.get('id'),
                'description': cve_data.get('descriptions', [{}])[0].get('value', ''),
                'severity': severity,
                'score': score,
                'published': cve_data.get('published', ''),
                'vector': vector,
                'references': [ref.get('url') for ref in cve_data.get('references', [])[:2]]
            }
            
            cves.append(cve_info)
        
        return cves
    
    def deduplicate_and_rank_cves(self, cves):
        """CVE去重并按相关性排序"""
        seen = set()
        unique_cves = []
        
        for cve in cves:
            cve_id = cve.get('cve_id')
            if cve_id and cve_id not in seen:
                seen.add(cve_id)
                
                # 计算相关性评分
                relevance_score = self.calculate_relevance_score(cve)
                cve['relevance_score'] = relevance_score
                
                unique_cves.append(cve)
        
        # 按相关性和严重性排序
        return sorted(unique_cves, 
                     key=lambda x: (x.get('relevance_score', 0), x.get('score', 0)), 
                     reverse=True)
    
    def calculate_relevance_score(self, cve):
        """计算CVE与Predixy的相关性评分"""
        score = 0
        description = cve.get('description', '').lower()
        
        # Predixy相关关键词
        if 'predixy' in description:
            score += 10  # 直接提到predixy
        if 'redis' in description and 'proxy' in description:
            score += 8   # Redis代理相关
        if 'redis' in description:
            score += 5   # Redis相关
        if 'proxy' in description:
            score += 3   # 代理相关
        
        # 技术栈相关
        if 'c++' in description or 'cpp' in description:
            score += 3
        if 'buffer overflow' in description:
            score += 2
        if 'memory' in description:
            score += 2
        if 'network' in description:
            score += 1
        
        # 排除明显不相关的
        if any(exclude in description for exclude in ['javascript', 'python', 'java', 'web browser']):
            score -= 3
        
        return max(0, score)
    
    def display_cve_summary(self, cves):
        """显示CVE搜索结果摘要"""
        if not cves:
            return
        
        print("  📋 CVE搜索结果 (按相关性排序):")
        
        for i, cve in enumerate(cves[:5], 1):  # 显示前5个最相关的
            severity_color = self.get_severity_color(cve.get('severity', 'Unknown'))
            print(f"    {severity_color}{i}. {cve['cve_id']} ({cve.get('severity', 'Unknown')}, 评分: {cve.get('score', 'N/A')}){self.reset_color()}")
            print(f"       📝 {cve['description'][:100]}...")
            print(f"       🔍 搜索词: {cve.get('search_keyword', 'Unknown')}")
            print(f"       🎯 相关性: {cve.get('relevance_score', 0)}/10")
            if cve.get('references'):
                print(f"       🔗 参考: {cve['references'][0]}")
            print()
        
        if len(cves) > 5:
            print(f"    ... 还有 {len(cves) - 5} 个CVE")
    
    def run_cppcheck_scan(self):
        """运行CppCheck代码扫描"""
        if not self.cppcheck_available:
            print("⚠️ CppCheck不可用，跳过代码扫描")
            print("💡 安装建议: brew install cppcheck")
            return
        
        print("🔧 CppCheck代码扫描")
        print("-" * 20)
        
        # 检查src目录是否存在
        src_dir = self.project_root / 'src'
        if not src_dir.exists():
            print("  ❌ 未找到src目录")
            return
        
        # 运行CppCheck
        cmd = [
            'cppcheck',
            '--enable=all',
            '--xml',
            '--xml-version=2',
            '--suppress=missingIncludeSystem',
            '--suppress=unmatchedSuppression',
            '--suppress=unusedFunction',  # 减少噪音
            str(src_dir)
        ]
        
        try:
            print("  🔄 正在扫描代码...")
            result = subprocess.run(cmd, capture_output=True, text=True, 
                                  cwd=self.project_root, timeout=300)
            
            if result.stderr:  # CppCheck输出到stderr
                issues = self.parse_cppcheck_xml(result.stderr)
                self.scan_results['cppcheck_results'] = issues
                print(f"  ✅ 扫描完成，发现 {len(issues)} 个问题")
                self.display_cppcheck_summary(issues)
                self.generate_fix_recommendations(issues)
            else:
                print("  ✅ 扫描完成，未发现问题")
                
        except subprocess.TimeoutExpired:
            print("  ⏰ 扫描超时")
        except Exception as e:
            print(f"  ❌ 扫描失败: {e}")
        
        print()
    
    def parse_cppcheck_xml(self, xml_output):
        """解析CppCheck XML输出"""
        issues = []
        
        try:
            root = ET.fromstring(xml_output)
            
            for error in root.findall('.//error'):
                issue = {
                    'id': error.get('id', 'unknown'),
                    'severity': error.get('severity', 'unknown'),
                    'msg': error.get('msg', ''),
                    'verbose': error.get('verbose', ''),
                    'locations': []
                }
                
                # 解析位置信息
                for location in error.findall('location'):
                    loc = {
                        'file': location.get('file', ''),
                        'line': int(location.get('line', 0)),
                        'column': int(location.get('column', 0))
                    }
                    issue['locations'].append(loc)
                
                issues.append(issue)
                
        except ET.ParseError as e:
            print(f"  ⚠️ 解析CppCheck输出失败: {e}")
        
        return issues
    
    def display_cppcheck_summary(self, issues):
        """显示CppCheck结果摘要"""
        if not issues:
            return
        
        # 按严重性分类
        severity_counts = {}
        for issue in issues:
            severity = issue['severity']
            severity_counts[severity] = severity_counts.get(severity, 0) + 1
        
        print("  📊 问题分类:")
        severity_order = ['error', 'warning', 'style', 'performance', 'portability', 'information']
        
        for severity in severity_order:
            if severity in severity_counts:
                count = severity_counts[severity]
                color = self.get_severity_color(severity)
                print(f"     {color}{severity}: {count} 个{self.reset_color()}")
        
        # 显示高优先级问题
        high_priority = [i for i in issues if i['severity'] in ['error', 'warning']]
        
        if high_priority:
            print("  🚨 高优先级问题:")
            for i, issue in enumerate(high_priority[:3], 1):
                if issue['locations']:
                    loc = issue['locations'][0]
                    file_path = os.path.relpath(loc['file'], self.project_root)
                    print(f"     {i}. {file_path}:{loc['line']} - {issue['msg']}")
    
    def generate_fix_recommendations(self, issues):
        """生成修复建议"""
        recommendations = []
        
        for issue in issues:
            if issue['severity'] in ['error', 'warning']:
                rec = self.create_fix_recommendation(issue)
                if rec:
                    recommendations.append(rec)
        
        self.scan_results['recommendations'] = recommendations
        
        if recommendations:
            print("  💡 修复建议:")
            for i, rec in enumerate(recommendations[:5], 1):
                print(f"     {i}. {rec['title']}")
                print(f"        📍 位置: {rec['location']}")
                print(f"        🔧 建议: {rec['suggestion']}")
                print()
    
    def create_fix_recommendation(self, issue):
        """为CppCheck问题创建修复建议"""
        if not issue['locations']:
            return None
        
        loc = issue['locations'][0]
        file_path = os.path.relpath(loc['file'], self.project_root)
        
        # 根据问题类型生成建议
        issue_id = issue['id']
        msg = issue['msg']
        
        fix_suggestions = {
            'arrayIndexOutOfBounds': {
                'title': '数组越界访问',
                'suggestion': '添加边界检查: if (index >= 0 && index < array_size)'
            },
            'nullPointer': {
                'title': '空指针解引用',
                'suggestion': '添加空指针检查: if (ptr != NULL)'
            },
            'memoryLeak': {
                'title': '内存泄漏',
                'suggestion': '确保调用free()释放malloc分配的内存'
            },
            'bufferAccessOutOfBounds': {
                'title': '缓冲区越界',
                'suggestion': '使用安全的字符串函数，如strncpy替代strcpy'
            },
            'uninitvar': {
                'title': '使用未初始化变量',
                'suggestion': '在使用前初始化变量'
            }
        }
        
        suggestion_info = fix_suggestions.get(issue_id, {
            'title': msg,
            'suggestion': '请查看CppCheck文档了解详细修复方法'
        })
        
        return {
            'title': suggestion_info['title'],
            'location': f"{file_path}:{loc['line']}",
            'suggestion': suggestion_info['suggestion'],
            'severity': issue['severity'],
            'issue_id': issue_id
        }
    
    def detect_dependency_versions(self):
        """检测依赖库版本"""
        print("📦 检测依赖库版本")
        print("-" * 15)

        detected_deps = {}

        # 检测系统库版本
        system_checks = {
            'glibc': ['ldd --version', r'ldd \(.*\) ([\d.]+)'],
            'openssl': ['openssl version', r'OpenSSL ([\d.]+[a-z]?)'],
            'gcc': ['gcc --version', r'gcc.*? ([\d.]+)'],
            'cmake': ['cmake --version', r'cmake version ([\d.]+)']
        }

        for lib_name, (cmd, pattern) in system_checks.items():
            try:
                result = subprocess.run(cmd.split(), capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    import re
                    match = re.search(pattern, result.stdout)
                    if match:
                        version = match.group(1)
                        detected_deps[lib_name] = {
                            'version': version,
                            'detection_method': 'system_command',
                            'full_output': result.stdout.strip()
                        }
                        print(f"  ✅ {lib_name}: {version}")
                    else:
                        print(f"  ⚠️ {lib_name}: 已安装但无法解析版本")
                else:
                    print(f"  ❌ {lib_name}: 未找到")
            except Exception as e:
                print(f"  ❌ {lib_name}: 检测失败 ({e})")

        # 检测pkg-config可检测的库
        pkg_config_libs = ['zlib', 'libevent']

        for lib_name in pkg_config_libs:
            try:
                result = subprocess.run(['pkg-config', '--modversion', lib_name],
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    version = result.stdout.strip()
                    detected_deps[lib_name] = {
                        'version': version,
                        'detection_method': 'pkg_config',
                        'full_output': version
                    }
                    print(f"  ✅ {lib_name}: {version}")
                else:
                    print(f"  ❌ {lib_name}: 未找到")
            except Exception as e:
                print(f"  ❌ {lib_name}: 检测失败 ({e})")

        # 检测编译时链接的库（从二进制文件）
        self.check_linked_libraries(detected_deps)

        print(f"\n  📊 检测到 {len(detected_deps)} 个依赖库版本")
        return detected_deps

    def check_linked_libraries(self, detected_deps):
        """检查编译时链接的库"""
        # 查找可能的Predixy二进制文件
        possible_binaries = [
            'predixy',
            'src/predixy',
            'build/predixy',
            'bin/predixy'
        ]

        for binary_path in possible_binaries:
            full_path = self.project_root / binary_path
            if full_path.exists():
                print(f"  🔍 分析二进制文件: {binary_path}")
                try:
                    # 使用ldd检查动态链接库
                    result = subprocess.run(['ldd', str(full_path)],
                                          capture_output=True, text=True, timeout=5)
                    if result.returncode == 0:
                        self.parse_ldd_output(result.stdout, detected_deps)
                    break
                except Exception as e:
                    print(f"    ⚠️ 无法分析: {e}")

    def parse_ldd_output(self, ldd_output, detected_deps):
        """解析ldd输出"""
        import re

        # 解析ldd输出中的库信息
        lib_patterns = {
            'hiredis': r'libhiredis\.so\.([\d.]+)',
            'ssl': r'libssl\.so\.([\d.]+)',
            'crypto': r'libcrypto\.so\.([\d.]+)',
            'event': r'libevent.*\.so\.([\d.]+)',
            'z': r'libz\.so\.([\d.]+)',
            'pthread': r'libpthread\.so\.([\d.]+)',
            'c': r'libc\.so\.([\d.]+)'
        }

        for lib_name, pattern in lib_patterns.items():
            match = re.search(pattern, ldd_output)
            if match:
                version = match.group(1)
                if lib_name not in detected_deps:
                    detected_deps[lib_name] = {
                        'version': version,
                        'detection_method': 'ldd_analysis',
                        'full_output': f"Linked library version {version}"
                    }
                    print(f"    ✅ 链接库 {lib_name}: {version}")

    def check_dependency_cves_with_versions(self, detected_deps):
        """基于版本信息检查依赖库CVE"""
        print("\n🔍 基于版本的精确CVE检查")
        print("-" * 25)

        dependency_cves = []

        for dep_name, dep_info in detected_deps.items():
            version = dep_info['version']
            print(f"  🔍 检查 {dep_name} v{version}...")

            try:
                # 搜索特定版本的CVE
                search_terms = [
                    f"{dep_name} {version}",
                    f"{dep_name} vulnerability",
                    f"{dep_name} before {version}"
                ]

                found_cves = []
                for search_term in search_terms[:2]:  # 限制搜索数量
                    params = {
                        'keywordSearch': search_term,
                        'resultsPerPage': 5
                    }

                    response = requests.get(self.nvd_api_base, params=params, timeout=10)

                    if response.status_code == 200:
                        data = response.json()
                        cves = self.parse_cve_response(data)

                        # 过滤与当前库和版本相关的CVE
                        relevant_cves = self.filter_version_specific_cves(cves, dep_name, version)
                        found_cves.extend(relevant_cves)

                    time.sleep(1)

                if found_cves:
                    unique_cves = self.deduplicate_cves(found_cves)
                    print(f"    ⚠️ 找到 {len(unique_cves)} 个版本相关CVE")

                    for cve in unique_cves:
                        cve['dependency'] = dep_name
                        cve['detected_version'] = version
                        cve['detection_method'] = dep_info['detection_method']

                    dependency_cves.extend(unique_cves)

                    # 显示最严重的CVE
                    if unique_cves:
                        top_cve = max(unique_cves, key=lambda x: x.get('score', 0))
                        severity_color = self.get_severity_color(top_cve.get('severity', 'Unknown'))
                        print(f"      {severity_color}🚨 最严重: {top_cve['cve_id']} (评分: {top_cve.get('score', 'N/A')}){self.reset_color()}")
                else:
                    print(f"    ✅ 未发现版本相关CVE")

            except Exception as e:
                print(f"    ❌ 查询错误: {e}")

        self.scan_results['dependency_cves'] = dependency_cves
        self.scan_results['detected_dependencies'] = detected_deps

        if dependency_cves:
            print(f"\n  📊 版本相关CVE总计: {len(dependency_cves)} 个")
            self.display_version_aware_cve_summary(dependency_cves)
        else:
            print(f"\n  ✅ 未发现版本相关CVE")

        print()

    def filter_version_specific_cves(self, cves, dep_name, current_version):
        """过滤版本特定的CVE"""
        relevant_cves = []

        for cve in cves:
            description = cve.get('description', '').lower()

            # 检查是否与当前库相关
            if dep_name.lower() not in description:
                continue

            # 尝试解析CVE中提到的版本范围
            version_relevance = self.check_version_relevance(description, current_version)

            if version_relevance['is_relevant']:
                cve['version_relevance'] = version_relevance
                cve['relevance_score'] = version_relevance['confidence']
                relevant_cves.append(cve)

        return relevant_cves

    def check_version_relevance(self, description, current_version):
        """检查版本相关性"""
        import re

        # 解析当前版本
        try:
            current_parts = [int(x) for x in current_version.split('.') if x.isdigit()]
        except:
            return {'is_relevant': True, 'confidence': 0.5, 'reason': '无法解析版本'}

        # 查找版本模式
        version_patterns = [
            r'before ([\d.]+)',
            r'prior to ([\d.]+)',
            r'versions? ([\d.]+) and earlier',
            r'through ([\d.]+)',
            r'up to ([\d.]+)',
            r'([\d.]+) and earlier'
        ]

        for pattern in version_patterns:
            matches = re.findall(pattern, description)
            for match in matches:
                try:
                    affected_parts = [int(x) for x in match.split('.') if x.isdigit()]

                    # 比较版本
                    if self.compare_versions(current_parts, affected_parts) <= 0:
                        return {
                            'is_relevant': True,
                            'confidence': 0.9,
                            'reason': f'当前版本 {current_version} 可能受影响 (CVE提到: {match})'
                        }
                except:
                    continue

        # 如果没有找到明确的版本信息，但提到了库名，则认为可能相关
        return {'is_relevant': True, 'confidence': 0.3, 'reason': '通用库漏洞，需要人工确认'}

    def compare_versions(self, version1, version2):
        """比较版本号 返回: -1(v1<v2), 0(v1==v2), 1(v1>v2)"""
        # 补齐版本号长度
        max_len = max(len(version1), len(version2))
        v1 = version1 + [0] * (max_len - len(version1))
        v2 = version2 + [0] * (max_len - len(version2))

        for i in range(max_len):
            if v1[i] < v2[i]:
                return -1
            elif v1[i] > v2[i]:
                return 1
        return 0

    def display_version_aware_cve_summary(self, dependency_cves):
        """显示版本感知的CVE摘要"""
        print("  📋 版本相关CVE详情:")

        # 按依赖库分组
        by_dependency = {}
        for cve in dependency_cves:
            dep = cve.get('dependency', 'unknown')
            if dep not in by_dependency:
                by_dependency[dep] = []
            by_dependency[dep].append(cve)

        for dep, cves in by_dependency.items():
            detected_version = cves[0].get('detected_version', 'Unknown')
            detection_method = cves[0].get('detection_method', 'Unknown')

            print(f"    📦 {dep} v{detected_version} (检测方式: {detection_method})")
            print(f"       发现 {len(cves)} 个相关CVE:")

            for cve in cves[:2]:  # 显示前2个
                severity_color = self.get_severity_color(cve.get('severity', 'Unknown'))
                relevance = cve.get('version_relevance', {})
                confidence = relevance.get('confidence', 0)

                print(f"       {severity_color}• {cve['cve_id']} ({cve.get('severity', 'Unknown')}, 评分: {cve.get('score', 'N/A')}){self.reset_color()}")
                print(f"         相关性: {confidence:.1f}/1.0 - {relevance.get('reason', '未知')}")
                print(f"         描述: {cve['description'][:80]}...")

            if len(cves) > 2:
                print(f"       ... 还有 {len(cves) - 2} 个CVE")
            print()

    def check_dependency_cves(self):
        """检查依赖库CVE（增强版本）"""
        # 首先检测依赖库版本
        detected_deps = self.detect_dependency_versions()

        # 基于版本信息进行精确CVE检查
        if detected_deps:
            self.check_dependency_cves_with_versions(detected_deps)
        else:
            print("  ⚠️ 未检测到依赖库版本，跳过精确CVE检查")
            print()
    
    def display_dependency_cve_summary(self, dependency_cves):
        """显示依赖库CVE摘要"""
        print("  📋 依赖库CVE详情:")
        
        # 按依赖库分组
        by_dependency = {}
        for cve in dependency_cves:
            dep = cve.get('dependency', 'unknown')
            if dep not in by_dependency:
                by_dependency[dep] = []
            by_dependency[dep].append(cve)
        
        for dep, cves in by_dependency.items():
            print(f"    📦 {dep}: {len(cves)} 个CVE")
            for cve in cves[:2]:  # 显示前2个
                severity_color = self.get_severity_color(cve.get('severity', 'Unknown'))
                print(f"      {severity_color}• {cve['cve_id']} ({cve.get('severity', 'Unknown')}){self.reset_color()}")
                print(f"        {cve['description'][:80]}...")
    
    def get_severity_color(self, severity):
        """获取严重性对应的颜色"""
        colors = {
            'CRITICAL': '\033[1;31m',   # 亮红色
            'HIGH': '\033[0;31m',       # 红色
            'MEDIUM': '\033[1;33m',     # 黄色
            'LOW': '\033[0;32m',        # 绿色
            'error': '\033[0;31m',      # 红色
            'warning': '\033[1;33m',    # 黄色
            'style': '\033[0;32m',      # 绿色
            'performance': '\033[0;36m', # 青色
            'portability': '\033[0;35m', # 紫色
            'information': '\033[0;37m'  # 灰色
        }
        return colors.get(severity, '')
    
    def reset_color(self):
        """重置颜色"""
        return '\033[0m'
    
    def save_scan_report(self, format_type='text'):
        """保存扫描报告"""
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        if format_type == 'text':
            report_file = f"predixy_security_scan_{timestamp}.txt"
            self.save_text_report(report_file)
        else:
            report_file = f"predixy_security_scan_{timestamp}.json"
            self.save_json_report(report_file)

        print(f"📄 扫描报告已保存: {report_file}")
        return report_file

    def save_text_report(self, report_file):
        """保存文本格式报告"""
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("=" * 60 + "\n")
            f.write("🔒 Predixy安全扫描报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"扫描时间: {self.scan_results['scan_time']}\n")
            f.write(f"项目目录: {self.project_root.absolute()}\n")
            f.write("\n")

            # CVE搜索结果
            f.write("🔍 CVE数据库搜索结果\n")
            f.write("-" * 30 + "\n")
            cve_results = self.scan_results['cve_search_results']
            if cve_results:
                f.write(f"总计找到: {len(cve_results)} 个相关CVE\n\n")

                for i, cve in enumerate(cve_results, 1):
                    f.write(f"{i}. {cve['cve_id']} ({cve.get('severity', 'Unknown')})\n")
                    f.write(f"   评分: {cve.get('score', 'N/A')}\n")
                    f.write(f"   发布时间: {cve.get('published', 'Unknown')}\n")
                    f.write(f"   搜索关键词: {cve.get('search_keyword', 'Unknown')}\n")
                    f.write(f"   相关性评分: {cve.get('relevance_score', 0)}/10\n")
                    f.write(f"   描述: {cve['description']}\n")
                    if cve.get('references'):
                        f.write(f"   参考链接: {cve['references'][0]}\n")
                    f.write("\n")
            else:
                f.write("未找到相关CVE\n\n")

            # CppCheck结果
            f.write("🔧 CppCheck代码扫描结果\n")
            f.write("-" * 30 + "\n")
            cppcheck_results = self.scan_results['cppcheck_results']
            if cppcheck_results:
                f.write(f"总计发现: {len(cppcheck_results)} 个问题\n\n")

                # 按严重性分类统计
                severity_counts = {}
                for issue in cppcheck_results:
                    severity = issue['severity']
                    severity_counts[severity] = severity_counts.get(severity, 0) + 1

                f.write("问题分类统计:\n")
                for severity, count in severity_counts.items():
                    f.write(f"  {severity}: {count} 个\n")
                f.write("\n")

                # 详细问题列表
                f.write("详细问题列表:\n")
                for i, issue in enumerate(cppcheck_results, 1):
                    f.write(f"{i}. [{issue['severity'].upper()}] {issue['msg']}\n")
                    f.write(f"   问题ID: {issue['id']}\n")
                    if issue['locations']:
                        for loc in issue['locations']:
                            file_path = os.path.relpath(loc['file'], self.project_root)
                            f.write(f"   位置: {file_path}:{loc['line']}:{loc['column']}\n")
                    if issue.get('verbose'):
                        f.write(f"   详细说明: {issue['verbose']}\n")
                    f.write("\n")
            else:
                f.write("未发现代码问题\n\n")

            # 修复建议
            f.write("💡 修复建议\n")
            f.write("-" * 15 + "\n")
            recommendations = self.scan_results['recommendations']
            if recommendations:
                f.write(f"总计建议: {len(recommendations)} 条\n\n")

                for i, rec in enumerate(recommendations, 1):
                    f.write(f"{i}. {rec['title']}\n")
                    f.write(f"   位置: {rec['location']}\n")
                    f.write(f"   严重性: {rec['severity']}\n")
                    f.write(f"   建议: {rec['suggestion']}\n")
                    f.write(f"   问题ID: {rec['issue_id']}\n")
                    f.write("\n")
            else:
                f.write("无需修复建议\n\n")

            # 检测到的依赖库版本
            f.write("📦 检测到的依赖库版本\n")
            f.write("-" * 25 + "\n")
            detected_deps = self.scan_results.get('detected_dependencies', {})
            if detected_deps:
                f.write(f"总计检测到: {len(detected_deps)} 个依赖库\n\n")

                for dep_name, dep_info in detected_deps.items():
                    f.write(f"📚 {dep_name}:\n")
                    f.write(f"   版本: {dep_info['version']}\n")
                    f.write(f"   检测方式: {dep_info['detection_method']}\n")
                    f.write(f"   详细信息: {dep_info['full_output']}\n")
                    f.write("\n")
            else:
                f.write("未检测到依赖库版本信息\n\n")

            # 依赖库CVE
            f.write("🔍 依赖库CVE检查结果\n")
            f.write("-" * 25 + "\n")
            dependency_cves = self.scan_results['dependency_cves']
            if dependency_cves:
                f.write(f"总计发现: {len(dependency_cves)} 个版本相关CVE\n\n")

                # 按依赖库分组
                by_dependency = {}
                for cve in dependency_cves:
                    dep = cve.get('dependency', 'unknown')
                    if dep not in by_dependency:
                        by_dependency[dep] = []
                    by_dependency[dep].append(cve)

                for dep, cves in by_dependency.items():
                    detected_version = cves[0].get('detected_version', 'Unknown')
                    detection_method = cves[0].get('detection_method', 'Unknown')

                    f.write(f"📦 {dep} v{detected_version} (检测方式: {detection_method})\n")
                    f.write(f"   发现 {len(cves)} 个相关CVE:\n\n")

                    for j, cve in enumerate(cves, 1):
                        f.write(f"  {j}. {cve['cve_id']} ({cve.get('severity', 'Unknown')})\n")
                        f.write(f"     评分: {cve.get('score', 'N/A')}\n")
                        f.write(f"     发布时间: {cve.get('published', 'Unknown')}\n")

                        # 版本相关性信息
                        relevance = cve.get('version_relevance', {})
                        if relevance:
                            confidence = relevance.get('confidence', 0)
                            reason = relevance.get('reason', '未知')
                            f.write(f"     版本相关性: {confidence:.1f}/1.0 - {reason}\n")

                        f.write(f"     描述: {cve['description']}\n")
                        if cve.get('references'):
                            f.write(f"     参考: {cve['references'][0]}\n")
                        f.write("\n")
                    f.write("\n")
            else:
                f.write("未发现版本相关的依赖库CVE\n\n")

            # 总结
            f.write("📊 扫描总结\n")
            f.write("-" * 15 + "\n")
            cve_count = len(self.scan_results['cve_search_results'])
            cppcheck_count = len(self.scan_results['cppcheck_results'])
            dependency_cve_count = len(self.scan_results['dependency_cves'])
            recommendation_count = len(self.scan_results['recommendations'])

            f.write(f"CVE搜索结果: {cve_count} 个\n")
            f.write(f"CppCheck问题: {cppcheck_count} 个\n")
            f.write(f"依赖库CVE: {dependency_cve_count} 个\n")
            f.write(f"修复建议: {recommendation_count} 条\n")

            # 风险评估
            high_risk_cves = len([cve for cve in self.scan_results['cve_search_results']
                                 if cve.get('severity') in ['CRITICAL', 'HIGH']])
            high_risk_issues = len([issue for issue in self.scan_results['cppcheck_results']
                                   if issue.get('severity') in ['error', 'warning']])

            total_high_risk = high_risk_cves + high_risk_issues

            f.write(f"\n风险评估:\n")
            if total_high_risk > 0:
                f.write(f"⚠️ 发现 {total_high_risk} 个高风险问题，建议优先处理\n")
            else:
                f.write(f"✅ 未发现高风险问题，安全状况良好\n")

            f.write("\n" + "=" * 60 + "\n")
            f.write("报告生成完成\n")

    def save_json_report(self, report_file):
        """保存JSON格式报告"""
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.scan_results, f, indent=2, ensure_ascii=False)
    
    def print_final_summary(self):
        """打印最终摘要"""
        print("=" * 50)
        print("📊 Predixy安全扫描总结")
        print("=" * 50)
        
        cve_count = len(self.scan_results['cve_search_results'])
        cppcheck_count = len(self.scan_results['cppcheck_results'])
        dependency_cve_count = len(self.scan_results['dependency_cves'])
        recommendation_count = len(self.scan_results['recommendations'])
        
        print(f"🔍 CVE搜索结果: {cve_count} 个")
        print(f"🔧 CppCheck问题: {cppcheck_count} 个")
        print(f"📦 依赖库CVE: {dependency_cve_count} 个")
        print(f"💡 修复建议: {recommendation_count} 条")
        
        # 风险评估
        high_risk_cves = len([cve for cve in self.scan_results['cve_search_results'] 
                             if cve.get('severity') in ['CRITICAL', 'HIGH']])
        high_risk_issues = len([issue for issue in self.scan_results['cppcheck_results'] 
                               if issue.get('severity') in ['error', 'warning']])
        
        total_high_risk = high_risk_cves + high_risk_issues
        
        if total_high_risk > 0:
            print(f"\n⚠️ 发现 {total_high_risk} 个高风险问题，建议优先处理")
        else:
            print(f"\n✅ 未发现高风险问题，安全状况良好")
        
        print("=" * 50)
    
    def run_full_scan(self, search_keywords=None, report_format='text'):
        """运行完整扫描"""
        self.print_header()

        # 默认搜索关键词
        if search_keywords is None:
            search_keywords = [
                'predixy',                    # 直接搜索predixy
                'predixy vulnerability',      # predixy漏洞
                'redis proxy vulnerability',  # Redis代理漏洞
                'redis proxy buffer overflow', # Redis代理缓冲区溢出
                'c++ redis proxy',            # C++ Redis代理
                'network proxy security'      # 网络代理安全
            ]

        # 1. CVE关键字搜索
        self.search_cve_by_keywords(search_keywords)

        # 2. CppCheck代码扫描
        self.run_cppcheck_scan()

        # 3. 依赖库CVE检查
        self.check_dependency_cves()

        # 4. 保存报告
        self.save_scan_report(format_type=report_format)

        # 5. 显示总结
        self.print_final_summary()

        return self.scan_results

def main():
    parser = argparse.ArgumentParser(description='Predixy专用安全扫描器')
    parser.add_argument('--project', default='.', help='项目根目录')
    parser.add_argument('--keywords', nargs='+', help='自定义CVE搜索关键词')
    parser.add_argument('--format', choices=['text', 'json'], default='text',
                       help='报告格式 (默认: text)')

    args = parser.parse_args()

    scanner = PredixySecurityScanner(project_root=args.project)
    scanner.run_full_scan(search_keywords=args.keywords, report_format=args.format)

if __name__ == "__main__":
    main()
