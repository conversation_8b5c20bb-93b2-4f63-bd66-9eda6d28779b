Checking src/AcceptConnection.cpp ...
src/Predixy.h:10:0: information: Include file: <stdio.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <stdio.h>
^
src/Predixy.h:11:0: information: Include file: <string.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string.h>
^
src/Predixy.h:12:0: information: Include file: <strings.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <strings.h>
^
src/Predixy.h:13:0: information: Include file: <limits.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <limits.h>
^
src/Predixy.h:14:0: information: Include file: <errno.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <errno.h>
^
src/Predixy.h:15:0: information: Include file: <unistd.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <unistd.h>
^
src/Predixy.h:16:0: information: Include file: <sys/uio.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/uio.h>
^
src/Common.h:10:0: information: Include file: <limits.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <limits.h>
^
src/Sync.h:12:0: information: Include file: <atomic> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <atomic>
^
src/Sync.h:13:0: information: Include file: <mutex> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <mutex>
^
src/HashFunc.h:10:0: information: Include file: <string.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string.h>
^
src/HashFunc.h:11:0: information: Include file: <stdint.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <stdint.h>
^
src/ID.h:11:0: information: Include file: <vector> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <vector>
^
src/IOVec.h:11:0: information: Include file: <sys/uio.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/uio.h>
^
src/Util.h:10:0: information: Include file: <stdio.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <stdio.h>
^
src/Util.h:11:0: information: Include file: <string.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string.h>
^
src/Util.h:12:0: information: Include file: <strings.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <strings.h>
^
src/Util.h:13:0: information: Include file: <errno.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <errno.h>
^
src/Util.h:14:0: information: Include file: <chrono> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <chrono>
^
src/PString.h:10:0: information: Include file: <string.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string.h>
^
src/PString.h:11:0: information: Include file: <strings.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <strings.h>
^
src/PString.h:12:0: information: Include file: <stdio.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <stdio.h>
^
src/PString.h:13:0: information: Include file: <stdarg.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <stdarg.h>
^
src/PString.h:14:0: information: Include file: <string> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string>
^
src/Timer.h:10:0: information: Include file: <map> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <map>
^
src/Timer.h:11:0: information: Include file: <chrono> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <chrono>
^
src/Exception.h:10:0: information: Include file: <exception> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <exception>
^
src/Exception.h:11:0: information: Include file: <stdarg.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <stdarg.h>
^
src/Exception.h:12:0: information: Include file: <stdio.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <stdio.h>
^
src/Logger.h:10:0: information: Include file: <stdarg.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <stdarg.h>
^
src/Logger.h:11:0: information: Include file: <vector> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <vector>
^
src/Logger.h:12:0: information: Include file: <atomic> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <atomic>
^
src/Logger.h:13:0: information: Include file: <mutex> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <mutex>
^
src/Logger.h:14:0: information: Include file: <condition_variable> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <condition_variable>
^
src/Logger.h:15:0: information: Include file: <thread> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <thread>
^
src/Alloc.h:10:0: information: Include file: <stdlib.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <stdlib.h>
^
src/Alloc.h:11:0: information: Include file: <iostream> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <iostream>
^
src/Alloc.h:12:0: information: Include file: <thread> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <thread>
^
src/Command.h:10:0: information: Include file: <unordered_map> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <unordered_map>
^
src/Socket.h:10:0: information: Include file: <unistd.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <unistd.h>
^
src/Socket.h:11:0: information: Include file: <sys/types.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/types.h>
^
src/Socket.h:12:0: information: Include file: <sys/socket.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/socket.h>
^
src/Socket.h:13:0: information: Include file: <sys/un.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/un.h>
^
src/Socket.h:14:0: information: Include file: <sys/uio.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/uio.h>
^
src/Socket.h:15:0: information: Include file: <netinet/in.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <netinet/in.h>
^
src/Socket.h:16:0: information: Include file: <netinet/ip.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <netinet/ip.h>
^
src/Socket.h:17:0: information: Include file: <netinet/tcp.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <netinet/tcp.h>
^
src/Socket.h:18:0: information: Include file: <fcntl.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <fcntl.h>
^
src/Socket.h:19:0: information: Include file: <arpa/inet.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <arpa/inet.h>
^
src/Socket.h:20:0: information: Include file: <netdb.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <netdb.h>
^
src/Socket.h:21:0: information: Include file: <errno.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <errno.h>
^
src/Transaction.h:10:0: information: Include file: <stdint.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <stdint.h>
^
src/RequestParser.h:10:0: information: Include file: <map> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <map>
^
src/Conf.h:10:0: information: Include file: <limits.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <limits.h>
^
src/Conf.h:11:0: information: Include file: <string.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string.h>
^
src/Conf.h:12:0: information: Include file: <strings.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <strings.h>
^
src/Conf.h:13:0: information: Include file: <string> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string>
^
src/Conf.h:14:0: information: Include file: <map> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <map>
^
src/Conf.h:15:0: information: Include file: <set> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <set>
^
src/Conf.h:16:0: information: Include file: <vector> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <vector>
^
src/Conf.h:17:0: information: Include file: <bitset> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <bitset>
^
src/ConfParser.h:10:0: information: Include file: <PString.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <PString.h>
^
src/ConfParser.h:11:0: information: Include file: <string> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string>
^
src/ConfParser.h:12:0: information: Include file: <set> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <set>
^
src/ConfParser.h:13:0: information: Include file: <vector> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <vector>
^
src/ConfParser.h:14:0: information: Include file: <fstream> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <fstream>
^
src/Auth.h:10:0: information: Include file: <map> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <map>
^
src/Auth.h:11:0: information: Include file: <set> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <set>
^
src/Auth.h:12:0: information: Include file: <vector> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <vector>
^
src/Enums.h:10:0: information: Include file: <PString.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <PString.h>
^
src/Enums.h:11:0: information: Include file: <strings.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <strings.h>
^
src/Handler.h:10:0: information: Include file: <vector> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <vector>
^
src/LatencyMonitor.h:10:0: information: Include file: <algorithm> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <algorithm>
^
src/LatencyMonitor.h:11:0: information: Include file: <vector> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <vector>
^
src/LatencyMonitor.h:12:0: information: Include file: <map> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <map>
^
src/ConnectConnectionPool.h:10:0: information: Include file: <vector> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <vector>
^
src/ConnectSocket.h:11:0: information: Include file: <string> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string>
^
src/Server.h:10:0: information: Include file: <string> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string>
^
src/DC.h:10:0: information: Include file: <vector> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <vector>
^
src/DC.h:11:0: information: Include file: <map> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <map>
^
src/Proxy.h:10:0: information: Include file: <vector> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <vector>
^
src/ServerPool.h:10:0: information: Include file: <string> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string>
^
src/ServerPool.h:11:0: information: Include file: <map> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <map>
^
src/ServerPool.h:14:0: information: Include file: <vector> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <vector>
^
src/ClusterServerPool.h:10:0: information: Include file: <vector> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <vector>
^
src/ClusterServerPool.h:11:0: information: Include file: <string> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string>
^
src/ClusterServerPool.h:12:0: information: Include file: <map> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <map>
^
src/StandaloneServerPool.h:10:0: information: Include file: <map> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <map>
^
src/AcceptConnection.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/ServerPool.h:108:5: warning: Member variable 'ServerPool::mServerTimeout' is not initialized in the constructor. [uninitMemberVar]
    ServerPool(Proxy* p, int type, T* sub = nullptr):
    ^
src/ServerPool.h:108:5: warning: Member variable 'ServerPool::mKeepAlive' is not initialized in the constructor. [uninitMemberVar]
    ServerPool(Proxy* p, int type, T* sub = nullptr):
    ^
src/PString.h:185:5: warning: Member variable 'SString < Const :: MaxAddrLen >::mBuf' is not initialized in the copy constructor. [uninitMemberVar]
    SString(const SString& str)
    ^
src/PString.h:185:5: warning: Member variable 'SString < Const :: MaxServNameLen >::mBuf' is not initialized in the copy constructor. [uninitMemberVar]
    SString(const SString& str)
    ^
src/ConnectSocket.h:27:10: warning: The class 'ConnectSocket' defines member function with name 'close' also defined in its parent class 'Socket'. [duplInheritedMember]
    void close();
         ^
src/Socket.h:59:10: note: Parent function 'Socket::close'
    void close();
         ^
src/ConnectSocket.h:27:10: note: Derived function 'ConnectSocket::close'
    void close();
         ^
src/AcceptConnection.cpp:27:24: warning: The class 'AcceptConnection' defines member function with name 'close' also defined in its parent class 'Socket'. [duplInheritedMember]
void AcceptConnection::close()
                       ^
src/Socket.h:59:10: note: Parent function 'Socket::close'
    void close();
         ^
src/AcceptConnection.cpp:27:24: note: Derived function 'AcceptConnection::close'
void AcceptConnection::close()
                       ^
src/ClusterServerPool.h:29:13: warning: The class 'ClusterServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'. [duplInheritedMember]
    Server* getServer(Handler* h, Request* req, const String& key) const;
            ^
src/ServerPool.h:89:13: note: Parent function 'ServerPool::getServer'
    Server* getServer(Handler* h, Request* req, const String& key) const
            ^
src/ClusterServerPool.h:29:13: note: Derived function 'ClusterServerPool::getServer'
    Server* getServer(Handler* h, Request* req, const String& key) const;
            ^
src/ClusterServerPool.h:30:10: warning: The class 'ClusterServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'. [duplInheritedMember]
    void refreshRequest(Handler* h);
         ^
src/ServerPool.h:97:10: note: Parent function 'ServerPool::refreshRequest'
    void refreshRequest(Handler* h)
         ^
src/ClusterServerPool.h:30:10: note: Derived function 'ClusterServerPool::refreshRequest'
    void refreshRequest(Handler* h);
         ^
src/ClusterServerPool.h:31:10: warning: The class 'ClusterServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'. [duplInheritedMember]
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/ServerPool.h:101:10: note: Parent function 'ServerPool::handleResponse'
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/ClusterServerPool.h:31:10: note: Derived function 'ClusterServerPool::handleResponse'
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/ClusterServerPool.h:37:13: warning: The class 'ClusterServerPool' defines member function with name 'iter' also defined in its parent class 'ServerPool'. [duplInheritedMember]
    Server* iter(int& cursor) const
            ^
src/ServerPool.h:93:13: note: Parent function 'ServerPool::iter'
    Server* iter(int& cursor) const
            ^
src/ClusterServerPool.h:37:13: note: Derived function 'ClusterServerPool::iter'
    Server* iter(int& cursor) const
            ^
src/StandaloneServerPool.h:22:13: warning: The class 'StandaloneServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'. [duplInheritedMember]
    Server* getServer(Handler* h, Request* req, const String& key) const;
            ^
src/ServerPool.h:89:13: note: Parent function 'ServerPool::getServer'
    Server* getServer(Handler* h, Request* req, const String& key) const
            ^
src/StandaloneServerPool.h:22:13: note: Derived function 'StandaloneServerPool::getServer'
    Server* getServer(Handler* h, Request* req, const String& key) const;
            ^
src/StandaloneServerPool.h:23:13: warning: The class 'StandaloneServerPool' defines member function with name 'iter' also defined in its parent class 'ServerPool'. [duplInheritedMember]
    Server* iter(int& cursor) const
            ^
src/ServerPool.h:93:13: note: Parent function 'ServerPool::iter'
    Server* iter(int& cursor) const
            ^
src/StandaloneServerPool.h:23:13: note: Derived function 'StandaloneServerPool::iter'
    Server* iter(int& cursor) const
            ^
src/StandaloneServerPool.h:27:10: warning: The class 'StandaloneServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'. [duplInheritedMember]
    void refreshRequest(Handler* h);
         ^
src/ServerPool.h:97:10: note: Parent function 'ServerPool::refreshRequest'
    void refreshRequest(Handler* h)
         ^
src/StandaloneServerPool.h:27:10: note: Derived function 'StandaloneServerPool::refreshRequest'
    void refreshRequest(Handler* h);
         ^
src/StandaloneServerPool.h:28:10: warning: The class 'StandaloneServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'. [duplInheritedMember]
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/ServerPool.h:101:10: note: Parent function 'ServerPool::handleResponse'
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/StandaloneServerPool.h:28:10: note: Derived function 'StandaloneServerPool::handleResponse'
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/HashFunc.h:28:5: style: Class 'Hash' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    Hash(Type t = None):
    ^
src/ID.h:64:5: style: Class 'IDUnique' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    IDUnique(int sz = 0):
    ^
src/Util.h:33:5: style: Class 'StrErrorImpl' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    StrErrorImpl(int err)
    ^
src/PString.h:24:5: style: Class 'String' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    String(const char* str):
    ^
src/PString.h:34:5: style: Class 'String' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    String(const std::string& s):
    ^
src/Timer.h:17:5: style: Class 'TimerPoint' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    TimerPoint(const char* key);
    ^
src/Timer.h:56:5: style: Class 'Timer' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    Timer(TimerPoint* key):
    ^
src/Logger.h:58:5: style: Class 'Logger' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    Logger(int maxLogUnitNum = 1024);
    ^
src/Socket.h:52:5: style: Class 'Socket' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    Socket(int fd = -1);
    ^
src/Buffer.h:211:5: style: Class 'SegmentStr' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SegmentStr(const Segment& seg)
    ^
src/Request.h:56:5: style: Class 'Request' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    Request(AcceptConnection* c);
    ^
src/Request.h:57:5: style: Class 'Request' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    Request(GenericCode code);
    ^
src/Response.h:49:5: style: Class 'Response' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    Response(GenericCode code);
    ^
src/Distribution.h:20:5: style: Class 'Distribution' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    Distribution(Type t = None):
    ^
src/ConfParser.h:73:5: style: Class 'ConfParser' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    ConfParser(int maxNodeDepth = 10);
    ^
src/Auth.h:18:5: style: Class 'Auth' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    Auth(int mode = Command::Read|Command::Write|Command::Admin);
    ^
src/Auth.h:19:5: style: Class 'Auth' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    Auth(const AuthConf& conf);
    ^
src/Enums.h:68:5: style: Class 'ServerPoolRefreshMethod' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    ServerPoolRefreshMethod(Type t = None):
    ^
src/ClusterServerPool.h:20:5: style: Class 'ClusterServerPool' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    ClusterServerPool(Proxy* p);
    ^
src/StandaloneServerPool.h:19:5: style: Class 'StandaloneServerPool' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    StandaloneServerPool(Proxy* p);
    ^
src/Handler.h:24:5: style: Class 'Handler' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    Handler(Proxy* p);
    ^
src/Enums.h:25:5: style: Class 'EnumBase < ServerPoolRefreshMethod >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    EnumBase(int t):
    ^
src/Alloc.h:171:5: style: Class 'SharePtr < Buffer >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SharePtr(T* obj):
    ^
src/Alloc.h:171:5: style: Class 'SharePtr < Request >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SharePtr(T* obj):
    ^
src/Alloc.h:171:5: style: Class 'SharePtr < Response >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SharePtr(T* obj):
    ^
src/Alloc.h:171:5: style: Class 'SharePtr < AcceptConnection >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SharePtr(T* obj):
    ^
src/PString.h:171:5: style: Class 'SString < Const :: MaxAddrLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const char* str):
    ^
src/PString.h:181:5: style: Class 'SString < Const :: MaxAddrLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const std::string& str)
    ^
src/PString.h:190:5: style: Class 'SString < Const :: MaxAddrLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const T& str)
    ^
src/PString.h:171:5: style: Class 'SString < Const :: MaxServNameLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const char* str):
    ^
src/PString.h:181:5: style: Class 'SString < Const :: MaxServNameLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const std::string& str)
    ^
src/PString.h:190:5: style: Class 'SString < Const :: MaxServNameLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const T& str)
    ^
src/ClusterServerPool.h:21:6: style: The destructor '~ClusterServerPool' overrides a destructor in a base class but is not marked with a 'override' specifier. [missingOverride]
    ~ClusterServerPool();
     ^
src/ServerPool.h:28:14: note: Virtual destructor in base class
    virtual ~ServerPool();
             ^
src/ClusterServerPool.h:21:6: note: Destructor in derived class
    ~ClusterServerPool();
     ^
src/StandaloneServerPool.h:20:6: style: The destructor '~StandaloneServerPool' overrides a destructor in a base class but is not marked with a 'override' specifier. [missingOverride]
    ~StandaloneServerPool();
     ^
src/ServerPool.h:28:14: note: Virtual destructor in base class
    virtual ~ServerPool();
             ^
src/StandaloneServerPool.h:20:6: note: Destructor in derived class
    ~StandaloneServerPool();
     ^
src/PString.h:130:32: style: Expression is always false because 'else if' condition matches previous condition at line 128. [multiCondition]
            } else if (mDat[0] == '+') {
                               ^
src/PString.h:242:21: style: Return value 'mLen==len' is always false [knownConditionTrueFalse]
        return mLen == len;
                    ^
src/PString.h:241:14: note: mLen is assigned 'len<Const::MaxAddrLen?len:Const::MaxAddrLen' here.
        mLen = len < Size ? len : Size;
             ^
src/PString.h:242:21: note: Return value 'mLen==len' is always false
        return mLen == len;
                    ^
src/Request.h:123:58: style: C-style pointer casting [cstyleCast]
        return isLeader() ? const_cast<Request*>(this) : (Request*)mLeader;
                                                         ^
src/ServerPool.h:156:29: style: C-style pointer casting [cstyleCast]
        ServerPool(p, type, (ServerPoolTmpl<T>*)this)
                            ^
src/Alloc.h:61:22: style: C-style pointer casting [cstyleCast]
                new ((void*)obj) T(args...);
                     ^
src/Alloc.h:70:39: style: C-style pointer casting [cstyleCast]
                    ::operator delete((void*)obj);
                                      ^
src/Alloc.h:111:31: style: C-style pointer casting [cstyleCast]
            ::operator delete((void*)obj);
                              ^
src/Deque.h:98:32: style: C-style pointer casting [cstyleCast]
            static_cast<Node*>((T*)mTail)->concat(p, Idx);
                               ^
src/Deque.h:46:52: style: C-style pointer casting [cstyleCast]
        static_cast<DequeNode*>(obj)->mPrev[idx] = (T*)this;
                                                   ^
src/List.h:71:32: style: C-style pointer casting [cstyleCast]
            static_cast<Node*>((T*)mTail)->concat(p, Idx);
                               ^
src/List.h:93:28: style: C-style pointer casting [cstyleCast]
            Node* n = node((T*)obj);
                           ^
src/PString.h:127:15: style: The comparison 'i > 0' is always false. [knownConditionTrueFalse]
        if (i > 0) {
              ^
src/PString.h:125:17: note: 'i' is assigned value '0' here.
        int i = 0;
                ^
src/PString.h:127:15: note: The comparison 'i > 0' is always false.
        if (i > 0) {
              ^
src/Deque.h:119:14: style: Local variable 'prev' shadows outer function [shadowFunction]
        auto prev = n->prev(Idx);
             ^
src/Deque.h:81:7: note: Shadowed declaration
    P prev(T* obj)
      ^
src/Deque.h:119:14: note: Shadow variable
        auto prev = n->prev(Idx);
             ^
src/Deque.h:120:14: style: Local variable 'next' shadows outer function [shadowFunction]
        auto next = n->next(Idx);
             ^
src/Deque.h:85:7: note: Shadowed declaration
    P next(T* obj)
      ^
src/Deque.h:120:14: note: Shadow variable
        auto next = n->next(Idx);
             ^
src/Deque.h:143:18: style: Local variable 'next' shadows outer function [shadowFunction]
        if (auto next = n->next(Idx)) {
                 ^
src/Deque.h:85:7: note: Shadowed declaration
    P next(T* obj)
      ^
src/Deque.h:143:18: note: Shadow variable
        if (auto next = n->next(Idx)) {
                 ^
src/Deque.h:144:22: style: Local variable 'prev' shadows outer function [shadowFunction]
            if (auto prev = n->prev(Idx)) {
                     ^
src/Deque.h:81:7: note: Shadowed declaration
    P prev(T* obj)
      ^
src/Deque.h:144:22: note: Shadow variable
            if (auto prev = n->prev(Idx)) {
                     ^
src/AcceptConnection.cpp:74:14: style: Variable 'res' can be declared as pointer to const [constVariablePointer]
        auto res = req->getResponse();
             ^
src/Handler.h:58:33: style: Parameter 'serv' can be declared as pointer to const [constParameterPointer]
    int getPendRequests(Server* serv) const
                                ^
src/Handler.h:65:37: style: Parameter 'serv' can be declared as pointer to const [constParameterPointer]
    void addServerReadStats(Server* serv, int num)
                                    ^
src/Handler.h:70:38: style: Parameter 'serv' can be declared as pointer to const [constParameterPointer]
    void addServerWriteStats(Server* serv, int num)
                                     ^
src/DC.h:31:18: style: Parameter 'oth' can be declared as pointer to const [constParameterPointer]
    void set(DC* oth, const ReadPolicyConf& c)
                 ^
src/DC.h:36:29: style: Parameter 'oth' can be declared as pointer to const [constParameterPointer]
    int getReadPriority(DC* oth) const
                            ^
src/DC.h:40:27: style: Parameter 'oth' can be declared as pointer to const [constParameterPointer]
    int getReadWeight(DC* oth) const
                          ^
src/DC.h:44:43: style: Parameter 'oth' can be declared as pointer to const [constParameterPointer]
    const DCReadPolicy& getReadPolicy(DC* oth) const
                                          ^
src/Enums.h:48:47: style: Consider using std::find_if algorithm instead of a raw loop. [useStlAlgorithm]
            if (strcasecmp(i.name, str) == 0) {
                                              ^
src/EpollMultiplexor.h:10:0: information: Include file: <unistd.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <unistd.h>
^
src/EpollMultiplexor.h:11:0: information: Include file: <sys/epoll.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/epoll.h>
^
Checking src/AcceptConnection.cpp: _EPOLL_...
src/EpollMultiplexor.h:23:6: style: The destructor '~EpollMultiplexor' overrides a destructor in a base class but is not marked with a 'override' specifier. [missingOverride]
    ~EpollMultiplexor();
     ^
src/Multiplexor.h:24:14: note: Virtual destructor in base class
    virtual ~MultiplexorBase() {}
             ^
src/EpollMultiplexor.h:23:6: note: Destructor in derived class
    ~EpollMultiplexor();
     ^
Checking src/AcceptConnection.cpp: _GNU_SOURCE...
src/KqueueMultiplexor.h:10:0: information: Include file: <unistd.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <unistd.h>
^
src/KqueueMultiplexor.h:11:0: information: Include file: <sys/types.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/types.h>
^
src/KqueueMultiplexor.h:12:0: information: Include file: <sys/event.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/event.h>
^
src/KqueueMultiplexor.h:13:0: information: Include file: <sys/time.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/time.h>
^
Checking src/AcceptConnection.cpp: _KQUEUE_...
src/KqueueMultiplexor.h:25:6: style: The destructor '~KqueueMultiplexor' overrides a destructor in a base class but is not marked with a 'override' specifier. [missingOverride]
    ~KqueueMultiplexor();
     ^
src/Multiplexor.h:24:14: note: Virtual destructor in base class
    virtual ~MultiplexorBase() {}
             ^
src/KqueueMultiplexor.h:25:6: note: Destructor in derived class
    ~KqueueMultiplexor();
     ^
src/PollMultiplexor.h:10:0: information: Include file: <unistd.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <unistd.h>
^
src/PollMultiplexor.h:11:0: information: Include file: <poll.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <poll.h>
^
src/PollMultiplexor.h:12:0: information: Include file: <vector> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <vector>
^
Checking src/AcceptConnection.cpp: _POLL_...
src/PollMultiplexor.h:21:5: style: Class 'PollMultiplexor' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    PollMultiplexor(int maxFdSize = 81920);
    ^
src/PollMultiplexor.h:22:6: style: The destructor '~PollMultiplexor' overrides a destructor in a base class but is not marked with a 'override' specifier. [missingOverride]
    ~PollMultiplexor();
     ^
src/Multiplexor.h:24:14: note: Virtual destructor in base class
    virtual ~MultiplexorBase() {}
             ^
src/PollMultiplexor.h:22:6: note: Destructor in derived class
    ~PollMultiplexor();
     ^
Checking src/AcceptConnection.cpp: _PREDIXY_SINGLE_THREAD_...
src/ServerPool.h:85:30: style: Variable 'lck' is assigned a value that is never used. [unreadVariable]
        UniqueLock<Mutex> lck(mMtx);
                             ^
Checking src/AcceptConnection.cpp: _PREDIXY_TIMER_STATS_...
1/42 files checked 2% done
Checking src/AcceptSocket.cpp ...
src/AcceptSocket.cpp:19:31: style: C-style pointer casting [cstyleCast]
            sockaddr_in* in = (sockaddr_in*)addr;
                              ^
src/AcceptSocket.cpp:20:32: style: C-style pointer casting [cstyleCast]
            inet_ntop(AF_INET, (void*)&in->sin_addr, host, sizeof(host));
                               ^
src/AcceptSocket.cpp:28:32: style: C-style pointer casting [cstyleCast]
            sockaddr_in6* in = (sockaddr_in6*)addr;
                               ^
src/AcceptSocket.cpp:29:33: style: C-style pointer casting [cstyleCast]
            inet_ntop(AF_INET6, (void*)&in->sin6_addr, host, sizeof(host));
                                ^
2/42 files checked 3% done
Checking src/Alloc.cpp ...
src/Alloc.h:171:5: style: Class 'SharePtr' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SharePtr(T* obj):
    ^
Checking src/Alloc.cpp: _GNU_SOURCE...
Checking src/Alloc.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/Alloc.cpp: _PREDIXY_TIMER_STATS_...
3/42 files checked 3% done
Checking src/Auth.cpp ...
src/Auth.cpp:7:0: information: Include file: <algorithm> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <algorithm>
^
src/PString.h:308:35: portability: Undefined behaviour, when 'n' is -3 the pointer arithmetic 'mBuf+n' is out of bounds. [pointerOutOfBoundsCond]
                    snprintf(mBuf + n, Size - n, "\\x%02X", (int)dat[i]);
                                  ^
src/PString.h:307:34: note: Assuming that condition 'n+4<Size' is not redundant
                } else if (n + 4 < Size) {
                                 ^
src/PString.h:308:35: note: Pointer arithmetic overflow
                    snprintf(mBuf + n, Size - n, "\\x%02X", (int)dat[i]);
                                  ^
src/PString.h:185:5: warning: Member variable 'SString::mBuf' is not initialized in the copy constructor. [uninitMemberVar]
    SString(const SString& str)
    ^
src/Auth.cpp:27:9: warning: Class 'Auth' does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s). [noCopyConstructor]
        mReadKeyPrefix = new KeyPrefixSet(conf.readKeyPrefix.begin(), conf.readKeyPrefix.end());
        ^
src/Auth.cpp:27:9: warning: Class 'Auth' does not have a operator= which is recommended since it has dynamic memory/resource allocation(s). [noOperatorEq]
        mReadKeyPrefix = new KeyPrefixSet(conf.readKeyPrefix.begin(), conf.readKeyPrefix.end());
        ^
src/PString.h:171:5: style: Class 'SString' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const char* str):
    ^
src/PString.h:181:5: style: Class 'SString' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const std::string& str)
    ^
src/PString.h:190:5: style: Class 'SString' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const T& str)
    ^
src/Auth.cpp:40:5: error: Memory leak: kp [memleak]
    }
    ^
src/Auth.cpp:88:16: style: Variable 'i' can be declared as reference to const [constVariableReference]
    for (auto& i : mAuthMap) {
               ^
src/Auth.cpp:53:32: style: Parameter 'req' can be declared as pointer to const [constParameterPointer]
bool Auth::permission(Request* req, const String& key) const
                               ^
Checking src/Auth.cpp: _EPOLL_...
Checking src/Auth.cpp: _GNU_SOURCE...
Checking src/Auth.cpp: _KQUEUE_...
Checking src/Auth.cpp: _POLL_...
src/Auth.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
Checking src/Auth.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/Auth.cpp: _PREDIXY_TIMER_STATS_...
4/42 files checked 4% done
Checking src/Buffer.cpp ...
src/Buffer.cpp:14:9: warning: Member variable 'Buffer::mDat' is not initialized in the constructor. [uninitMemberVarPrivate]
Buffer::Buffer():
        ^
src/Buffer.cpp:339:10: style: Local variable 'end' shadows outer function [shadowFunction]
    bool end = false;
         ^
src/Buffer.h:183:16: note: Shadowed declaration
    BufferPos& end()
               ^
src/Buffer.cpp:339:10: note: Shadow variable
    bool end = false;
         ^
src/Buffer.cpp:365:10: style: Local variable 'end' shadows outer function [shadowFunction]
    bool end = false;
         ^
src/Buffer.h:183:16: note: Shadowed declaration
    BufferPos& end()
               ^
src/Buffer.cpp:365:10: note: Shadow variable
    bool end = false;
         ^
src/Buffer.cpp:218:13: style: Variable 'buf' can be declared as pointer to const [constVariablePointer]
    Buffer* buf = mBegin.buf;
            ^
Checking src/Buffer.cpp: _GNU_SOURCE...
Checking src/Buffer.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/Buffer.cpp: _PREDIXY_TIMER_STATS_...
5/42 files checked 7% done
Checking src/ClusterNodesParser.cpp ...
src/ClusterNodesParser.cpp:7:0: information: Include file: <PString.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <PString.h>
^
src/ClusterNodesParser.cpp:11:21: warning: Member variable 'ClusterNodesParser::mRole' is not initialized in the constructor. [uninitMemberVar]
ClusterNodesParser::ClusterNodesParser():
                    ^
src/ClusterNodesParser.cpp:11:21: warning: Member variable 'ClusterNodesParser::mFieldCnt' is not initialized in the constructor. [uninitMemberVar]
ClusterNodesParser::ClusterNodesParser():
                    ^
src/ClusterNodesParser.cpp:11:21: warning: Member variable 'ClusterNodesParser::mSlotBegin' is not initialized in the constructor. [uninitMemberVar]
ClusterNodesParser::ClusterNodesParser():
                    ^
src/ClusterNodesParser.cpp:11:21: warning: Member variable 'ClusterNodesParser::mSlotEnd' is not initialized in the constructor. [uninitMemberVar]
ClusterNodesParser::ClusterNodesParser():
                    ^
src/PString.h:185:5: warning: Member variable 'SString < NodeIdLen >::mBuf' is not initialized in the copy constructor. [uninitMemberVar]
    SString(const SString& str)
    ^
src/PString.h:185:5: warning: Member variable 'SString < AddrLen >::mBuf' is not initialized in the copy constructor. [uninitMemberVar]
    SString(const SString& str)
    ^
src/PString.h:185:5: warning: Member variable 'SString < FlagsLen >::mBuf' is not initialized in the copy constructor. [uninitMemberVar]
    SString(const SString& str)
    ^
src/PString.h:171:5: style: Class 'SString < NodeIdLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const char* str):
    ^
src/PString.h:181:5: style: Class 'SString < NodeIdLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const std::string& str)
    ^
src/PString.h:190:5: style: Class 'SString < NodeIdLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const T& str)
    ^
src/PString.h:171:5: style: Class 'SString < AddrLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const char* str):
    ^
src/PString.h:181:5: style: Class 'SString < AddrLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const std::string& str)
    ^
src/PString.h:190:5: style: Class 'SString < AddrLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const T& str)
    ^
src/PString.h:171:5: style: Class 'SString < FlagsLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const char* str):
    ^
src/PString.h:181:5: style: Class 'SString < FlagsLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const std::string& str)
    ^
src/PString.h:190:5: style: Class 'SString < FlagsLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const T& str)
    ^
Checking src/ClusterNodesParser.cpp: _EPOLL_...
Checking src/ClusterNodesParser.cpp: _GNU_SOURCE...
Checking src/ClusterNodesParser.cpp: _KQUEUE_...
Checking src/ClusterNodesParser.cpp: _POLL_...
src/ClusterNodesParser.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
Checking src/ClusterNodesParser.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/ClusterNodesParser.cpp: _PREDIXY_TIMER_STATS_...
6/42 files checked 9% done
Checking src/ClusterServerPool.cpp ...
src/ClusterServerPool.cpp:7:0: information: Include file: <time.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <time.h>
^
src/ServerGroup.h:10:0: information: Include file: <string> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string>
^
src/ServerGroup.h:11:0: information: Include file: <deque> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <deque>
^
src/ServerGroup.h:12:0: information: Include file: <vector> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <vector>
^
src/ClusterServerPool.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/AcceptConnection.h:36:10: warning: The class 'AcceptConnection' defines member function with name 'close' also defined in its parent class 'Socket'. [duplInheritedMember]
    void close();
         ^
src/Socket.h:59:10: note: Parent function 'Socket::close'
    void close();
         ^
src/AcceptConnection.h:36:10: note: Derived function 'AcceptConnection::close'
    void close();
         ^
src/ClusterServerPool.cpp:36:28: warning: The class 'ClusterServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'. [duplInheritedMember]
Server* ClusterServerPool::getServer(Handler* h, Request* req, const String& key) const
                           ^
src/ServerPool.h:89:13: note: Parent function 'ServerPool::getServer'
    Server* getServer(Handler* h, Request* req, const String& key) const
            ^
src/ClusterServerPool.cpp:36:28: note: Derived function 'ClusterServerPool::getServer'
Server* ClusterServerPool::getServer(Handler* h, Request* req, const String& key) const
                           ^
src/ClusterServerPool.cpp:82:25: warning: The class 'ClusterServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'. [duplInheritedMember]
void ClusterServerPool::refreshRequest(Handler* h)
                        ^
src/ServerPool.h:97:10: note: Parent function 'ServerPool::refreshRequest'
    void refreshRequest(Handler* h)
         ^
src/ClusterServerPool.cpp:82:25: note: Derived function 'ClusterServerPool::refreshRequest'
void ClusterServerPool::refreshRequest(Handler* h)
                        ^
src/ClusterServerPool.cpp:89:25: warning: The class 'ClusterServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'. [duplInheritedMember]
void ClusterServerPool::handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res)
                        ^
src/ServerPool.h:101:10: note: Parent function 'ServerPool::handleResponse'
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/ClusterServerPool.cpp:89:25: note: Derived function 'ClusterServerPool::handleResponse'
void ClusterServerPool::handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res)
                        ^
src/ServerGroup.h:23:12: performance: Function 'name()' should return member 'mName' by const reference. [returnByReference]
    String name() const {return mName;}
           ^
src/ClusterServerPool.cpp:74:16: style: Variable 'sc' can be declared as reference to const [constVariableReference]
    for (auto& sc : conf.servers) {
               ^
src/ClusterServerPool.cpp:48:18: style: Variable 'g' can be declared as pointer to const [constVariablePointer]
    ServerGroup* g = mSlots[i];
                 ^
src/ClusterServerPool.cpp:55:65: style: Parameter 'old' can be declared as pointer to const [constParameterPointer]
Server* ClusterServerPool::redirect(const String& addr, Server* old) const
                                                                ^
src/ClusterServerPool.cpp:89:71: style: Parameter 's' can be declared as pointer to const [constParameterPointer]
void ClusterServerPool::handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res)
                                                                      ^
Checking src/ClusterServerPool.cpp: _EPOLL_...
Checking src/ClusterServerPool.cpp: _GNU_SOURCE...
Checking src/ClusterServerPool.cpp: _KQUEUE_...
Checking src/ClusterServerPool.cpp: _POLL_...
Checking src/ClusterServerPool.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/ClusterServerPool.cpp: _PREDIXY_TIMER_STATS_...
7/42 files checked 12% done
Checking src/Command.cpp ...
src/Command.cpp:7:0: information: Include file: <PString.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <PString.h>
^
src/Command.cpp:8:0: information: Include file: <strings.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <strings.h>
^
src/Command.cpp:9:0: information: Include file: <map> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <map>
^
Checking src/Command.cpp: _EPOLL_...
Checking src/Command.cpp: _GNU_SOURCE...
Checking src/Command.cpp: _KQUEUE_...
Checking src/Command.cpp: _POLL_...
src/Command.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
Checking src/Command.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/Command.cpp: _PREDIXY_TIMER_STATS_...
8/42 files checked 17% done
Checking src/Conf.cpp ...
src/Conf.cpp:7:0: information: Include file: <ctype.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <ctype.h>
^
src/Conf.cpp:8:0: information: Include file: <iostream> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <iostream>
^
src/Conf.cpp:9:0: information: Include file: <sstream> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sstream>
^
src/Conf.cpp:10:0: information: Include file: <fstream> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <fstream>
^
src/LogFileSink.h:10:0: information: Include file: <stdio.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <stdio.h>
^
src/LogFileSink.h:11:0: information: Include file: <atomic> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <atomic>
^
src/LogFileSink.h:12:0: information: Include file: <string> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string>
^
src/Conf.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/Conf.cpp:43:7: warning: Member variable 'Conf::mStandaloneServerPool' is not initialized in the constructor. [uninitMemberVar]
Conf::Conf():
      ^
src/Conf.cpp:127:29: style: Local variable 'clusterServerPool' shadows outer function [shadowFunction]
    const ConfParser::Node* clusterServerPool = nullptr;
                            ^
src/Conf.h:176:34: note: Shadowed declaration
    const ClusterServerPoolConf& clusterServerPool() const
                                 ^
src/Conf.cpp:127:29: note: Shadow variable
    const ConfParser::Node* clusterServerPool = nullptr;
                            ^
src/Conf.cpp:128:29: style: Local variable 'standaloneServerPool' shadows outer function [shadowFunction]
    const ConfParser::Node* standaloneServerPool = nullptr;
                            ^
src/Conf.h:180:37: note: Shadowed declaration
    const StandaloneServerPoolConf& standaloneServerPool() const
                                    ^
src/Conf.cpp:128:29: note: Shadow variable
    const ConfParser::Node* standaloneServerPool = nullptr;
                            ^
src/Conf.cpp:130:42: style: Local variable 'latencyMonitors' shadows outer function [shadowFunction]
    std::vector<const ConfParser::Node*> latencyMonitors;
                                         ^
src/Conf.h:192:44: note: Shadowed declaration
    const std::vector<LatencyMonitorConf>& latencyMonitors() const
                                           ^
src/Conf.cpp:130:42: note: Shadow variable
    std::vector<const ConfParser::Node*> latencyMonitors;
                                         ^
src/Conf.cpp:360:20: style: Variable 'g' can be declared as reference to const [constVariableReference]
        for (auto& g : mStandaloneServerPool.groups) {
                   ^
src/Conf.cpp:576:20: style: Variable 'dc' can be declared as reference to const [constVariableReference]
        for (auto& dc : mDCConfs) {
                   ^
src/Conf.cpp:586:20: style: Variable 'dc' can be declared as reference to const [constVariableReference]
        for (auto& dc : mDCConfs) {
                   ^
src/Conf.cpp:587:24: style: Variable 'rp' can be declared as reference to const [constVariableReference]
            for (auto& rp : dc.readPolicy) {
                       ^
src/Conf.cpp:589:28: style: Variable 'i' can be declared as reference to const [constVariableReference]
                for (auto& i :mDCConfs) {
                           ^
src/Conf.cpp:104:23: style: Variable 'n' can be declared as pointer to const [constVariablePointer]
    ConfParser::Node* n = p.load(argv[1]);
                      ^
src/Conf.cpp:111:26: style: Variable 'v' can be declared as pointer to const [constVariablePointer]
        } else if (char* v = GetVal(argv[i], "--WorkerThreads=")) {
                         ^
src/Conf.cpp:361:36: style: Consider using std::find_if algorithm instead of a raw loop. [useStlAlgorithm]
            if (g.servers.empty()) {
                                   ^
src/Conf.cpp:577:38: style: Consider using std::any_of algorithm instead of a raw loop. [useStlAlgorithm]
            if (dc.name == mLocalDC) {
                                     ^
src/Conf.cpp:590:44: style: Consider using std::any_of algorithm instead of a raw loop. [useStlAlgorithm]
                    if (rp.name == i.name) {
                                           ^
Checking src/Conf.cpp: _EPOLL_...
Checking src/Conf.cpp: _GNU_SOURCE...
Checking src/Conf.cpp: _KQUEUE_...
Checking src/Conf.cpp: _POLL_...
Checking src/Conf.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/Conf.cpp: _PREDIXY_TIMER_STATS_...
9/42 files checked 27% done
Checking src/ConfParser.cpp ...
src/ConfParser.cpp:7:0: information: Include file: <unistd.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <unistd.h>
^
src/ConfParser.cpp:8:0: information: Include file: <limits.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <limits.h>
^
src/ConfParser.cpp:9:0: information: Include file: <PString.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <PString.h>
^
src/ConfParser.cpp:10:0: information: Include file: <strings.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <strings.h>
^
src/ConfParser.cpp:11:0: information: Include file: <errno.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <errno.h>
^
src/ConfParser.cpp:12:0: information: Include file: <iostream> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <iostream>
^
src/ConfParser.cpp:13:0: information: Include file: <fstream> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <fstream>
^
src/ConfParser.cpp:14:0: information: Include file: <memory> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <memory>
^
src/ConfParser.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/ConfParser.cpp:119:32: style: Variable 'pre' can be declared as reference to const [constVariableReference]
                    for (auto& pre : files) {
                               ^
src/ConfParser.cpp:120:67: style: Consider using std::any_of algorithm instead of a raw loop. [useStlAlgorithm]
                        if (strcmp(pre.name, name->c_str()) == 0) {
                                                                  ^
src/ConfParser.cpp:296:19: style: Consider using std::count_if algorithm instead of a raw loop. [useStlAlgorithm]
                ++vsp;
                  ^
Checking src/ConfParser.cpp: _GNU_SOURCE...
10/42 files checked 30% done
Checking src/ConnectConnection.cpp ...
src/ConnectConnection.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/ConnectConnection.cpp:193:30: style: Variable 'req' can be declared as pointer to const [constVariablePointer]
                if (Request* req = mSentRequests.front()) {
                             ^
Checking src/ConnectConnection.cpp: _EPOLL_...
Checking src/ConnectConnection.cpp: _GNU_SOURCE...
Checking src/ConnectConnection.cpp: _KQUEUE_...
Checking src/ConnectConnection.cpp: _POLL_...
Checking src/ConnectConnection.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/ConnectConnection.cpp: _PREDIXY_TIMER_STATS_...
11/42 files checked 33% done
Checking src/ConnectConnectionPool.cpp ...
src/ConnectConnectionPool.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/ConnectConnectionPool.cpp:125:10: style: Variable 'sp' can be declared as pointer to const [constVariablePointer]
    auto sp = mHandler->proxy()->serverPool();
         ^
Checking src/ConnectConnectionPool.cpp: _EPOLL_...
Checking src/ConnectConnectionPool.cpp: _GNU_SOURCE...
Checking src/ConnectConnectionPool.cpp: _KQUEUE_...
Checking src/ConnectConnectionPool.cpp: _POLL_...
Checking src/ConnectConnectionPool.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/ConnectConnectionPool.cpp: _PREDIXY_TIMER_STATS_...
12/42 files checked 35% done
Checking src/ConnectSocket.cpp ...
src/ConnectSocket.cpp:62:21: warning: The class 'ConnectSocket' defines member function with name 'close' also defined in its parent class 'Socket'. [duplInheritedMember]
void ConnectSocket::close()
                    ^
src/Socket.h:59:10: note: Parent function 'Socket::close'
    void close();
         ^
src/ConnectSocket.cpp:62:21: note: Derived function 'ConnectSocket::close'
void ConnectSocket::close()
                    ^
src/ConnectSocket.cpp:17:40: style: C-style pointer casting [cstyleCast]
    getFirstAddr(peer, type, protocol, (sockaddr*)&mPeerAddr, &mPeerAddrLen);
                                       ^
src/ConnectSocket.cpp:18:20: style: C-style pointer casting [cstyleCast]
    sockaddr* in = (sockaddr*)&mPeerAddr;
                   ^
src/ConnectSocket.cpp:32:36: style: C-style pointer casting [cstyleCast]
        int ret = ::connect(fd(), (const sockaddr*)&mPeerAddr, mPeerAddrLen);
                                   ^
src/ConnectSocket.cpp:56:20: style: C-style pointer casting [cstyleCast]
    sockaddr* in = (sockaddr*)&mPeerAddr;
                   ^
src/ConnectSocket.cpp:18:15: style: Variable 'in' can be declared as pointer to const [constVariablePointer]
    sockaddr* in = (sockaddr*)&mPeerAddr;
              ^
src/ConnectSocket.cpp:56:15: style: Variable 'in' can be declared as pointer to const [constVariablePointer]
    sockaddr* in = (sockaddr*)&mPeerAddr;
              ^
13/42 files checked 36% done
Checking src/Connection.cpp ...
src/Connection.cpp:17:42: style: Parameter 'h' can be declared as pointer to const [constParameterPointer]
BufferPtr Connection::getBuffer(Handler* h, bool allowNew)
                                         ^
Checking src/Connection.cpp: _GNU_SOURCE...
Checking src/Connection.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/Connection.cpp: _PREDIXY_TIMER_STATS_...
14/42 files checked 36% done
Checking src/Crc16.cpp ...
15/42 files checked 37% done
Checking src/DC.cpp ...
Checking src/DC.cpp: _EPOLL_...
Checking src/DC.cpp: _GNU_SOURCE...
Checking src/DC.cpp: _KQUEUE_...
Checking src/DC.cpp: _POLL_...
src/DC.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
Checking src/DC.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/DC.cpp: _PREDIXY_TIMER_STATS_...
16/42 files checked 38% done
Checking src/Distribution.cpp ...
src/Distribution.cpp:7:0: information: Include file: <PString.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <PString.h>
^
src/Distribution.cpp:8:0: information: Include file: <strings.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <strings.h>
^
17/42 files checked 38% done
Checking src/Enums.cpp ...
18/42 files checked 38% done
Checking src/EpollMultiplexor.cpp ...
Checking src/EpollMultiplexor.cpp: _EPOLL_...
Checking src/EpollMultiplexor.cpp: _GNU_SOURCE...
Checking src/EpollMultiplexor.cpp: _KQUEUE_...
Checking src/EpollMultiplexor.cpp: _POLL_...
src/EpollMultiplexor.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
19/42 files checked 39% done
Checking src/Handler.cpp ...
src/Handler.cpp:7:0: information: Include file: <unistd.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <unistd.h>
^
src/Handler.cpp:8:0: information: Include file: <sys/types.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/types.h>
^
src/Handler.cpp:9:0: information: Include file: <sys/time.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/time.h>
^
src/Handler.cpp:10:0: information: Include file: <time.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <time.h>
^
src/Handler.cpp:11:0: information: Include file: <signal.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <signal.h>
^
src/Handler.cpp:12:0: information: Include file: <sys/resource.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/resource.h>
^
src/Handler.cpp:13:0: information: Include file: <iostream> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <iostream>
^
src/SentinelServerPool.h:10:0: information: Include file: <map> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <map>
^
src/Handler.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/PString.h:185:5: warning: Member variable 'SString < Const :: MaxKeyLen >::mBuf' is not initialized in the copy constructor. [uninitMemberVar]
    SString(const SString& str)
    ^
src/PString.h:185:5: warning: Member variable 'SString < 32 >::mBuf' is not initialized in the copy constructor. [uninitMemberVar]
    SString(const SString& str)
    ^
src/PString.h:185:5: warning: Member variable 'SString < 512 >::mBuf' is not initialized in the copy constructor. [uninitMemberVar]
    SString(const SString& str)
    ^
src/Handler.cpp:27:5: warning: Class 'Handler' does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s). [noCopyConstructor]
    mEventLoop(new Multiplexor()),
    ^
src/Handler.cpp:27:5: warning: Class 'Handler' does not have a operator= which is recommended since it has dynamic memory/resource allocation(s). [noOperatorEq]
    mEventLoop(new Multiplexor()),
    ^
src/SentinelServerPool.h:22:13: warning: The class 'SentinelServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'. [duplInheritedMember]
    Server* getServer(Handler* h, Request* req, const String& key) const;
            ^
src/ServerPool.h:89:13: note: Parent function 'ServerPool::getServer'
    Server* getServer(Handler* h, Request* req, const String& key) const
            ^
src/SentinelServerPool.h:22:13: note: Derived function 'SentinelServerPool::getServer'
    Server* getServer(Handler* h, Request* req, const String& key) const;
            ^
src/SentinelServerPool.h:23:13: warning: The class 'SentinelServerPool' defines member function with name 'iter' also defined in its parent class 'ServerPool'. [duplInheritedMember]
    Server* iter(int& cursor) const
            ^
src/ServerPool.h:93:13: note: Parent function 'ServerPool::iter'
    Server* iter(int& cursor) const
            ^
src/SentinelServerPool.h:23:13: note: Derived function 'SentinelServerPool::iter'
    Server* iter(int& cursor) const
            ^
src/SentinelServerPool.h:27:10: warning: The class 'SentinelServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'. [duplInheritedMember]
    void refreshRequest(Handler* h);
         ^
src/ServerPool.h:97:10: note: Parent function 'ServerPool::refreshRequest'
    void refreshRequest(Handler* h)
         ^
src/SentinelServerPool.h:27:10: note: Derived function 'SentinelServerPool::refreshRequest'
    void refreshRequest(Handler* h);
         ^
src/SentinelServerPool.h:28:10: warning: The class 'SentinelServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'. [duplInheritedMember]
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/ServerPool.h:101:10: note: Parent function 'ServerPool::handleResponse'
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/SentinelServerPool.h:28:10: note: Derived function 'SentinelServerPool::handleResponse'
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/SentinelServerPool.h:19:5: style: Class 'SentinelServerPool' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SentinelServerPool(Proxy* p);
    ^
src/Buffer.h:211:5: style: Class 'SegmentStr < Const :: MaxKeyLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SegmentStr(const Segment& seg)
    ^
src/Buffer.h:211:5: style: Class 'SegmentStr < 64 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SegmentStr(const Segment& seg)
    ^
src/Buffer.h:211:5: style: Class 'SegmentStr < 128 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SegmentStr(const Segment& seg)
    ^
src/Buffer.h:211:5: style: Class 'SegmentStr < 256 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SegmentStr(const Segment& seg)
    ^
src/PString.h:171:5: style: Class 'SString < Const :: MaxKeyLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const char* str):
    ^
src/PString.h:181:5: style: Class 'SString < Const :: MaxKeyLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const std::string& str)
    ^
src/PString.h:190:5: style: Class 'SString < Const :: MaxKeyLen >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const T& str)
    ^
src/PString.h:171:5: style: Class 'SString < 32 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const char* str):
    ^
src/PString.h:181:5: style: Class 'SString < 32 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const std::string& str)
    ^
src/PString.h:190:5: style: Class 'SString < 32 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const T& str)
    ^
src/PString.h:171:5: style: Class 'SString < 512 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const char* str):
    ^
src/PString.h:181:5: style: Class 'SString < 512 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const std::string& str)
    ^
src/PString.h:190:5: style: Class 'SString < 512 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const T& str)
    ^
src/SentinelServerPool.h:20:6: style: The destructor '~SentinelServerPool' overrides a destructor in a base class but is not marked with a 'override' specifier. [missingOverride]
    ~SentinelServerPool();
     ^
src/ServerPool.h:28:14: note: Virtual destructor in base class
    virtual ~ServerPool();
             ^
src/SentinelServerPool.h:20:6: note: Destructor in derived class
    ~SentinelServerPool();
     ^
src/Handler.cpp:295:32: style: C-style pointer casting [cstyleCast]
            int fd = s->accept((sockaddr*)&addr, &len);
                               ^
src/Handler.cpp:298:37: style: C-style pointer casting [cstyleCast]
                addAcceptSocket(fd, (sockaddr*)&addr, len);
                                    ^
src/Handler.cpp:1269:22: style: C-style pointer casting [cstyleCast]
    char* p = strchr((char*)d.data(), '\r');
                     ^
src/Handler.cpp:675:9: style: Consecutive return, break, continue, goto or throw statements are unnecessary. [duplicateBreak]
        break;
        ^
src/Handler.cpp:1432:9: style: The scope of the variable 'slot' can be reduced. [variableScope]
    int slot;
        ^
src/Handler.cpp:39:11: style: Variable 'conf' can be declared as pointer to const [constVariablePointer]
    Conf* conf = p->conf();
          ^
src/Handler.cpp:51:10: style: Variable 'conf' can be declared as pointer to const [constVariablePointer]
    auto conf = mProxy->conf();
         ^
src/Handler.cpp:336:14: style: Variable 'auth' can be declared as pointer to const [constVariablePointer]
    if (auto auth = mProxy->authority()->getDefault()) {
             ^
src/Handler.cpp:439:59: style: Parameter 'req' can be declared as pointer to const [constParameterPointer]
ConnectConnection* Handler::getConnectConnection(Request* req, Server* serv)
                                                          ^
src/Handler.cpp:460:10: style: Variable 'c' can be declared as pointer to const [constVariablePointer]
    auto c = req->connection();
         ^
src/Handler.cpp:520:10: style: Variable 'sp' can be declared as pointer to const [constVariablePointer]
    auto sp = mProxy->serverPool();
         ^
src/Handler.cpp:611:18: style: Variable 'sp' can be declared as pointer to const [constVariablePointer]
            auto sp = mProxy->serverPool();
                 ^
src/Handler.cpp:652:18: style: Variable 'g' can be declared as pointer to const [constVariablePointer]
            auto g = sp->getGroup(groupIdx);
                 ^
src/Handler.cpp:682:42: style: Parameter 'req' can be declared as pointer to const [constParameterPointer]
void Handler::postHandleRequest(Request* req, ConnectConnection* s)
                                         ^
src/Handler.cpp:816:18: style: Variable 'g' can be declared as pointer to const [constVariablePointer]
            auto g = s->server()->group();
                 ^
src/Handler.cpp:975:19: style: Variable 'h' can be declared as pointer to const [constVariablePointer]
        for (auto h : mProxy->handlers()) {
                  ^
src/Handler.cpp:994:14: style: Variable 'sp' can be declared as pointer to const [constVariablePointer]
        auto sp = mProxy->serverPool();
             ^
src/Handler.cpp:995:24: style: Variable 'serv' can be declared as pointer to const [constVariablePointer]
        while (Server* serv = sp->iter(servCursor)) {
                       ^
src/Handler.cpp:997:23: style: Variable 'h' can be declared as pointer to const [constVariablePointer]
            for (auto h : mProxy->handlers()) {
                      ^
src/Handler.cpp:1004:18: style: Variable 'g' can be declared as pointer to const [constVariablePointer]
            auto g = serv->group();
                 ^
src/Handler.cpp:1079:20: style: Variable 'serv' can be declared as pointer to const [constVariablePointer]
    while (Server* serv = sp->iter(servCursor)) {
                   ^
src/Handler.cpp:1082:19: style: Variable 'h' can be declared as pointer to const [constVariablePointer]
        for (auto h : mProxy->handlers()) {
                  ^
src/Handler.cpp:1141:19: style: Variable 'h' can be declared as pointer to const [constVariablePointer]
        for (auto h : mProxy->handlers()) {
                  ^
src/Handler.cpp:1154:23: style: Variable 'h' can be declared as pointer to const [constVariablePointer]
            for (auto h : mProxy->handlers()) {
                      ^
src/Handler.cpp:1426:43: style: Parameter 'c' can be declared as pointer to const [constParameterPointer]
bool Handler::redirect(ConnectConnection* c, Request* req, Response* res, bool moveOrAsk)
                                          ^
src/Handler.cpp:1426:70: style: Parameter 'res' can be declared as pointer to const [constParameterPointer]
bool Handler::redirect(ConnectConnection* c, Request* req, Response* res, bool moveOrAsk)
                                                                     ^
src/Handler.cpp:1443:10: style: Variable 'p' can be declared as pointer to const [constVariablePointer]
    auto p = static_cast<ClusterServerPool*>(mProxy->serverPool());
         ^
src/Handler.cpp:1481:25: style: Variable 'auth' can be declared as pointer to const [constVariablePointer]
        } else if (auto auth = m->get(req->isInline() ? pw : key)) {
                        ^
src/Handler.cpp:999:24: style: Consider using std::accumulate algorithm instead of a raw loop. [useStlAlgorithm]
                    st += cp->stats();
                       ^
src/Handler.cpp:1084:20: style: Consider using std::accumulate algorithm instead of a raw loop. [useStlAlgorithm]
                lm += cp->latencyMonitors()[i];
                   ^
src/Handler.cpp:1143:20: style: Consider using std::accumulate algorithm instead of a raw loop. [useStlAlgorithm]
                lm += cp->latencyMonitors()[i];
                   ^
src/Handler.cpp:1156:24: style: Consider using std::accumulate algorithm instead of a raw loop. [useStlAlgorithm]
                    lm += cp->latencyMonitors()[i];
                       ^
Checking src/Handler.cpp: _EPOLL_...
Checking src/Handler.cpp: _GNU_SOURCE...
Checking src/Handler.cpp: _KQUEUE_...
Checking src/Handler.cpp: _MULTIPLEXOR_ASYNC_ASSIGN_...
Checking src/Handler.cpp: _POLL_...
Checking src/Handler.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/Handler.cpp: _PREDIXY_TIMER_STATS_...
20/42 files checked 58% done
Checking src/HashFunc.cpp ...
src/HashFunc.cpp:7:0: information: Include file: <PString.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <PString.h>
^
src/HashFunc.cpp:8:0: information: Include file: <strings.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <strings.h>
^
src/HashFunc.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
21/42 files checked 58% done
Checking src/KqueueMultiplexor.cpp ...
Checking src/KqueueMultiplexor.cpp: _EPOLL_...
Checking src/KqueueMultiplexor.cpp: _GNU_SOURCE...
Checking src/KqueueMultiplexor.cpp: _KQUEUE_...
Checking src/KqueueMultiplexor.cpp: _POLL_...
src/KqueueMultiplexor.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
22/42 files checked 59% done
Checking src/LatencyMonitor.cpp ...
src/LatencyMonitor.cpp:55:18: style: Local variable 'i' shadows outer variable [shadowVariable]
        for (int i = 0; i < size; ++i) {
                 ^
src/LatencyMonitor.cpp:41:9: note: Shadowed declaration
    int i = 0;
        ^
src/LatencyMonitor.cpp:55:18: note: Shadow variable
        for (int i = 0; i < size; ++i) {
                 ^
src/LatencyMonitor.cpp:12:16: style: Variable 's' can be declared as reference to const [constVariableReference]
    for (auto& s : mTimeSpan) {
               ^
src/LatencyMonitor.cpp:13:12: style: Consider using std::accumulate algorithm instead of a raw loop. [useStlAlgorithm]
        tc += s.count;
           ^
Checking src/LatencyMonitor.cpp: _EPOLL_...
Checking src/LatencyMonitor.cpp: _GNU_SOURCE...
Checking src/LatencyMonitor.cpp: _KQUEUE_...
Checking src/LatencyMonitor.cpp: _POLL_...
src/LatencyMonitor.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
Checking src/LatencyMonitor.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/LatencyMonitor.cpp: _PREDIXY_TIMER_STATS_...
23/42 files checked 60% done
Checking src/ListenSocket.cpp ...
src/ListenSocket.cpp:7:0: information: Include file: <PString.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <PString.h>
^
src/ListenSocket.cpp:17:40: style: C-style pointer casting [cstyleCast]
    getFirstAddr(addr, type, protocol, (sockaddr*)&saddr, &len);
                                       ^
src/ListenSocket.cpp:18:20: style: C-style pointer casting [cstyleCast]
    sockaddr* in = (sockaddr*)&saddr;
                   ^
Checking src/ListenSocket.cpp: _GNU_SOURCE...
24/42 files checked 60% done
Checking src/LogFileSink.cpp ...
src/LogFileSink.cpp:7:0: information: Include file: <unistd.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <unistd.h>
^
src/LogFileSink.cpp:8:0: information: Include file: <PString.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <PString.h>
^
src/LogFileSink.cpp:9:0: information: Include file: <strings.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <strings.h>
^
src/LogFileSink.cpp:10:0: information: Include file: <time.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <time.h>
^
src/LogFileSink.cpp:11:0: information: Include file: <sstream> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sstream>
^
src/LogFileSink.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/LogFileSink.cpp:18:14: warning: Member variable 'LogFileSink::mFilePath' is not initialized in the constructor. [uninitMemberVar]
LogFileSink::LogFileSink():
             ^
src/LogFileSink.cpp:70:19: style: int result is assigned to long & variable. If the variable is long & to avoid loss of information, then you have loss of information. [truncLongCastAssignment]
            bytes = n * (1 << 30);
                  ^
src/LogFileSink.cpp:72:19: style: int result is assigned to long & variable. If the variable is long & to avoid loss of information, then you have loss of information. [truncLongCastAssignment]
            bytes = n * (1 << 20);
                  ^
src/LogFileSink.cpp:176:20: style: Variable 'rotate' is assigned a value that is never used. [unreadVariable]
            rotate = reopen(now);
                   ^
25/42 files checked 62% done
Checking src/Logger.cpp ...
src/Logger.cpp:7:0: information: Include file: <time.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <time.h>
^
src/Logger.cpp:8:0: information: Include file: <unistd.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <unistd.h>
^
src/Logger.cpp:9:0: information: Include file: <PString.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <PString.h>
^
src/Logger.cpp:10:0: information: Include file: <errno.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <errno.h>
^
src/Logger.cpp:11:0: information: Include file: <chrono> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <chrono>
^
src/Logger.cpp:15:0: information: Include file: <stdio.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <stdio.h>
^
src/Logger.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/Logger.cpp:26:10: warning: Member variable 'LogUnit::mBuf' is not initialized in the constructor. [uninitMemberVar]
LogUnit::LogUnit():
         ^
src/Logger.cpp:72:9: warning: Member variable 'Logger::mThread' is not initialized in the constructor. [uninitMemberVar]
Logger::Logger(int maxLogUnitNum):
        ^
src/Logger.cpp:127:37: style: Condition '!mStop' is always true [knownConditionTrueFalse]
            while (mLogs.empty() && !mStop) {
                                    ^
src/Logger.cpp:123:12: note: Assuming that condition '!mStop' is not redundant
    while (!mStop) {
           ^
src/Logger.cpp:127:37: note: Condition '!mStop' is always true
            while (mLogs.empty() && !mStop) {
                                    ^
src/Logger.cpp:136:23: style: Local variable 'log' shadows outer function [shadowFunction]
            for (auto log : logs) {
                      ^
src/Logger.h:79:14: note: Shadowed declaration
    LogUnit* log(LogLevel::Type lvl)
             ^
src/Logger.cpp:136:23: note: Shadow variable
            for (auto log : logs) {
                      ^
src/Logger.cpp:144:23: style: Local variable 'log' shadows outer function [shadowFunction]
            for (auto log : logs) {
                      ^
src/Logger.h:79:14: note: Shadowed declaration
    LogUnit* log(LogLevel::Type lvl)
             ^
src/Logger.cpp:144:23: note: Shadow variable
            for (auto log : logs) {
                      ^
src/Logger.cpp:151:21: style: Local variable 'log' shadows outer function [shadowFunction]
            LogUnit log;
                    ^
src/Logger.h:79:14: note: Shadowed declaration
    LogUnit* log(LogLevel::Type lvl)
             ^
src/Logger.cpp:151:21: note: Shadow variable
            LogUnit log;
                    ^
src/Logger.cpp:173:14: style: Local variable 'log' shadows outer function [shadowFunction]
    LogUnit* log = nullptr;
             ^
src/Logger.h:79:14: note: Shadowed declaration
    LogUnit* log(LogLevel::Type lvl)
             ^
src/Logger.cpp:173:14: note: Shadow variable
    LogUnit* log = nullptr;
             ^
src/Logger.cpp:145:23: style: Consider using std::copy algorithm instead of a raw loop. [useStlAlgorithm]
                mFree.push_back(log);
                      ^
Checking src/Logger.cpp: _GNU_SOURCE...
26/42 files checked 64% done
Checking src/PollMultiplexor.cpp ...
src/PollMultiplexor.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/PollMultiplexor.cpp:36:41: style: Parameter 's' can be declared as pointer to const [constParameterPointer]
void PollMultiplexor::delSocket(Socket* s)
                                        ^
src/PollMultiplexor.cpp:52:40: style: Parameter 's' can be declared as pointer to const [constParameterPointer]
bool PollMultiplexor::addEvent(Socket* s, int evts)
                                       ^
src/PollMultiplexor.cpp:61:40: style: Parameter 's' can be declared as pointer to const [constParameterPointer]
bool PollMultiplexor::delEvent(Socket* s, int evts)
                                       ^
Checking src/PollMultiplexor.cpp: _EPOLL_...
Checking src/PollMultiplexor.cpp: _GNU_SOURCE...
Checking src/PollMultiplexor.cpp: _KQUEUE_...
Checking src/PollMultiplexor.cpp: _POLL_...
27/42 files checked 65% done
Checking src/Proxy.cpp ...
src/Proxy.cpp:7:0: information: Include file: <unistd.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <unistd.h>
^
src/Proxy.cpp:8:0: information: Include file: <signal.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <signal.h>
^
src/Proxy.cpp:9:0: information: Include file: <time.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <time.h>
^
src/Proxy.cpp:10:0: information: Include file: <stdlib.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <stdlib.h>
^
src/Proxy.cpp:11:0: information: Include file: <sys/types.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/types.h>
^
src/Proxy.cpp:12:0: information: Include file: <iostream> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <iostream>
^
src/Proxy.cpp:13:0: information: Include file: <thread> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <thread>
^
src/Proxy.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/Proxy.cpp:46:8: warning: Member variable 'Proxy::mConf' is not initialized in the constructor. [uninitMemberVar]
Proxy::Proxy():
       ^
src/Proxy.cpp:92:16: style: Variable 'ac' can be declared as reference to const [constVariableReference]
    for (auto& ac : mConf->authConfs()) {
               ^
Checking src/Proxy.cpp: _EPOLL_...
Checking src/Proxy.cpp: _GNU_SOURCE...
Checking src/Proxy.cpp: _KQUEUE_...
Checking src/Proxy.cpp: _POLL_...
src/Backtrace.h:14:0: information: Include file: <execinfo.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <execinfo.h>
^
Checking src/Proxy.cpp: _PREDIXY_BACKTRACE_...
Checking src/Proxy.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/Proxy.cpp: _PREDIXY_TIMER_STATS_...
28/42 files checked 66% done
Checking src/Reply.cpp ...
29/42 files checked 66% done
Checking src/Request.cpp ...
src/Request.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/Request.cpp:94:10: style: Variable 'r' can be declared as pointer to const [constVariablePointer]
    auto r = GenericRequests[code];
         ^
src/Request.cpp:350:27: style: Variable 'leaderRes' can be declared as pointer to const [constVariablePointer]
            if (Response* leaderRes = ld->getResponse()) {
                          ^
src/Request.cpp:359:27: style: Variable 'leaderRes' can be declared as pointer to const [constVariablePointer]
            if (Response* leaderRes = ld->getResponse()) {
                          ^
src/Request.cpp:380:27: style: Variable 'leaderRes' can be declared as pointer to const [constVariablePointer]
            if (Response* leaderRes = ld->getResponse()) {
                          ^
Checking src/Request.cpp: _EPOLL_...
Checking src/Request.cpp: _GNU_SOURCE...
Checking src/Request.cpp: _KQUEUE_...
Checking src/Request.cpp: _POLL_...
Checking src/Request.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/Request.cpp: _PREDIXY_TIMER_STATS_...
30/42 files checked 70% done
Checking src/RequestParser.cpp ...
src/RequestParser.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/RequestParser.cpp:9:16: warning: Member variable 'RequestParser::mCmd' is not initialized in the constructor. [uninitMemberVar]
RequestParser::RequestParser()
               ^
src/PString.h:185:5: warning: Member variable 'SString < 64 >::mBuf' is not initialized in the copy constructor. [uninitMemberVar]
    SString(const SString& str)
    ^
src/PString.h:185:5: warning: Member variable 'SString < 16 >::mBuf' is not initialized in the copy constructor. [uninitMemberVar]
    SString(const SString& str)
    ^
src/PString.h:171:5: style: Class 'SString < 64 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const char* str):
    ^
src/PString.h:181:5: style: Class 'SString < 64 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const std::string& str)
    ^
src/PString.h:190:5: style: Class 'SString < 64 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const T& str)
    ^
src/PString.h:171:5: style: Class 'SString < 16 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const char* str):
    ^
src/PString.h:181:5: style: Class 'SString < 16 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const std::string& str)
    ^
src/PString.h:190:5: style: Class 'SString < 16 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const T& str)
    ^
src/RequestParser.cpp:74:11: style: Variable 'cursor' can be declared as pointer to const [constVariablePointer]
    char* cursor = buf->data() + pos;
          ^
src/RequestParser.cpp:75:11: style: Variable 'end' can be declared as pointer to const [constVariablePointer]
    char* end = buf->tail();
          ^
Checking src/RequestParser.cpp: _EPOLL_...
Checking src/RequestParser.cpp: _GNU_SOURCE...
Checking src/RequestParser.cpp: _KQUEUE_...
Checking src/RequestParser.cpp: _POLL_...
Checking src/RequestParser.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/RequestParser.cpp: _PREDIXY_TIMER_STATS_...
31/42 files checked 76% done
Checking src/Response.cpp ...
src/Response.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/Buffer.h:211:5: style: Class 'SegmentStr < Const :: MaxAddrLen + 16 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SegmentStr(const Segment& seg)
    ^
src/Response.cpp:64:10: style: Variable 'r' can be declared as pointer to const [constVariablePointer]
    auto r = GenericResponses[code];
         ^
src/Response.cpp:107:41: style: Parameter 'req' can be declared as pointer to const [constParameterPointer]
void Response::adjustForLeader(Request* req)
                                        ^
src/Response.cpp:109:18: style: Variable 'leader' can be declared as pointer to const [constVariablePointer]
    if (Request* leader = req->leader()) {
                 ^
Checking src/Response.cpp: _EPOLL_...
Checking src/Response.cpp: _GNU_SOURCE...
Checking src/Response.cpp: _KQUEUE_...
Checking src/Response.cpp: _POLL_...
Checking src/Response.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/Response.cpp: _PREDIXY_TIMER_STATS_...
32/42 files checked 78% done
Checking src/ResponseParser.cpp ...
src/ResponseParser.cpp:10:17: warning: Member variable 'ResponseParser::mArrayNum' is not initialized in the constructor. [uninitMemberVar]
ResponseParser::ResponseParser()
                ^
src/ResponseParser.cpp:10:17: warning: Member variable 'ResponseParser::mElementCnt' is not initialized in the constructor. [uninitMemberVar]
ResponseParser::ResponseParser()
                ^
src/ResponseParser.cpp:35:11: style: Variable 'end' can be declared as pointer to const [constVariablePointer]
    char* end = buf->tail();
          ^
Checking src/ResponseParser.cpp: _EPOLL_...
Checking src/ResponseParser.cpp: _GNU_SOURCE...
Checking src/ResponseParser.cpp: _KQUEUE_...
Checking src/ResponseParser.cpp: _POLL_...
src/ResponseParser.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
Checking src/ResponseParser.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/ResponseParser.cpp: _PREDIXY_TIMER_STATS_...
33/42 files checked 81% done
Checking src/SentinelServerPool.cpp ...
src/SentinelServerPool.cpp:7:0: information: Include file: <algorithm> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <algorithm>
^
src/SentinelServerPool.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/PString.h:185:5: warning: Member variable 'SString < 4 >::mBuf' is not initialized in the copy constructor. [uninitMemberVar]
    SString(const SString& str)
    ^
src/SentinelServerPool.cpp:58:29: warning: The class 'SentinelServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'. [duplInheritedMember]
Server* SentinelServerPool::getServer(Handler* h, Request* req, const String& key) const
                            ^
src/ServerPool.h:89:13: note: Parent function 'ServerPool::getServer'
    Server* getServer(Handler* h, Request* req, const String& key) const
            ^
src/SentinelServerPool.cpp:58:29: note: Derived function 'SentinelServerPool::getServer'
Server* SentinelServerPool::getServer(Handler* h, Request* req, const String& key) const
                            ^
src/SentinelServerPool.cpp:103:26: warning: The class 'SentinelServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'. [duplInheritedMember]
void SentinelServerPool::refreshRequest(Handler* h)
                         ^
src/ServerPool.h:97:10: note: Parent function 'ServerPool::refreshRequest'
    void refreshRequest(Handler* h)
         ^
src/SentinelServerPool.cpp:103:26: note: Derived function 'SentinelServerPool::refreshRequest'
void SentinelServerPool::refreshRequest(Handler* h)
                         ^
src/SentinelServerPool.cpp:122:26: warning: The class 'SentinelServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'. [duplInheritedMember]
void SentinelServerPool::handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res)
                         ^
src/ServerPool.h:101:10: note: Parent function 'ServerPool::handleResponse'
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/SentinelServerPool.cpp:122:26: note: Derived function 'SentinelServerPool::handleResponse'
void SentinelServerPool::handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res)
                         ^
src/SentinelServerPool.cpp:148:5: style: Class 'AddrParser' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    AddrParser(const Segment& res):
    ^
src/Buffer.h:211:5: style: Class 'SegmentStr < Const :: MaxAddrLen + 32 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SegmentStr(const Segment& seg)
    ^
src/PString.h:171:5: style: Class 'SString < 4 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const char* str):
    ^
src/PString.h:181:5: style: Class 'SString < 4 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const std::string& str)
    ^
src/PString.h:190:5: style: Class 'SString < 4 >' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    SString(const T& str)
    ^
src/SentinelServerPool.cpp:337:22: style: C-style pointer casting [cstyleCast]
    ServerGroup* g = (ServerGroup*)req->data();
                     ^
src/SentinelServerPool.cpp:411:22: style: C-style pointer casting [cstyleCast]
    ServerGroup* g = (ServerGroup*)req->data();
                     ^
src/SentinelServerPool.cpp:89:13: style: Consecutive return, break, continue, goto or throw statements are unnecessary. [duplicateBreak]
            break;
            ^
src/SentinelServerPool.cpp:95:13: style: Consecutive return, break, continue, goto or throw statements are unnecessary. [duplicateBreak]
            break;
            ^
src/SentinelServerPool.cpp:290:51: style: Parameter 'h' can be declared as pointer to const [constParameterPointer]
void SentinelServerPool::handleSentinels(Handler* h, ConnectConnection* s, Request* req, Response* res)
                                                  ^
src/SentinelServerPool.cpp:332:85: style: Parameter 'req' can be declared as pointer to const [constParameterPointer]
void SentinelServerPool::handleGetMaster(Handler* h, ConnectConnection* s, Request* req, Response* res)
                                                                                    ^
src/SentinelServerPool.cpp:406:82: style: Parameter 'req' can be declared as pointer to const [constParameterPointer]
void SentinelServerPool::handleSlaves(Handler* h, ConnectConnection* s, Request* req, Response* res)
                                                                                 ^
Checking src/SentinelServerPool.cpp: _EPOLL_...
Checking src/SentinelServerPool.cpp: _GNU_SOURCE...
Checking src/SentinelServerPool.cpp: _KQUEUE_...
Checking src/SentinelServerPool.cpp: _POLL_...
Checking src/SentinelServerPool.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/SentinelServerPool.cpp: _PREDIXY_TIMER_STATS_...
34/42 files checked 86% done
Checking src/Server.cpp ...
src/Server.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/Server.cpp:31:14: style: Variable 'dataCenter' can be declared as pointer to const [constVariablePointer]
    if (auto dataCenter = pool->proxy()->dataCenter()) {
             ^
Checking src/Server.cpp: _EPOLL_...
Checking src/Server.cpp: _GNU_SOURCE...
Checking src/Server.cpp: _KQUEUE_...
Checking src/Server.cpp: _POLL_...
Checking src/Server.cpp: _PREDIXY_SINGLE_THREAD_...
src/Server.cpp:47:12: style: The comparison 'mNextActivateTime == v' is always true because 'mNextActivateTime' and 'v' represent the same value. [knownConditionTrueFalse]
    return AtomicCAS(mNextActivateTime, v, now + mPool->serverRetryTimeout());
           ^
src/Server.cpp:42:14: note: 'v' is assigned value 'mNextActivateTime' here.
    long v = mNextActivateTime;
             ^
src/Server.cpp:47:12: note: The comparison 'mNextActivateTime == v' is always true because 'mNextActivateTime' and 'v' represent the same value.
    return AtomicCAS(mNextActivateTime, v, now + mPool->serverRetryTimeout());
           ^
Checking src/Server.cpp: _PREDIXY_TIMER_STATS_...
35/42 files checked 87% done
Checking src/ServerGroup.cpp ...
src/ServerGroup.cpp:7:0: information: Include file: <algorithm> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <algorithm>
^
src/ServerGroup.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/ServerGroup.cpp:46:53: style: Parameter 'req' can be declared as pointer to const [constParameterPointer]
Server* ServerGroup::getServer(Handler* h, Request* req) const
                                                    ^
src/ServerGroup.cpp:62:21: style: Variable 'dataCenter' can be declared as pointer to const [constVariablePointer]
    } else if (auto dataCenter = mPool->proxy()->dataCenter()) {
                    ^
src/ServerGroup.cpp:136:52: style: Parameter 'localDC' can be declared as pointer to const [constParameterPointer]
Server* ServerGroup::getReadServer(Handler* h, DC* localDC) const
                                                   ^
src/ServerGroup.cpp:203:9: style: Variable 'dc' can be declared as pointer to const [constVariablePointer]
    DC* dc = nullptr;
        ^
src/ServerGroup.cpp:298:34: style: Parameter 's' can be declared as pointer to const [constParameterPointer]
void ServerGroup::remove(Server* s)
                                 ^
Checking src/ServerGroup.cpp: _EPOLL_...
Checking src/ServerGroup.cpp: _GNU_SOURCE...
Checking src/ServerGroup.cpp: _KQUEUE_...
Checking src/ServerGroup.cpp: _POLL_...
Checking src/ServerGroup.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/ServerGroup.cpp: _PREDIXY_TIMER_STATS_...
36/42 files checked 90% done
Checking src/ServerPool.cpp ...
src/ServerPool.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/ClusterServerPool.h:31:10: warning: The class 'ClusterServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'. [duplInheritedMember]
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/ServerPool.cpp:39:18: note: Parent function 'ServerPool::handleResponse'
void ServerPool::handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res)
                 ^
src/ClusterServerPool.h:31:10: note: Derived function 'ClusterServerPool::handleResponse'
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/StandaloneServerPool.h:28:10: warning: The class 'StandaloneServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'. [duplInheritedMember]
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/ServerPool.cpp:39:18: note: Parent function 'ServerPool::handleResponse'
void ServerPool::handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res)
                 ^
src/StandaloneServerPool.h:28:10: note: Derived function 'StandaloneServerPool::handleResponse'
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
Checking src/ServerPool.cpp: _EPOLL_...
Checking src/ServerPool.cpp: _GNU_SOURCE...
Checking src/ServerPool.cpp: _KQUEUE_...
Checking src/ServerPool.cpp: _POLL_...
Checking src/ServerPool.cpp: _PREDIXY_SINGLE_THREAD_...
src/ServerPool.cpp:36:12: style: The comparison 'mLastRefreshTime == last' is always true because 'mLastRefreshTime' and 'last' represent the same value. [knownConditionTrueFalse]
    return AtomicCAS(mLastRefreshTime, last, now);
           ^
src/ServerPool.cpp:31:17: note: 'last' is assigned value 'mLastRefreshTime' here.
    long last = mLastRefreshTime;
                ^
src/ServerPool.cpp:36:12: note: The comparison 'mLastRefreshTime == last' is always true because 'mLastRefreshTime' and 'last' represent the same value.
    return AtomicCAS(mLastRefreshTime, last, now);
           ^
Checking src/ServerPool.cpp: _PREDIXY_TIMER_STATS_...
37/42 files checked 91% done
Checking src/Socket.cpp ...
src/Socket.cpp:7:0: information: Include file: <sys/types.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/types.h>
^
src/Socket.cpp:8:0: information: Include file: <sys/socket.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/socket.h>
^
src/Socket.cpp:9:0: information: Include file: <sys/un.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/un.h>
^
src/Socket.cpp:10:0: information: Include file: <sys/uio.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <sys/uio.h>
^
src/Socket.cpp:11:0: information: Include file: <netinet/in.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <netinet/in.h>
^
src/Socket.cpp:12:0: information: Include file: <netinet/ip.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <netinet/ip.h>
^
src/Socket.cpp:13:0: information: Include file: <netinet/tcp.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <netinet/tcp.h>
^
src/Socket.cpp:14:0: information: Include file: <fcntl.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <fcntl.h>
^
src/Socket.cpp:15:0: information: Include file: <PString.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <PString.h>
^
src/Socket.cpp:16:0: information: Include file: <string> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <string>
^
src/Socket.cpp:77:41: warning: Either the condition 'mStatus<CustomStatus' is redundant or the array 'strs[6]' is accessed at index 99, which is out of bounds. [arrayIndexOutOfBoundsCond]
    return mStatus < CustomStatus ? strs[mStatus] : "Custom";
                                        ^
src/Socket.cpp:77:20: note: Assuming that condition 'mStatus<CustomStatus' is not redundant
    return mStatus < CustomStatus ? strs[mStatus] : "Custom";
                   ^
src/Socket.cpp:77:41: note: Array index out of bounds
    return mStatus < CustomStatus ? strs[mStatus] : "Custom";
                                        ^
src/Socket.cpp:30:9: warning: Member variable 'Socket::mEvts' is not initialized in the constructor. [uninitMemberVar]
Socket::Socket(int domain, int type, int protocol):
        ^
src/Socket.cpp:82:9: style: Local variable 'fd' shadows outer function [shadowFunction]
    int fd = ::socket(domain, type, protocol);
        ^
src/Socket.h:70:9: note: Shadowed declaration
    int fd() const
        ^
src/Socket.cpp:82:9: note: Shadow variable
    int fd = ::socket(domain, type, protocol);
        ^
Checking src/Socket.cpp: _GNU_SOURCE...
Checking src/Socket.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/Socket.cpp: _PREDIXY_TIMER_STATS_...
Checking src/Socket.cpp: __linux__...
38/42 files checked 93% done
Checking src/StandaloneServerPool.cpp ...
src/StandaloneServerPool.cpp:7:0: information: Include file: <algorithm> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <algorithm>
^
src/StandaloneServerPool.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
src/StandaloneServerPool.cpp:72:31: warning: The class 'StandaloneServerPool' defines member function with name 'getServer' also defined in its parent class 'ServerPool'. [duplInheritedMember]
Server* StandaloneServerPool::getServer(Handler* h, Request* req, const String& key) const
                              ^
src/ServerPool.h:89:13: note: Parent function 'ServerPool::getServer'
    Server* getServer(Handler* h, Request* req, const String& key) const
            ^
src/StandaloneServerPool.cpp:72:31: note: Derived function 'StandaloneServerPool::getServer'
Server* StandaloneServerPool::getServer(Handler* h, Request* req, const String& key) const
                              ^
src/StandaloneServerPool.cpp:117:28: warning: The class 'StandaloneServerPool' defines member function with name 'refreshRequest' also defined in its parent class 'ServerPool'. [duplInheritedMember]
void StandaloneServerPool::refreshRequest(Handler* h)
                           ^
src/ServerPool.h:97:10: note: Parent function 'ServerPool::refreshRequest'
    void refreshRequest(Handler* h)
         ^
src/StandaloneServerPool.cpp:117:28: note: Derived function 'StandaloneServerPool::refreshRequest'
void StandaloneServerPool::refreshRequest(Handler* h)
                           ^
src/StandaloneServerPool.cpp:142:28: warning: The class 'StandaloneServerPool' defines member function with name 'handleResponse' also defined in its parent class 'ServerPool'. [duplInheritedMember]
void StandaloneServerPool::handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res)
                           ^
src/ServerPool.h:101:10: note: Parent function 'ServerPool::handleResponse'
    void handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res);
         ^
src/StandaloneServerPool.cpp:142:28: note: Derived function 'StandaloneServerPool::handleResponse'
void StandaloneServerPool::handleResponse(Handler* h, ConnectConnection* s, Request* req, Response* res)
                           ^
src/StandaloneServerPool.cpp:168:5: style: Class 'AddrParser' has a constructor with 1 argument that is not explicit. [noExplicitConstructor]
    AddrParser(const Segment& res):
    ^
src/StandaloneServerPool.cpp:357:22: style: C-style pointer casting [cstyleCast]
    ServerGroup* g = (ServerGroup*)req->data();
                     ^
src/StandaloneServerPool.cpp:431:22: style: C-style pointer casting [cstyleCast]
    ServerGroup* g = (ServerGroup*)req->data();
                     ^
src/StandaloneServerPool.cpp:103:13: style: Consecutive return, break, continue, goto or throw statements are unnecessary. [duplicateBreak]
            break;
            ^
src/StandaloneServerPool.cpp:109:13: style: Consecutive return, break, continue, goto or throw statements are unnecessary. [duplicateBreak]
            break;
            ^
src/StandaloneServerPool.cpp:37:20: style: Variable 'sc' can be declared as reference to const [constVariableReference]
        for (auto& sc : conf.sentinels) {
                   ^
src/StandaloneServerPool.cpp:47:16: style: Variable 'gc' can be declared as reference to const [constVariableReference]
    for (auto& gc : conf.groups) {
               ^
src/StandaloneServerPool.cpp:51:20: style: Variable 'sc' can be declared as reference to const [constVariableReference]
        for (auto& sc : gc.servers) {
                   ^
src/StandaloneServerPool.cpp:310:53: style: Parameter 'h' can be declared as pointer to const [constParameterPointer]
void StandaloneServerPool::handleSentinels(Handler* h, ConnectConnection* s, Request* req, Response* res)
                                                    ^
src/StandaloneServerPool.cpp:352:87: style: Parameter 'req' can be declared as pointer to const [constParameterPointer]
void StandaloneServerPool::handleGetMaster(Handler* h, ConnectConnection* s, Request* req, Response* res)
                                                                                      ^
src/StandaloneServerPool.cpp:426:84: style: Parameter 'req' can be declared as pointer to const [constParameterPointer]
void StandaloneServerPool::handleSlaves(Handler* h, ConnectConnection* s, Request* req, Response* res)
                                                                                   ^
Checking src/StandaloneServerPool.cpp: _EPOLL_...
Checking src/StandaloneServerPool.cpp: _GNU_SOURCE...
Checking src/StandaloneServerPool.cpp: _KQUEUE_...
Checking src/StandaloneServerPool.cpp: _POLL_...
Checking src/StandaloneServerPool.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/StandaloneServerPool.cpp: _PREDIXY_TIMER_STATS_...
39/42 files checked 98% done
Checking src/Subscribe.cpp ...
src/Subscribe.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
Checking src/Subscribe.cpp: _EPOLL_...
Checking src/Subscribe.cpp: _GNU_SOURCE...
Checking src/Subscribe.cpp: _KQUEUE_...
Checking src/Subscribe.cpp: _POLL_...
Checking src/Subscribe.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/Subscribe.cpp: _PREDIXY_TIMER_STATS_...
40/42 files checked 99% done
Checking src/Timer.cpp ...
src/Timer.cpp:7:0: information: Include file: <stdio.h> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <stdio.h>
^
src/Timer.cpp:8:0: information: Include file: <algorithm> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <algorithm>
^
src/Timer.cpp:9:0: information: Include file: <iostream> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <iostream>
^
src/Timer.cpp:49:28: style: Parameter 'p1' can be declared as pointer to const [constParameterPointer]
            [](TimerPoint* p1, TimerPoint* p2)
                           ^
src/Timer.cpp:49:44: style: Parameter 'p2' can be declared as pointer to const [constParameterPointer]
            [](TimerPoint* p1, TimerPoint* p2)
                                           ^
src/Timer.cpp:53:14: style: Variable 'p' can be declared as pointer to const [constVariablePointer]
        auto p = points[i];
             ^
Checking src/Timer.cpp: _PREDIXY_SINGLE_THREAD_...
src/Timer.cpp:23:26: style: Variable 'lck' is assigned a value that is never used. [unreadVariable]
    UniqueLock<Mutex> lck(Mtx);
                         ^
Checking src/Timer.cpp: _PREDIXY_TIMER_STATS_...
41/42 files checked 99% done
Checking src/main.cpp ...
src/main.cpp:7:0: information: Include file: <iostream> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <iostream>
^
src/main.cpp:8:0: information: Include file: <exception> not found. Please note: Cppcheck does not need standard library headers to get proper results. [missingIncludeSystem]
#include <exception>
^
src/main.cpp:0:0: information: Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches. [normalCheckLevelMaxBranches]

^
Checking src/main.cpp: _EPOLL_...
Checking src/main.cpp: _GNU_SOURCE...
Checking src/main.cpp: _KQUEUE_...
Checking src/main.cpp: _POLL_...
Checking src/main.cpp: _PREDIXY_SINGLE_THREAD_...
Checking src/main.cpp: _PREDIXY_TIMER_STATS_...
42/42 files checked 100% done
src/ClusterServerPool.h:24:0: style: The function 'servers' is never used. [unusedFunction]
    const std::vector<Server*>& servers() const
^
src/Command.h:223:0: style: The function 'isMultiKey' is never used. [unusedFunction]
    bool isMultiKey() const
^
src/Command.h:227:0: style: The function 'isSMultiKey' is never used. [unusedFunction]
    bool isSMultiKey() const
^
src/Command.h:231:0: style: The function 'isMultiKeyVal' is never used. [unusedFunction]
    bool isMultiKeyVal() const
^
src/Command.h:235:0: style: The function 'isAnyMulti' is never used. [unusedFunction]
    bool isAnyMulti() const
^
src/ConnectConnection.h:45:0: style: The function 'isAuth' is never used. [unusedFunction]
    bool isAuth() const
^
src/ConnectConnection.h:53:0: style: The function 'readonly' is never used. [unusedFunction]
    bool readonly() const
^
src/ConnectConnection.h:57:0: style: The function 'setReadonly' is never used. [unusedFunction]
    void setReadonly(bool v)
^
src/ConnectConnection.h:73:0: style: The function 'pendRequestCount' is never used. [unusedFunction]
    int pendRequestCount() const
^
src/DC.h:44:0: style: The function 'getReadPolicy' is never used. [unusedFunction]
    const DCReadPolicy& getReadPolicy(DC* oth) const
^
src/Deque.h:163:0: style: The function 'pop_back' is never used. [unusedFunction]
    P pop_back()
^
src/ID.h:45:0: style: The function 'maxId' is never used. [unusedFunction]
    static int maxId()
^
src/Proxy.h:60:0: style: The function 'supportSubscribe' is never used. [unusedFunction]
    bool supportSubscribe() const
^
src/Request.h:145:0: style: The function 'redirectCnt' is never used. [unusedFunction]
    int redirectCnt() const
^
src/RequestParser.h:77:0: style: The function 'command' is never used. [unusedFunction]
    const Command* command() const
^
src/Response.h:86:0: style: The function 'isInteger' is never used. [unusedFunction]
    bool isInteger() const
^
src/Server.h:55:0: style: The function 'isMaster' is never used. [unusedFunction]
    bool isMaster() const
^
src/Server.h:59:0: style: The function 'isSlave' is never used. [unusedFunction]
    bool isSlave() const
^
src/ServerPool.h:55:0: style: The function 'refreshInterval' is never used. [unusedFunction]
    long refreshInterval() const
^
src/Subscribe.h:26:0: style: The function 'inPendSub' is never used. [unusedFunction]
    bool inPendSub() const
^
src/Sync.h:34:0: style: The function 'lock' is never used. [unusedFunction]
    void lock() const
^
src/Sync.h:37:0: style: The function 'unlock' is never used. [unusedFunction]
    void unlock() const
^
src/Timer.h:70:0: style: The function 'restart' is never used. [unusedFunction]
    void restart()
^
src/Transaction.h:25:0: style: The function 'inPendWatch' is never used. [unusedFunction]
    bool inPendWatch() const
^
src/Transaction.h:42:0: style: The function 'inWatch' is never used. [unusedFunction]
    bool inWatch() const
^
src/Transaction.h:50:0: style: The function 'decrWatch' is never used. [unusedFunction]
    int decrWatch()
^
src/Transaction.h:60:0: style: The function 'inPendMulti' is never used. [unusedFunction]
    bool inPendMulti() const
^
src/Util.h:60:0: style: The function 'nowSec' is never used. [unusedFunction]
    inline long nowSec()
^
src/Util.h:64:0: style: The function 'nowMSec' is never used. [unusedFunction]
    inline long nowMSec()
^
src/Util.h:72:0: style: The function 'elapsedSec' is never used. [unusedFunction]
    inline long elapsedSec()
^
src/Util.h:76:0: style: The function 'elapsedMSec' is never used. [unusedFunction]
    inline long elapsedMSec()
^
nofile:0:0: information: Active checkers: 167/856 (use --checkers-report=<filename> to see details) [checkersReport]

