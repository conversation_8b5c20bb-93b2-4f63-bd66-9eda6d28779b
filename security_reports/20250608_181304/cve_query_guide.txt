=== CVE查询建议 ===
建议在以下网站查询相关CVE:
1. https://cve.mitre.org/
2. https://nvd.nist.gov/
3. https://www.cvedetails.com/

搜索关键词:
- Redis proxy
- C++ network programming
- Socket programming vulnerabilities
- Buffer overflow C++

重点关注的CVE类型:
- CWE-120: Buffer Copy without Checking Size of Input
- CWE-125: Out-of-bounds Read
- CWE-787: Out-of-bounds Write
- CWE-416: Use After Free
- CWE-476: NULL Pointer Dereference
