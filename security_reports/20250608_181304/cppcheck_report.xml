<?xml version="1.0" encoding="UTF-8"?>
<results version="2">
    <cppcheck version="2.17.1"/>
    <errors>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;stdio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;stdio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Predixy.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Predixy.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Predixy.h" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;limits.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;limits.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Predixy.h" line="13" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;errno.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;errno.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Predixy.h" line="14" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Predixy.h" line="15" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/uio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/uio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Predixy.h" line="16" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;limits.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;limits.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Common.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;atomic&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;atomic&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Sync.h" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;mutex&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;mutex&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Sync.h" line="13" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/HashFunc.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;stdint.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;stdint.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/HashFunc.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ID.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/uio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/uio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/IOVec.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;stdio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;stdio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Util.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Util.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Util.h" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;errno.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;errno.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Util.h" line="13" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;chrono&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;chrono&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Util.h" line="14" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/PString.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/PString.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;stdio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;stdio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/PString.h" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;stdarg.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;stdarg.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/PString.h" line="13" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/PString.h" line="14" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Timer.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;chrono&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;chrono&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Timer.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;exception&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;exception&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Exception.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;stdarg.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;stdarg.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Exception.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;stdio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;stdio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Exception.h" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;stdarg.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;stdarg.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Logger.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Logger.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;atomic&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;atomic&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Logger.h" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;mutex&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;mutex&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Logger.h" line="13" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;condition_variable&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;condition_variable&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Logger.h" line="14" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;thread&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;thread&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Logger.h" line="15" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;stdlib.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;stdlib.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Alloc.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;iostream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;iostream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Alloc.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;thread&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;thread&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Alloc.h" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;unordered_map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;unordered_map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Command.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/types.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/types.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/socket.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/socket.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.h" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/un.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/un.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.h" line="13" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/uio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/uio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.h" line="14" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;netinet/in.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;netinet/in.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.h" line="15" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;netinet/ip.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;netinet/ip.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.h" line="16" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;netinet/tcp.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;netinet/tcp.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.h" line="17" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;fcntl.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;fcntl.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.h" line="18" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;arpa/inet.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;arpa/inet.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.h" line="19" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;netdb.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;netdb.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.h" line="20" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;errno.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;errno.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.h" line="21" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;stdint.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;stdint.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Transaction.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/RequestParser.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;limits.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;limits.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Conf.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Conf.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Conf.h" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Conf.h" line="13" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Conf.h" line="14" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;set&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;set&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Conf.h" line="15" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Conf.h" line="16" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;bitset&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;bitset&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Conf.h" line="17" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConfParser.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConfParser.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;set&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;set&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConfParser.h" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConfParser.h" line="13" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;fstream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;fstream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConfParser.h" line="14" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Auth.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;set&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;set&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Auth.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Auth.h" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Enums.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Enums.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Handler.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;algorithm&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;algorithm&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/LatencyMonitor.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/LatencyMonitor.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/LatencyMonitor.h" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConnectConnectionPool.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConnectSocket.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Server.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/DC.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/DC.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Proxy.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ServerPool.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ServerPool.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ServerPool.h" line="14" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ClusterServerPool.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ClusterServerPool.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ClusterServerPool.h" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/StandaloneServerPool.h" line="10" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/AcceptConnection.cpp">
            <location file="src/AcceptConnection.cpp" line="0" column="0"/>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;ServerPool::mServerTimeout&apos; is not initialized in the constructor." verbose="Member variable &apos;ServerPool::mServerTimeout&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/ServerPool.h" line="108" column="5"/>
            <symbol>ServerPool::mServerTimeout</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;ServerPool::mKeepAlive&apos; is not initialized in the constructor." verbose="Member variable &apos;ServerPool::mKeepAlive&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/ServerPool.h" line="108" column="5"/>
            <symbol>ServerPool::mKeepAlive</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;SString &lt; Const :: MaxAddrLen &gt;::mBuf&apos; is not initialized in the copy constructor." verbose="Member variable &apos;SString &lt; Const :: MaxAddrLen &gt;::mBuf&apos; is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/PString.h" line="185" column="5"/>
            <symbol>SString &lt; Const :: MaxAddrLen &gt;::mBuf</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;SString &lt; Const :: MaxServNameLen &gt;::mBuf&apos; is not initialized in the copy constructor." verbose="Member variable &apos;SString &lt; Const :: MaxServNameLen &gt;::mBuf&apos; is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/PString.h" line="185" column="5"/>
            <symbol>SString &lt; Const :: MaxServNameLen &gt;::mBuf</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;ConnectSocket&apos; defines member function with name &apos;close&apos; also defined in its parent class &apos;Socket&apos;." verbose="The class &apos;ConnectSocket&apos; defines member function with name &apos;close&apos; also defined in its parent class &apos;Socket&apos;." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/ConnectSocket.h" line="27" column="10" info="Derived function &apos;ConnectSocket::close&apos;"/>
            <location file="src/Socket.h" line="59" column="10" info="Parent function &apos;Socket::close&apos;"/>
            <symbol>ConnectSocket</symbol>
            <symbol>close</symbol>
            <symbol>Socket</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;AcceptConnection&apos; defines member function with name &apos;close&apos; also defined in its parent class &apos;Socket&apos;." verbose="The class &apos;AcceptConnection&apos; defines member function with name &apos;close&apos; also defined in its parent class &apos;Socket&apos;." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/AcceptConnection.cpp" line="27" column="24" info="Derived function &apos;AcceptConnection::close&apos;"/>
            <location file="src/Socket.h" line="59" column="10" info="Parent function &apos;Socket::close&apos;"/>
            <symbol>AcceptConnection</symbol>
            <symbol>close</symbol>
            <symbol>Socket</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;ClusterServerPool&apos; defines member function with name &apos;getServer&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;ClusterServerPool&apos; defines member function with name &apos;getServer&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/ClusterServerPool.h" line="29" column="13" info="Derived function &apos;ClusterServerPool::getServer&apos;"/>
            <location file="src/ServerPool.h" line="89" column="13" info="Parent function &apos;ServerPool::getServer&apos;"/>
            <symbol>ClusterServerPool</symbol>
            <symbol>getServer</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;ClusterServerPool&apos; defines member function with name &apos;refreshRequest&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;ClusterServerPool&apos; defines member function with name &apos;refreshRequest&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/ClusterServerPool.h" line="30" column="10" info="Derived function &apos;ClusterServerPool::refreshRequest&apos;"/>
            <location file="src/ServerPool.h" line="97" column="10" info="Parent function &apos;ServerPool::refreshRequest&apos;"/>
            <symbol>ClusterServerPool</symbol>
            <symbol>refreshRequest</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;ClusterServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;ClusterServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/ClusterServerPool.h" line="31" column="10" info="Derived function &apos;ClusterServerPool::handleResponse&apos;"/>
            <location file="src/ServerPool.h" line="101" column="10" info="Parent function &apos;ServerPool::handleResponse&apos;"/>
            <symbol>ClusterServerPool</symbol>
            <symbol>handleResponse</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;ClusterServerPool&apos; defines member function with name &apos;iter&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;ClusterServerPool&apos; defines member function with name &apos;iter&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/ClusterServerPool.h" line="37" column="13" info="Derived function &apos;ClusterServerPool::iter&apos;"/>
            <location file="src/ServerPool.h" line="93" column="13" info="Parent function &apos;ServerPool::iter&apos;"/>
            <symbol>ClusterServerPool</symbol>
            <symbol>iter</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;getServer&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;getServer&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/StandaloneServerPool.h" line="22" column="13" info="Derived function &apos;StandaloneServerPool::getServer&apos;"/>
            <location file="src/ServerPool.h" line="89" column="13" info="Parent function &apos;ServerPool::getServer&apos;"/>
            <symbol>StandaloneServerPool</symbol>
            <symbol>getServer</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;iter&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;iter&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/StandaloneServerPool.h" line="23" column="13" info="Derived function &apos;StandaloneServerPool::iter&apos;"/>
            <location file="src/ServerPool.h" line="93" column="13" info="Parent function &apos;ServerPool::iter&apos;"/>
            <symbol>StandaloneServerPool</symbol>
            <symbol>iter</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;refreshRequest&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;refreshRequest&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/StandaloneServerPool.h" line="27" column="10" info="Derived function &apos;StandaloneServerPool::refreshRequest&apos;"/>
            <location file="src/ServerPool.h" line="97" column="10" info="Parent function &apos;ServerPool::refreshRequest&apos;"/>
            <symbol>StandaloneServerPool</symbol>
            <symbol>refreshRequest</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/StandaloneServerPool.h" line="28" column="10" info="Derived function &apos;StandaloneServerPool::handleResponse&apos;"/>
            <location file="src/ServerPool.h" line="101" column="10" info="Parent function &apos;ServerPool::handleResponse&apos;"/>
            <symbol>StandaloneServerPool</symbol>
            <symbol>handleResponse</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;Hash&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;Hash&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/HashFunc.h" line="28" column="5"/>
            <symbol>Hash</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;IDUnique&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;IDUnique&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/ID.h" line="64" column="5"/>
            <symbol>IDUnique</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;StrErrorImpl&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;StrErrorImpl&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Util.h" line="33" column="5"/>
            <symbol>StrErrorImpl</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;String&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;String&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/PString.h" line="24" column="5"/>
            <symbol>String</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;String&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;String&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/PString.h" line="34" column="5"/>
            <symbol>String</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;TimerPoint&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;TimerPoint&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Timer.h" line="17" column="5"/>
            <symbol>TimerPoint</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;Timer&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;Timer&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Timer.h" line="56" column="5"/>
            <symbol>Timer</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;Logger&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;Logger&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Logger.h" line="58" column="5"/>
            <symbol>Logger</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;Socket&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;Socket&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Socket.h" line="52" column="5"/>
            <symbol>Socket</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SegmentStr&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SegmentStr&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Buffer.h" line="211" column="5"/>
            <symbol>SegmentStr</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;Request&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;Request&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Request.h" line="56" column="5"/>
            <symbol>Request</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;Request&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;Request&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Request.h" line="57" column="5"/>
            <symbol>Request</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;Response&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;Response&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Response.h" line="49" column="5"/>
            <symbol>Response</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;Distribution&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;Distribution&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Distribution.h" line="20" column="5"/>
            <symbol>Distribution</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;ConfParser&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;ConfParser&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/ConfParser.h" line="73" column="5"/>
            <symbol>ConfParser</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;Auth&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;Auth&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Auth.h" line="18" column="5"/>
            <symbol>Auth</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;Auth&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;Auth&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Auth.h" line="19" column="5"/>
            <symbol>Auth</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;ServerPoolRefreshMethod&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;ServerPoolRefreshMethod&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Enums.h" line="68" column="5"/>
            <symbol>ServerPoolRefreshMethod</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;ClusterServerPool&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;ClusterServerPool&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/ClusterServerPool.h" line="20" column="5"/>
            <symbol>ClusterServerPool</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;StandaloneServerPool&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;StandaloneServerPool&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/StandaloneServerPool.h" line="19" column="5"/>
            <symbol>StandaloneServerPool</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;Handler&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;Handler&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Handler.h" line="24" column="5"/>
            <symbol>Handler</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;EnumBase &lt; ServerPoolRefreshMethod &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;EnumBase &lt; ServerPoolRefreshMethod &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Enums.h" line="25" column="5"/>
            <symbol>EnumBase &lt; ServerPoolRefreshMethod &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SharePtr &lt; Buffer &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SharePtr &lt; Buffer &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Alloc.h" line="171" column="5"/>
            <symbol>SharePtr &lt; Buffer &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SharePtr &lt; Request &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SharePtr &lt; Request &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Alloc.h" line="171" column="5"/>
            <symbol>SharePtr &lt; Request &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SharePtr &lt; Response &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SharePtr &lt; Response &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Alloc.h" line="171" column="5"/>
            <symbol>SharePtr &lt; Response &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SharePtr &lt; AcceptConnection &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SharePtr &lt; AcceptConnection &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Alloc.h" line="171" column="5"/>
            <symbol>SharePtr &lt; AcceptConnection &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; Const :: MaxAddrLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; Const :: MaxAddrLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/PString.h" line="171" column="5"/>
            <symbol>SString &lt; Const :: MaxAddrLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; Const :: MaxAddrLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; Const :: MaxAddrLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/PString.h" line="181" column="5"/>
            <symbol>SString &lt; Const :: MaxAddrLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; Const :: MaxAddrLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; Const :: MaxAddrLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/PString.h" line="190" column="5"/>
            <symbol>SString &lt; Const :: MaxAddrLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; Const :: MaxServNameLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; Const :: MaxServNameLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/PString.h" line="171" column="5"/>
            <symbol>SString &lt; Const :: MaxServNameLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; Const :: MaxServNameLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; Const :: MaxServNameLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/PString.h" line="181" column="5"/>
            <symbol>SString &lt; Const :: MaxServNameLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; Const :: MaxServNameLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; Const :: MaxServNameLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/PString.h" line="190" column="5"/>
            <symbol>SString &lt; Const :: MaxServNameLen &gt;</symbol>
        </error>
        <error id="missingOverride" severity="style" msg="The destructor &apos;~ClusterServerPool&apos; overrides a destructor in a base class but is not marked with a &apos;override&apos; specifier." verbose="The destructor &apos;~ClusterServerPool&apos; overrides a destructor in a base class but is not marked with a &apos;override&apos; specifier." file0="src/AcceptConnection.cpp">
            <location file="src/ClusterServerPool.h" line="21" column="6" info="Destructor in derived class"/>
            <location file="src/ServerPool.h" line="28" column="14" info="Virtual destructor in base class"/>
            <symbol>~ClusterServerPool</symbol>
        </error>
        <error id="missingOverride" severity="style" msg="The destructor &apos;~StandaloneServerPool&apos; overrides a destructor in a base class but is not marked with a &apos;override&apos; specifier." verbose="The destructor &apos;~StandaloneServerPool&apos; overrides a destructor in a base class but is not marked with a &apos;override&apos; specifier." file0="src/AcceptConnection.cpp">
            <location file="src/StandaloneServerPool.h" line="20" column="6" info="Destructor in derived class"/>
            <location file="src/ServerPool.h" line="28" column="14" info="Virtual destructor in base class"/>
            <symbol>~StandaloneServerPool</symbol>
        </error>
        <error id="multiCondition" severity="style" msg="Expression is always false because &apos;else if&apos; condition matches previous condition at line 128." verbose="Expression is always false because &apos;else if&apos; condition matches previous condition at line 128." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/PString.h" line="130" column="32"/>
        </error>
        <error id="knownConditionTrueFalse" severity="style" msg="Return value &apos;mLen==len&apos; is always false" verbose="Return value &apos;mLen==len&apos; is always false" cwe="570" file0="src/AcceptConnection.cpp">
            <location file="src/PString.h" line="242" column="21" info="Return value &apos;mLen==len&apos; is always false"/>
            <location file="src/PString.h" line="241" column="14" info="mLen is assigned &apos;len&lt;Const::MaxAddrLen?len:Const::MaxAddrLen&apos; here."/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Request.h" line="123" column="58"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/ServerPool.h" line="156" column="29"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Alloc.h" line="61" column="22"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Alloc.h" line="70" column="39"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Alloc.h" line="111" column="31"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Deque.h" line="98" column="32"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Deque.h" line="46" column="52"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/List.h" line="71" column="32"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/List.h" line="93" column="28"/>
        </error>
        <error id="knownConditionTrueFalse" severity="style" msg="The comparison &apos;i &gt; 0&apos; is always false." verbose="Finding the same expression on both sides of an operator is suspicious and might indicate a cut and paste or logic error. Please examine this code carefully to determine if it is correct." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/PString.h" line="127" column="15"/>
            <location file="src/PString.h" line="125" column="17" info="&apos;i&apos; is assigned value &apos;0&apos; here."/>
        </error>
        <error id="shadowFunction" severity="style" msg="Local variable &apos;prev&apos; shadows outer function" verbose="Local variable &apos;prev&apos; shadows outer function" cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Deque.h" line="119" column="14" info="Shadow variable"/>
            <location file="src/Deque.h" line="81" column="7" info="Shadowed declaration"/>
            <symbol>prev</symbol>
        </error>
        <error id="shadowFunction" severity="style" msg="Local variable &apos;next&apos; shadows outer function" verbose="Local variable &apos;next&apos; shadows outer function" cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Deque.h" line="120" column="14" info="Shadow variable"/>
            <location file="src/Deque.h" line="85" column="7" info="Shadowed declaration"/>
            <symbol>next</symbol>
        </error>
        <error id="shadowFunction" severity="style" msg="Local variable &apos;next&apos; shadows outer function" verbose="Local variable &apos;next&apos; shadows outer function" cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Deque.h" line="143" column="18" info="Shadow variable"/>
            <location file="src/Deque.h" line="85" column="7" info="Shadowed declaration"/>
            <symbol>next</symbol>
        </error>
        <error id="shadowFunction" severity="style" msg="Local variable &apos;prev&apos; shadows outer function" verbose="Local variable &apos;prev&apos; shadows outer function" cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Deque.h" line="144" column="22" info="Shadow variable"/>
            <location file="src/Deque.h" line="81" column="7" info="Shadowed declaration"/>
            <symbol>prev</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;res&apos; can be declared as pointer to const" verbose="Variable &apos;res&apos; can be declared as pointer to const" cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/AcceptConnection.cpp" line="74" column="14" info="Variable &apos;res&apos; can be declared as pointer to const"/>
            <symbol>res</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;serv&apos; can be declared as pointer to const" verbose="Parameter &apos;serv&apos; can be declared as pointer to const" cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Handler.h" line="58" column="33" info="Parameter &apos;serv&apos; can be declared as pointer to const"/>
            <symbol>serv</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;serv&apos; can be declared as pointer to const" verbose="Parameter &apos;serv&apos; can be declared as pointer to const" cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Handler.h" line="65" column="37" info="Parameter &apos;serv&apos; can be declared as pointer to const"/>
            <symbol>serv</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;serv&apos; can be declared as pointer to const" verbose="Parameter &apos;serv&apos; can be declared as pointer to const" cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Handler.h" line="70" column="38" info="Parameter &apos;serv&apos; can be declared as pointer to const"/>
            <symbol>serv</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;oth&apos; can be declared as pointer to const" verbose="Parameter &apos;oth&apos; can be declared as pointer to const" cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/DC.h" line="31" column="18" info="Parameter &apos;oth&apos; can be declared as pointer to const"/>
            <symbol>oth</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;oth&apos; can be declared as pointer to const" verbose="Parameter &apos;oth&apos; can be declared as pointer to const" cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/DC.h" line="36" column="29" info="Parameter &apos;oth&apos; can be declared as pointer to const"/>
            <symbol>oth</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;oth&apos; can be declared as pointer to const" verbose="Parameter &apos;oth&apos; can be declared as pointer to const" cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/DC.h" line="40" column="27" info="Parameter &apos;oth&apos; can be declared as pointer to const"/>
            <symbol>oth</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;oth&apos; can be declared as pointer to const" verbose="Parameter &apos;oth&apos; can be declared as pointer to const" cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/DC.h" line="44" column="43" info="Parameter &apos;oth&apos; can be declared as pointer to const"/>
            <symbol>oth</symbol>
        </error>
        <error id="useStlAlgorithm" severity="style" msg="Consider using std::find_if algorithm instead of a raw loop." verbose="Consider using std::find_if algorithm instead of a raw loop." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/Enums.h" line="48" column="47"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/EpollMultiplexor.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/epoll.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/epoll.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/EpollMultiplexor.h" line="11" column="0"/>
        </error>
        <error id="missingOverride" severity="style" msg="The destructor &apos;~EpollMultiplexor&apos; overrides a destructor in a base class but is not marked with a &apos;override&apos; specifier." verbose="The destructor &apos;~EpollMultiplexor&apos; overrides a destructor in a base class but is not marked with a &apos;override&apos; specifier." file0="src/AcceptConnection.cpp">
            <location file="src/EpollMultiplexor.h" line="23" column="6" info="Destructor in derived class"/>
            <location file="src/Multiplexor.h" line="24" column="14" info="Virtual destructor in base class"/>
            <symbol>~EpollMultiplexor</symbol>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/KqueueMultiplexor.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/types.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/types.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/KqueueMultiplexor.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/event.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/event.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/KqueueMultiplexor.h" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/time.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/time.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/KqueueMultiplexor.h" line="13" column="0"/>
        </error>
        <error id="missingOverride" severity="style" msg="The destructor &apos;~KqueueMultiplexor&apos; overrides a destructor in a base class but is not marked with a &apos;override&apos; specifier." verbose="The destructor &apos;~KqueueMultiplexor&apos; overrides a destructor in a base class but is not marked with a &apos;override&apos; specifier." file0="src/AcceptConnection.cpp">
            <location file="src/KqueueMultiplexor.h" line="25" column="6" info="Destructor in derived class"/>
            <location file="src/Multiplexor.h" line="24" column="14" info="Virtual destructor in base class"/>
            <symbol>~KqueueMultiplexor</symbol>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/PollMultiplexor.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;poll.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;poll.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/PollMultiplexor.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/PollMultiplexor.h" line="12" column="0"/>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;PollMultiplexor&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;PollMultiplexor&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/AcceptConnection.cpp">
            <location file="src/PollMultiplexor.h" line="21" column="5"/>
            <symbol>PollMultiplexor</symbol>
        </error>
        <error id="missingOverride" severity="style" msg="The destructor &apos;~PollMultiplexor&apos; overrides a destructor in a base class but is not marked with a &apos;override&apos; specifier." verbose="The destructor &apos;~PollMultiplexor&apos; overrides a destructor in a base class but is not marked with a &apos;override&apos; specifier." file0="src/AcceptConnection.cpp">
            <location file="src/PollMultiplexor.h" line="22" column="6" info="Destructor in derived class"/>
            <location file="src/Multiplexor.h" line="24" column="14" info="Virtual destructor in base class"/>
            <symbol>~PollMultiplexor</symbol>
        </error>
        <error id="unreadVariable" severity="style" msg="Variable &apos;lck&apos; is assigned a value that is never used." verbose="Variable &apos;lck&apos; is assigned a value that is never used." cwe="563" file0="src/AcceptConnection.cpp">
            <location file="src/ServerPool.h" line="85" column="30"/>
            <symbol>lck</symbol>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/AcceptSocket.cpp">
            <location file="src/AcceptSocket.cpp" line="19" column="31"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/AcceptSocket.cpp">
            <location file="src/AcceptSocket.cpp" line="20" column="32"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/AcceptSocket.cpp">
            <location file="src/AcceptSocket.cpp" line="28" column="32"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/AcceptSocket.cpp">
            <location file="src/AcceptSocket.cpp" line="29" column="33"/>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SharePtr&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SharePtr&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Alloc.cpp">
            <location file="src/Alloc.h" line="171" column="5"/>
            <symbol>SharePtr</symbol>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;algorithm&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;algorithm&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Auth.cpp" line="7" column="0"/>
        </error>
        <error id="pointerOutOfBoundsCond" severity="portability" msg="Undefined behaviour, when &apos;n&apos; is -3 the pointer arithmetic &apos;mBuf+n&apos; is out of bounds." verbose="Undefined behaviour, when &apos;n&apos; is -3 the pointer arithmetic &apos;mBuf+n&apos; is out of bounds." cwe="758" file0="src/Auth.cpp">
            <location file="src/PString.h" line="308" column="35" info="Pointer arithmetic overflow"/>
            <location file="src/PString.h" line="307" column="34" info="Assuming that condition &apos;n+4&lt;Size&apos; is not redundant"/>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;SString::mBuf&apos; is not initialized in the copy constructor." verbose="Member variable &apos;SString::mBuf&apos; is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/Auth.cpp">
            <location file="src/PString.h" line="185" column="5"/>
            <symbol>SString::mBuf</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;Auth&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;Auth&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398" file0="src/Auth.cpp">
            <location file="src/Auth.cpp" line="27" column="9"/>
            <symbol>Auth</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;Auth&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;Auth&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398" file0="src/Auth.cpp">
            <location file="src/Auth.cpp" line="27" column="9"/>
            <symbol>Auth</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Auth.cpp">
            <location file="src/PString.h" line="171" column="5"/>
            <symbol>SString</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Auth.cpp">
            <location file="src/PString.h" line="181" column="5"/>
            <symbol>SString</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Auth.cpp">
            <location file="src/PString.h" line="190" column="5"/>
            <symbol>SString</symbol>
        </error>
        <error id="memleak" severity="error" msg="Memory leak: kp" verbose="Memory leak: kp" cwe="401" file0="src/Auth.cpp">
            <location file="src/Auth.cpp" line="40" column="5"/>
            <symbol>kp</symbol>
        </error>
        <error id="constVariableReference" severity="style" msg="Variable &apos;i&apos; can be declared as reference to const" verbose="Variable &apos;i&apos; can be declared as reference to const" cwe="398" file0="src/Auth.cpp">
            <location file="src/Auth.cpp" line="88" column="16" info="Variable &apos;i&apos; can be declared as reference to const"/>
            <symbol>i</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;req&apos; can be declared as pointer to const" verbose="Parameter &apos;req&apos; can be declared as pointer to const" cwe="398" file0="src/Auth.cpp">
            <location file="src/Auth.cpp" line="53" column="32" info="Parameter &apos;req&apos; can be declared as pointer to const"/>
            <symbol>req</symbol>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/Auth.cpp">
            <location file="src/Auth.cpp" line="0" column="0"/>
        </error>
        <error id="uninitMemberVarPrivate" severity="warning" msg="Member variable &apos;Buffer::mDat&apos; is not initialized in the constructor." verbose="Member variable &apos;Buffer::mDat&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/Buffer.cpp">
            <location file="src/Buffer.cpp" line="14" column="9"/>
            <symbol>Buffer::mDat</symbol>
        </error>
        <error id="shadowFunction" severity="style" msg="Local variable &apos;end&apos; shadows outer function" verbose="Local variable &apos;end&apos; shadows outer function" cwe="398" file0="src/Buffer.cpp">
            <location file="src/Buffer.cpp" line="339" column="10" info="Shadow variable"/>
            <location file="src/Buffer.h" line="183" column="16" info="Shadowed declaration"/>
            <symbol>end</symbol>
        </error>
        <error id="shadowFunction" severity="style" msg="Local variable &apos;end&apos; shadows outer function" verbose="Local variable &apos;end&apos; shadows outer function" cwe="398" file0="src/Buffer.cpp">
            <location file="src/Buffer.cpp" line="365" column="10" info="Shadow variable"/>
            <location file="src/Buffer.h" line="183" column="16" info="Shadowed declaration"/>
            <symbol>end</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;buf&apos; can be declared as pointer to const" verbose="Variable &apos;buf&apos; can be declared as pointer to const" cwe="398" file0="src/Buffer.cpp">
            <location file="src/Buffer.cpp" line="218" column="13" info="Variable &apos;buf&apos; can be declared as pointer to const"/>
            <symbol>buf</symbol>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ClusterNodesParser.cpp" line="7" column="0"/>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;ClusterNodesParser::mRole&apos; is not initialized in the constructor." verbose="Member variable &apos;ClusterNodesParser::mRole&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/ClusterNodesParser.cpp" line="11" column="21"/>
            <symbol>ClusterNodesParser::mRole</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;ClusterNodesParser::mFieldCnt&apos; is not initialized in the constructor." verbose="Member variable &apos;ClusterNodesParser::mFieldCnt&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/ClusterNodesParser.cpp" line="11" column="21"/>
            <symbol>ClusterNodesParser::mFieldCnt</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;ClusterNodesParser::mSlotBegin&apos; is not initialized in the constructor." verbose="Member variable &apos;ClusterNodesParser::mSlotBegin&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/ClusterNodesParser.cpp" line="11" column="21"/>
            <symbol>ClusterNodesParser::mSlotBegin</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;ClusterNodesParser::mSlotEnd&apos; is not initialized in the constructor." verbose="Member variable &apos;ClusterNodesParser::mSlotEnd&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/ClusterNodesParser.cpp" line="11" column="21"/>
            <symbol>ClusterNodesParser::mSlotEnd</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;SString &lt; NodeIdLen &gt;::mBuf&apos; is not initialized in the copy constructor." verbose="Member variable &apos;SString &lt; NodeIdLen &gt;::mBuf&apos; is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/PString.h" line="185" column="5"/>
            <symbol>SString &lt; NodeIdLen &gt;::mBuf</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;SString &lt; AddrLen &gt;::mBuf&apos; is not initialized in the copy constructor." verbose="Member variable &apos;SString &lt; AddrLen &gt;::mBuf&apos; is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/PString.h" line="185" column="5"/>
            <symbol>SString &lt; AddrLen &gt;::mBuf</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;SString &lt; FlagsLen &gt;::mBuf&apos; is not initialized in the copy constructor." verbose="Member variable &apos;SString &lt; FlagsLen &gt;::mBuf&apos; is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/PString.h" line="185" column="5"/>
            <symbol>SString &lt; FlagsLen &gt;::mBuf</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; NodeIdLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; NodeIdLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/PString.h" line="171" column="5"/>
            <symbol>SString &lt; NodeIdLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; NodeIdLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; NodeIdLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/PString.h" line="181" column="5"/>
            <symbol>SString &lt; NodeIdLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; NodeIdLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; NodeIdLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/PString.h" line="190" column="5"/>
            <symbol>SString &lt; NodeIdLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; AddrLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; AddrLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/PString.h" line="171" column="5"/>
            <symbol>SString &lt; AddrLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; AddrLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; AddrLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/PString.h" line="181" column="5"/>
            <symbol>SString &lt; AddrLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; AddrLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; AddrLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/PString.h" line="190" column="5"/>
            <symbol>SString &lt; AddrLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; FlagsLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; FlagsLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/PString.h" line="171" column="5"/>
            <symbol>SString &lt; FlagsLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; FlagsLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; FlagsLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/PString.h" line="181" column="5"/>
            <symbol>SString &lt; FlagsLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; FlagsLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; FlagsLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/ClusterNodesParser.cpp">
            <location file="src/PString.h" line="190" column="5"/>
            <symbol>SString &lt; FlagsLen &gt;</symbol>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/ClusterNodesParser.cpp">
            <location file="src/ClusterNodesParser.cpp" line="0" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;time.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;time.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ClusterServerPool.cpp" line="7" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ServerGroup.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;deque&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;deque&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ServerGroup.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;vector&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ServerGroup.h" line="12" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/ClusterServerPool.cpp">
            <location file="src/ClusterServerPool.cpp" line="0" column="0"/>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;AcceptConnection&apos; defines member function with name &apos;close&apos; also defined in its parent class &apos;Socket&apos;." verbose="The class &apos;AcceptConnection&apos; defines member function with name &apos;close&apos; also defined in its parent class &apos;Socket&apos;." cwe="398" file0="src/ClusterServerPool.cpp">
            <location file="src/AcceptConnection.h" line="36" column="10" info="Derived function &apos;AcceptConnection::close&apos;"/>
            <location file="src/Socket.h" line="59" column="10" info="Parent function &apos;Socket::close&apos;"/>
            <symbol>AcceptConnection</symbol>
            <symbol>close</symbol>
            <symbol>Socket</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;ClusterServerPool&apos; defines member function with name &apos;getServer&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;ClusterServerPool&apos; defines member function with name &apos;getServer&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/ClusterServerPool.cpp">
            <location file="src/ClusterServerPool.cpp" line="36" column="28" info="Derived function &apos;ClusterServerPool::getServer&apos;"/>
            <location file="src/ServerPool.h" line="89" column="13" info="Parent function &apos;ServerPool::getServer&apos;"/>
            <symbol>ClusterServerPool</symbol>
            <symbol>getServer</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;ClusterServerPool&apos; defines member function with name &apos;refreshRequest&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;ClusterServerPool&apos; defines member function with name &apos;refreshRequest&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/ClusterServerPool.cpp">
            <location file="src/ClusterServerPool.cpp" line="82" column="25" info="Derived function &apos;ClusterServerPool::refreshRequest&apos;"/>
            <location file="src/ServerPool.h" line="97" column="10" info="Parent function &apos;ServerPool::refreshRequest&apos;"/>
            <symbol>ClusterServerPool</symbol>
            <symbol>refreshRequest</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;ClusterServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;ClusterServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/ClusterServerPool.cpp">
            <location file="src/ClusterServerPool.cpp" line="89" column="25" info="Derived function &apos;ClusterServerPool::handleResponse&apos;"/>
            <location file="src/ServerPool.h" line="101" column="10" info="Parent function &apos;ServerPool::handleResponse&apos;"/>
            <symbol>ClusterServerPool</symbol>
            <symbol>handleResponse</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="returnByReference" severity="performance" msg="Function &apos;name()&apos; should return member &apos;mName&apos; by const reference." verbose="Function &apos;name()&apos; should return member &apos;mName&apos; by const reference." file0="src/ClusterServerPool.cpp">
            <location file="src/ServerGroup.h" line="23" column="12"/>
        </error>
        <error id="constVariableReference" severity="style" msg="Variable &apos;sc&apos; can be declared as reference to const" verbose="Variable &apos;sc&apos; can be declared as reference to const" cwe="398" file0="src/ClusterServerPool.cpp">
            <location file="src/ClusterServerPool.cpp" line="74" column="16" info="Variable &apos;sc&apos; can be declared as reference to const"/>
            <symbol>sc</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;g&apos; can be declared as pointer to const" verbose="Variable &apos;g&apos; can be declared as pointer to const" cwe="398" file0="src/ClusterServerPool.cpp">
            <location file="src/ClusterServerPool.cpp" line="48" column="18" info="Variable &apos;g&apos; can be declared as pointer to const"/>
            <symbol>g</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;old&apos; can be declared as pointer to const" verbose="Parameter &apos;old&apos; can be declared as pointer to const" cwe="398" file0="src/ClusterServerPool.cpp">
            <location file="src/ClusterServerPool.cpp" line="55" column="65" info="Parameter &apos;old&apos; can be declared as pointer to const"/>
            <symbol>old</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;s&apos; can be declared as pointer to const" verbose="Parameter &apos;s&apos; can be declared as pointer to const" cwe="398" file0="src/ClusterServerPool.cpp">
            <location file="src/ClusterServerPool.cpp" line="89" column="71" info="Parameter &apos;s&apos; can be declared as pointer to const"/>
            <symbol>s</symbol>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Command.cpp" line="7" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Command.cpp" line="8" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Command.cpp" line="9" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/Command.cpp">
            <location file="src/Command.cpp" line="0" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;ctype.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;ctype.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Conf.cpp" line="7" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;iostream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;iostream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Conf.cpp" line="8" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sstream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sstream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Conf.cpp" line="9" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;fstream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;fstream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Conf.cpp" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;stdio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;stdio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/LogFileSink.h" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;atomic&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;atomic&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/LogFileSink.h" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/LogFileSink.h" line="12" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="0" column="0"/>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;Conf::mStandaloneServerPool&apos; is not initialized in the constructor." verbose="Member variable &apos;Conf::mStandaloneServerPool&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="43" column="7"/>
            <symbol>Conf::mStandaloneServerPool</symbol>
        </error>
        <error id="shadowFunction" severity="style" msg="Local variable &apos;clusterServerPool&apos; shadows outer function" verbose="Local variable &apos;clusterServerPool&apos; shadows outer function" cwe="398" file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="127" column="29" info="Shadow variable"/>
            <location file="src/Conf.h" line="176" column="34" info="Shadowed declaration"/>
            <symbol>clusterServerPool</symbol>
        </error>
        <error id="shadowFunction" severity="style" msg="Local variable &apos;standaloneServerPool&apos; shadows outer function" verbose="Local variable &apos;standaloneServerPool&apos; shadows outer function" cwe="398" file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="128" column="29" info="Shadow variable"/>
            <location file="src/Conf.h" line="180" column="37" info="Shadowed declaration"/>
            <symbol>standaloneServerPool</symbol>
        </error>
        <error id="shadowFunction" severity="style" msg="Local variable &apos;latencyMonitors&apos; shadows outer function" verbose="Local variable &apos;latencyMonitors&apos; shadows outer function" cwe="398" file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="130" column="42" info="Shadow variable"/>
            <location file="src/Conf.h" line="192" column="44" info="Shadowed declaration"/>
            <symbol>latencyMonitors</symbol>
        </error>
        <error id="constVariableReference" severity="style" msg="Variable &apos;g&apos; can be declared as reference to const" verbose="Variable &apos;g&apos; can be declared as reference to const" cwe="398" file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="360" column="20" info="Variable &apos;g&apos; can be declared as reference to const"/>
            <symbol>g</symbol>
        </error>
        <error id="constVariableReference" severity="style" msg="Variable &apos;dc&apos; can be declared as reference to const" verbose="Variable &apos;dc&apos; can be declared as reference to const" cwe="398" file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="576" column="20" info="Variable &apos;dc&apos; can be declared as reference to const"/>
            <symbol>dc</symbol>
        </error>
        <error id="constVariableReference" severity="style" msg="Variable &apos;dc&apos; can be declared as reference to const" verbose="Variable &apos;dc&apos; can be declared as reference to const" cwe="398" file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="586" column="20" info="Variable &apos;dc&apos; can be declared as reference to const"/>
            <symbol>dc</symbol>
        </error>
        <error id="constVariableReference" severity="style" msg="Variable &apos;rp&apos; can be declared as reference to const" verbose="Variable &apos;rp&apos; can be declared as reference to const" cwe="398" file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="587" column="24" info="Variable &apos;rp&apos; can be declared as reference to const"/>
            <symbol>rp</symbol>
        </error>
        <error id="constVariableReference" severity="style" msg="Variable &apos;i&apos; can be declared as reference to const" verbose="Variable &apos;i&apos; can be declared as reference to const" cwe="398" file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="589" column="28" info="Variable &apos;i&apos; can be declared as reference to const"/>
            <symbol>i</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;n&apos; can be declared as pointer to const" verbose="Variable &apos;n&apos; can be declared as pointer to const" cwe="398" file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="104" column="23" info="Variable &apos;n&apos; can be declared as pointer to const"/>
            <symbol>n</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;v&apos; can be declared as pointer to const" verbose="Variable &apos;v&apos; can be declared as pointer to const" cwe="398" file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="111" column="26" info="Variable &apos;v&apos; can be declared as pointer to const"/>
            <symbol>v</symbol>
        </error>
        <error id="useStlAlgorithm" severity="style" msg="Consider using std::find_if algorithm instead of a raw loop." verbose="Consider using std::find_if algorithm instead of a raw loop." cwe="398" file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="361" column="36"/>
        </error>
        <error id="useStlAlgorithm" severity="style" msg="Consider using std::any_of algorithm instead of a raw loop." verbose="Consider using std::any_of algorithm instead of a raw loop." cwe="398" file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="577" column="38"/>
        </error>
        <error id="useStlAlgorithm" severity="style" msg="Consider using std::any_of algorithm instead of a raw loop." verbose="Consider using std::any_of algorithm instead of a raw loop." cwe="398" file0="src/Conf.cpp">
            <location file="src/Conf.cpp" line="590" column="44"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConfParser.cpp" line="7" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;limits.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;limits.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConfParser.cpp" line="8" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConfParser.cpp" line="9" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConfParser.cpp" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;errno.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;errno.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConfParser.cpp" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;iostream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;iostream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConfParser.cpp" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;fstream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;fstream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConfParser.cpp" line="13" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;memory&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;memory&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ConfParser.cpp" line="14" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/ConfParser.cpp">
            <location file="src/ConfParser.cpp" line="0" column="0"/>
        </error>
        <error id="constVariableReference" severity="style" msg="Variable &apos;pre&apos; can be declared as reference to const" verbose="Variable &apos;pre&apos; can be declared as reference to const" cwe="398" file0="src/ConfParser.cpp">
            <location file="src/ConfParser.cpp" line="119" column="32" info="Variable &apos;pre&apos; can be declared as reference to const"/>
            <symbol>pre</symbol>
        </error>
        <error id="useStlAlgorithm" severity="style" msg="Consider using std::any_of algorithm instead of a raw loop." verbose="Consider using std::any_of algorithm instead of a raw loop." cwe="398" file0="src/ConfParser.cpp">
            <location file="src/ConfParser.cpp" line="120" column="67"/>
        </error>
        <error id="useStlAlgorithm" severity="style" msg="Consider using std::count_if algorithm instead of a raw loop." verbose="Consider using std::count_if algorithm instead of a raw loop." cwe="398" file0="src/ConfParser.cpp">
            <location file="src/ConfParser.cpp" line="296" column="19"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/ConnectConnection.cpp">
            <location file="src/ConnectConnection.cpp" line="0" column="0"/>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;req&apos; can be declared as pointer to const" verbose="Variable &apos;req&apos; can be declared as pointer to const" cwe="398" file0="src/ConnectConnection.cpp">
            <location file="src/ConnectConnection.cpp" line="193" column="30" info="Variable &apos;req&apos; can be declared as pointer to const"/>
            <symbol>req</symbol>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/ConnectConnectionPool.cpp">
            <location file="src/ConnectConnectionPool.cpp" line="0" column="0"/>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;sp&apos; can be declared as pointer to const" verbose="Variable &apos;sp&apos; can be declared as pointer to const" cwe="398" file0="src/ConnectConnectionPool.cpp">
            <location file="src/ConnectConnectionPool.cpp" line="125" column="10" info="Variable &apos;sp&apos; can be declared as pointer to const"/>
            <symbol>sp</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;ConnectSocket&apos; defines member function with name &apos;close&apos; also defined in its parent class &apos;Socket&apos;." verbose="The class &apos;ConnectSocket&apos; defines member function with name &apos;close&apos; also defined in its parent class &apos;Socket&apos;." cwe="398" file0="src/ConnectSocket.cpp">
            <location file="src/ConnectSocket.cpp" line="62" column="21" info="Derived function &apos;ConnectSocket::close&apos;"/>
            <location file="src/Socket.h" line="59" column="10" info="Parent function &apos;Socket::close&apos;"/>
            <symbol>ConnectSocket</symbol>
            <symbol>close</symbol>
            <symbol>Socket</symbol>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/ConnectSocket.cpp">
            <location file="src/ConnectSocket.cpp" line="17" column="40"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/ConnectSocket.cpp">
            <location file="src/ConnectSocket.cpp" line="18" column="20"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/ConnectSocket.cpp">
            <location file="src/ConnectSocket.cpp" line="32" column="36"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/ConnectSocket.cpp">
            <location file="src/ConnectSocket.cpp" line="56" column="20"/>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;in&apos; can be declared as pointer to const" verbose="Variable &apos;in&apos; can be declared as pointer to const" cwe="398" file0="src/ConnectSocket.cpp">
            <location file="src/ConnectSocket.cpp" line="18" column="15" info="Variable &apos;in&apos; can be declared as pointer to const"/>
            <symbol>in</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;in&apos; can be declared as pointer to const" verbose="Variable &apos;in&apos; can be declared as pointer to const" cwe="398" file0="src/ConnectSocket.cpp">
            <location file="src/ConnectSocket.cpp" line="56" column="15" info="Variable &apos;in&apos; can be declared as pointer to const"/>
            <symbol>in</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;h&apos; can be declared as pointer to const" verbose="Parameter &apos;h&apos; can be declared as pointer to const" cwe="398" file0="src/Connection.cpp">
            <location file="src/Connection.cpp" line="17" column="42" info="Parameter &apos;h&apos; can be declared as pointer to const"/>
            <symbol>h</symbol>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/DC.cpp">
            <location file="src/DC.cpp" line="0" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Distribution.cpp" line="7" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Distribution.cpp" line="8" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/EpollMultiplexor.cpp">
            <location file="src/EpollMultiplexor.cpp" line="0" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Handler.cpp" line="7" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/types.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/types.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Handler.cpp" line="8" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/time.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/time.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Handler.cpp" line="9" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;time.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;time.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Handler.cpp" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;signal.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;signal.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Handler.cpp" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/resource.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/resource.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Handler.cpp" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;iostream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;iostream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Handler.cpp" line="13" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;map&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/SentinelServerPool.h" line="10" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="0" column="0"/>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;SString &lt; Const :: MaxKeyLen &gt;::mBuf&apos; is not initialized in the copy constructor." verbose="Member variable &apos;SString &lt; Const :: MaxKeyLen &gt;::mBuf&apos; is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/Handler.cpp">
            <location file="src/PString.h" line="185" column="5"/>
            <symbol>SString &lt; Const :: MaxKeyLen &gt;::mBuf</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;SString &lt; 32 &gt;::mBuf&apos; is not initialized in the copy constructor." verbose="Member variable &apos;SString &lt; 32 &gt;::mBuf&apos; is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/Handler.cpp">
            <location file="src/PString.h" line="185" column="5"/>
            <symbol>SString &lt; 32 &gt;::mBuf</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;SString &lt; 512 &gt;::mBuf&apos; is not initialized in the copy constructor." verbose="Member variable &apos;SString &lt; 512 &gt;::mBuf&apos; is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/Handler.cpp">
            <location file="src/PString.h" line="185" column="5"/>
            <symbol>SString &lt; 512 &gt;::mBuf</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;Handler&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;Handler&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="27" column="5"/>
            <symbol>Handler</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;Handler&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;Handler&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="27" column="5"/>
            <symbol>Handler</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;SentinelServerPool&apos; defines member function with name &apos;getServer&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;SentinelServerPool&apos; defines member function with name &apos;getServer&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/Handler.cpp">
            <location file="src/SentinelServerPool.h" line="22" column="13" info="Derived function &apos;SentinelServerPool::getServer&apos;"/>
            <location file="src/ServerPool.h" line="89" column="13" info="Parent function &apos;ServerPool::getServer&apos;"/>
            <symbol>SentinelServerPool</symbol>
            <symbol>getServer</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;SentinelServerPool&apos; defines member function with name &apos;iter&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;SentinelServerPool&apos; defines member function with name &apos;iter&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/Handler.cpp">
            <location file="src/SentinelServerPool.h" line="23" column="13" info="Derived function &apos;SentinelServerPool::iter&apos;"/>
            <location file="src/ServerPool.h" line="93" column="13" info="Parent function &apos;ServerPool::iter&apos;"/>
            <symbol>SentinelServerPool</symbol>
            <symbol>iter</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;SentinelServerPool&apos; defines member function with name &apos;refreshRequest&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;SentinelServerPool&apos; defines member function with name &apos;refreshRequest&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/Handler.cpp">
            <location file="src/SentinelServerPool.h" line="27" column="10" info="Derived function &apos;SentinelServerPool::refreshRequest&apos;"/>
            <location file="src/ServerPool.h" line="97" column="10" info="Parent function &apos;ServerPool::refreshRequest&apos;"/>
            <symbol>SentinelServerPool</symbol>
            <symbol>refreshRequest</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;SentinelServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;SentinelServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/Handler.cpp">
            <location file="src/SentinelServerPool.h" line="28" column="10" info="Derived function &apos;SentinelServerPool::handleResponse&apos;"/>
            <location file="src/ServerPool.h" line="101" column="10" info="Parent function &apos;ServerPool::handleResponse&apos;"/>
            <symbol>SentinelServerPool</symbol>
            <symbol>handleResponse</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SentinelServerPool&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SentinelServerPool&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Handler.cpp">
            <location file="src/SentinelServerPool.h" line="19" column="5"/>
            <symbol>SentinelServerPool</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SegmentStr &lt; Const :: MaxKeyLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SegmentStr &lt; Const :: MaxKeyLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Handler.cpp">
            <location file="src/Buffer.h" line="211" column="5"/>
            <symbol>SegmentStr &lt; Const :: MaxKeyLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SegmentStr &lt; 64 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SegmentStr &lt; 64 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Handler.cpp">
            <location file="src/Buffer.h" line="211" column="5"/>
            <symbol>SegmentStr &lt; 64 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SegmentStr &lt; 128 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SegmentStr &lt; 128 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Handler.cpp">
            <location file="src/Buffer.h" line="211" column="5"/>
            <symbol>SegmentStr &lt; 128 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SegmentStr &lt; 256 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SegmentStr &lt; 256 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Handler.cpp">
            <location file="src/Buffer.h" line="211" column="5"/>
            <symbol>SegmentStr &lt; 256 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; Const :: MaxKeyLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; Const :: MaxKeyLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Handler.cpp">
            <location file="src/PString.h" line="171" column="5"/>
            <symbol>SString &lt; Const :: MaxKeyLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; Const :: MaxKeyLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; Const :: MaxKeyLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Handler.cpp">
            <location file="src/PString.h" line="181" column="5"/>
            <symbol>SString &lt; Const :: MaxKeyLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; Const :: MaxKeyLen &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; Const :: MaxKeyLen &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Handler.cpp">
            <location file="src/PString.h" line="190" column="5"/>
            <symbol>SString &lt; Const :: MaxKeyLen &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 32 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 32 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Handler.cpp">
            <location file="src/PString.h" line="171" column="5"/>
            <symbol>SString &lt; 32 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 32 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 32 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Handler.cpp">
            <location file="src/PString.h" line="181" column="5"/>
            <symbol>SString &lt; 32 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 32 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 32 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Handler.cpp">
            <location file="src/PString.h" line="190" column="5"/>
            <symbol>SString &lt; 32 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 512 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 512 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Handler.cpp">
            <location file="src/PString.h" line="171" column="5"/>
            <symbol>SString &lt; 512 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 512 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 512 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Handler.cpp">
            <location file="src/PString.h" line="181" column="5"/>
            <symbol>SString &lt; 512 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 512 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 512 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Handler.cpp">
            <location file="src/PString.h" line="190" column="5"/>
            <symbol>SString &lt; 512 &gt;</symbol>
        </error>
        <error id="missingOverride" severity="style" msg="The destructor &apos;~SentinelServerPool&apos; overrides a destructor in a base class but is not marked with a &apos;override&apos; specifier." verbose="The destructor &apos;~SentinelServerPool&apos; overrides a destructor in a base class but is not marked with a &apos;override&apos; specifier." file0="src/Handler.cpp">
            <location file="src/SentinelServerPool.h" line="20" column="6" info="Destructor in derived class"/>
            <location file="src/ServerPool.h" line="28" column="14" info="Virtual destructor in base class"/>
            <symbol>~SentinelServerPool</symbol>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="295" column="32"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="298" column="37"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="1269" column="22"/>
        </error>
        <error id="duplicateBreak" severity="style" msg="Consecutive return, break, continue, goto or throw statements are unnecessary." verbose="Consecutive return, break, continue, goto or throw statements are unnecessary. The second statement can never be executed, and so should be removed." cwe="561" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="675" column="9"/>
        </error>
        <error id="variableScope" severity="style" msg="The scope of the variable &apos;slot&apos; can be reduced." verbose="The scope of the variable &apos;slot&apos; can be reduced. Warning: Be careful when fixing this message, especially when there are inner loops. Here is an example where cppcheck will write that the scope for &apos;i&apos; can be reduced:\012void f(int x)\012{\012    int i = 0;\012    if (x) {\012        // it&apos;s safe to move &apos;int i = 0;&apos; here\012        for (int n = 0; n &lt; 10; ++n) {\012            // it is possible but not safe to move &apos;int i = 0;&apos; here\012            do_something(&amp;i);\012        }\012    }\012}\012When you see this message it is always safe to reduce the variable scope 1 level." cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="1432" column="9"/>
            <symbol>slot</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;conf&apos; can be declared as pointer to const" verbose="Variable &apos;conf&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="39" column="11" info="Variable &apos;conf&apos; can be declared as pointer to const"/>
            <symbol>conf</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;conf&apos; can be declared as pointer to const" verbose="Variable &apos;conf&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="51" column="10" info="Variable &apos;conf&apos; can be declared as pointer to const"/>
            <symbol>conf</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;auth&apos; can be declared as pointer to const" verbose="Variable &apos;auth&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="336" column="14" info="Variable &apos;auth&apos; can be declared as pointer to const"/>
            <symbol>auth</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;req&apos; can be declared as pointer to const" verbose="Parameter &apos;req&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="439" column="59" info="Parameter &apos;req&apos; can be declared as pointer to const"/>
            <symbol>req</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;c&apos; can be declared as pointer to const" verbose="Variable &apos;c&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="460" column="10" info="Variable &apos;c&apos; can be declared as pointer to const"/>
            <symbol>c</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;sp&apos; can be declared as pointer to const" verbose="Variable &apos;sp&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="520" column="10" info="Variable &apos;sp&apos; can be declared as pointer to const"/>
            <symbol>sp</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;sp&apos; can be declared as pointer to const" verbose="Variable &apos;sp&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="611" column="18" info="Variable &apos;sp&apos; can be declared as pointer to const"/>
            <symbol>sp</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;g&apos; can be declared as pointer to const" verbose="Variable &apos;g&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="652" column="18" info="Variable &apos;g&apos; can be declared as pointer to const"/>
            <symbol>g</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;req&apos; can be declared as pointer to const" verbose="Parameter &apos;req&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="682" column="42" info="Parameter &apos;req&apos; can be declared as pointer to const"/>
            <symbol>req</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;g&apos; can be declared as pointer to const" verbose="Variable &apos;g&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="816" column="18" info="Variable &apos;g&apos; can be declared as pointer to const"/>
            <symbol>g</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;h&apos; can be declared as pointer to const" verbose="Variable &apos;h&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="975" column="19" info="Variable &apos;h&apos; can be declared as pointer to const"/>
            <symbol>h</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;sp&apos; can be declared as pointer to const" verbose="Variable &apos;sp&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="994" column="14" info="Variable &apos;sp&apos; can be declared as pointer to const"/>
            <symbol>sp</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;serv&apos; can be declared as pointer to const" verbose="Variable &apos;serv&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="995" column="24" info="Variable &apos;serv&apos; can be declared as pointer to const"/>
            <symbol>serv</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;h&apos; can be declared as pointer to const" verbose="Variable &apos;h&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="997" column="23" info="Variable &apos;h&apos; can be declared as pointer to const"/>
            <symbol>h</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;g&apos; can be declared as pointer to const" verbose="Variable &apos;g&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="1004" column="18" info="Variable &apos;g&apos; can be declared as pointer to const"/>
            <symbol>g</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;serv&apos; can be declared as pointer to const" verbose="Variable &apos;serv&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="1079" column="20" info="Variable &apos;serv&apos; can be declared as pointer to const"/>
            <symbol>serv</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;h&apos; can be declared as pointer to const" verbose="Variable &apos;h&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="1082" column="19" info="Variable &apos;h&apos; can be declared as pointer to const"/>
            <symbol>h</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;h&apos; can be declared as pointer to const" verbose="Variable &apos;h&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="1141" column="19" info="Variable &apos;h&apos; can be declared as pointer to const"/>
            <symbol>h</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;h&apos; can be declared as pointer to const" verbose="Variable &apos;h&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="1154" column="23" info="Variable &apos;h&apos; can be declared as pointer to const"/>
            <symbol>h</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;c&apos; can be declared as pointer to const" verbose="Parameter &apos;c&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="1426" column="43" info="Parameter &apos;c&apos; can be declared as pointer to const"/>
            <symbol>c</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;res&apos; can be declared as pointer to const" verbose="Parameter &apos;res&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="1426" column="70" info="Parameter &apos;res&apos; can be declared as pointer to const"/>
            <symbol>res</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;p&apos; can be declared as pointer to const" verbose="Variable &apos;p&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="1443" column="10" info="Variable &apos;p&apos; can be declared as pointer to const"/>
            <symbol>p</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;auth&apos; can be declared as pointer to const" verbose="Variable &apos;auth&apos; can be declared as pointer to const" cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="1481" column="25" info="Variable &apos;auth&apos; can be declared as pointer to const"/>
            <symbol>auth</symbol>
        </error>
        <error id="useStlAlgorithm" severity="style" msg="Consider using std::accumulate algorithm instead of a raw loop." verbose="Consider using std::accumulate algorithm instead of a raw loop." cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="999" column="24"/>
        </error>
        <error id="useStlAlgorithm" severity="style" msg="Consider using std::accumulate algorithm instead of a raw loop." verbose="Consider using std::accumulate algorithm instead of a raw loop." cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="1084" column="20"/>
        </error>
        <error id="useStlAlgorithm" severity="style" msg="Consider using std::accumulate algorithm instead of a raw loop." verbose="Consider using std::accumulate algorithm instead of a raw loop." cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="1143" column="20"/>
        </error>
        <error id="useStlAlgorithm" severity="style" msg="Consider using std::accumulate algorithm instead of a raw loop." verbose="Consider using std::accumulate algorithm instead of a raw loop." cwe="398" file0="src/Handler.cpp">
            <location file="src/Handler.cpp" line="1156" column="24"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/HashFunc.cpp" line="7" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/HashFunc.cpp" line="8" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/HashFunc.cpp">
            <location file="src/HashFunc.cpp" line="0" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/KqueueMultiplexor.cpp">
            <location file="src/KqueueMultiplexor.cpp" line="0" column="0"/>
        </error>
        <error id="shadowVariable" severity="style" msg="Local variable &apos;i&apos; shadows outer variable" verbose="Local variable &apos;i&apos; shadows outer variable" cwe="398" file0="src/LatencyMonitor.cpp">
            <location file="src/LatencyMonitor.cpp" line="55" column="18" info="Shadow variable"/>
            <location file="src/LatencyMonitor.cpp" line="41" column="9" info="Shadowed declaration"/>
            <symbol>i</symbol>
        </error>
        <error id="constVariableReference" severity="style" msg="Variable &apos;s&apos; can be declared as reference to const" verbose="Variable &apos;s&apos; can be declared as reference to const" cwe="398" file0="src/LatencyMonitor.cpp">
            <location file="src/LatencyMonitor.cpp" line="12" column="16" info="Variable &apos;s&apos; can be declared as reference to const"/>
            <symbol>s</symbol>
        </error>
        <error id="useStlAlgorithm" severity="style" msg="Consider using std::accumulate algorithm instead of a raw loop." verbose="Consider using std::accumulate algorithm instead of a raw loop." cwe="398" file0="src/LatencyMonitor.cpp">
            <location file="src/LatencyMonitor.cpp" line="13" column="12"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/LatencyMonitor.cpp">
            <location file="src/LatencyMonitor.cpp" line="0" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ListenSocket.cpp" line="7" column="0"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/ListenSocket.cpp">
            <location file="src/ListenSocket.cpp" line="17" column="40"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/ListenSocket.cpp">
            <location file="src/ListenSocket.cpp" line="18" column="20"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/LogFileSink.cpp" line="7" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/LogFileSink.cpp" line="8" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;strings.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/LogFileSink.cpp" line="9" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;time.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;time.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/LogFileSink.cpp" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sstream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sstream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/LogFileSink.cpp" line="11" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/LogFileSink.cpp">
            <location file="src/LogFileSink.cpp" line="0" column="0"/>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;LogFileSink::mFilePath&apos; is not initialized in the constructor." verbose="Member variable &apos;LogFileSink::mFilePath&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/LogFileSink.cpp">
            <location file="src/LogFileSink.cpp" line="18" column="14"/>
            <symbol>LogFileSink::mFilePath</symbol>
        </error>
        <error id="truncLongCastAssignment" severity="style" msg="int result is assigned to long &amp; variable. If the variable is long &amp; to avoid loss of information, then you have loss of information." verbose="int result is assigned to long &amp; variable. If the variable is long &amp; to avoid loss of information, then there is loss of information. To avoid loss of information you must cast a calculation operand to long &amp;, for example &apos;l = a * b;&apos; =&gt; &apos;l = (long &amp;)a * b;&apos;." cwe="197" file0="src/LogFileSink.cpp">
            <location file="src/LogFileSink.cpp" line="70" column="19"/>
        </error>
        <error id="truncLongCastAssignment" severity="style" msg="int result is assigned to long &amp; variable. If the variable is long &amp; to avoid loss of information, then you have loss of information." verbose="int result is assigned to long &amp; variable. If the variable is long &amp; to avoid loss of information, then there is loss of information. To avoid loss of information you must cast a calculation operand to long &amp;, for example &apos;l = a * b;&apos; =&gt; &apos;l = (long &amp;)a * b;&apos;." cwe="197" file0="src/LogFileSink.cpp">
            <location file="src/LogFileSink.cpp" line="72" column="19"/>
        </error>
        <error id="unreadVariable" severity="style" msg="Variable &apos;rotate&apos; is assigned a value that is never used." verbose="Variable &apos;rotate&apos; is assigned a value that is never used." cwe="563" file0="src/LogFileSink.cpp">
            <location file="src/LogFileSink.cpp" line="176" column="20"/>
            <symbol>rotate</symbol>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;time.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;time.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Logger.cpp" line="7" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Logger.cpp" line="8" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Logger.cpp" line="9" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;errno.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;errno.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Logger.cpp" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;chrono&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;chrono&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Logger.cpp" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;stdio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;stdio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Logger.cpp" line="15" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/Logger.cpp">
            <location file="src/Logger.cpp" line="0" column="0"/>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;LogUnit::mBuf&apos; is not initialized in the constructor." verbose="Member variable &apos;LogUnit::mBuf&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/Logger.cpp">
            <location file="src/Logger.cpp" line="26" column="10"/>
            <symbol>LogUnit::mBuf</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;Logger::mThread&apos; is not initialized in the constructor." verbose="Member variable &apos;Logger::mThread&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/Logger.cpp">
            <location file="src/Logger.cpp" line="72" column="9"/>
            <symbol>Logger::mThread</symbol>
        </error>
        <error id="knownConditionTrueFalse" severity="style" msg="Condition &apos;!mStop&apos; is always true" verbose="Condition &apos;!mStop&apos; is always true" cwe="571" file0="src/Logger.cpp">
            <location file="src/Logger.cpp" line="127" column="37" info="Condition &apos;!mStop&apos; is always true"/>
            <location file="src/Logger.cpp" line="123" column="12" info="Assuming that condition &apos;!mStop&apos; is not redundant"/>
        </error>
        <error id="shadowFunction" severity="style" msg="Local variable &apos;log&apos; shadows outer function" verbose="Local variable &apos;log&apos; shadows outer function" cwe="398" file0="src/Logger.cpp">
            <location file="src/Logger.cpp" line="136" column="23" info="Shadow variable"/>
            <location file="src/Logger.h" line="79" column="14" info="Shadowed declaration"/>
            <symbol>log</symbol>
        </error>
        <error id="shadowFunction" severity="style" msg="Local variable &apos;log&apos; shadows outer function" verbose="Local variable &apos;log&apos; shadows outer function" cwe="398" file0="src/Logger.cpp">
            <location file="src/Logger.cpp" line="144" column="23" info="Shadow variable"/>
            <location file="src/Logger.h" line="79" column="14" info="Shadowed declaration"/>
            <symbol>log</symbol>
        </error>
        <error id="shadowFunction" severity="style" msg="Local variable &apos;log&apos; shadows outer function" verbose="Local variable &apos;log&apos; shadows outer function" cwe="398" file0="src/Logger.cpp">
            <location file="src/Logger.cpp" line="151" column="21" info="Shadow variable"/>
            <location file="src/Logger.h" line="79" column="14" info="Shadowed declaration"/>
            <symbol>log</symbol>
        </error>
        <error id="shadowFunction" severity="style" msg="Local variable &apos;log&apos; shadows outer function" verbose="Local variable &apos;log&apos; shadows outer function" cwe="398" file0="src/Logger.cpp">
            <location file="src/Logger.cpp" line="173" column="14" info="Shadow variable"/>
            <location file="src/Logger.h" line="79" column="14" info="Shadowed declaration"/>
            <symbol>log</symbol>
        </error>
        <error id="useStlAlgorithm" severity="style" msg="Consider using std::copy algorithm instead of a raw loop." verbose="Consider using std::copy algorithm instead of a raw loop." cwe="398" file0="src/Logger.cpp">
            <location file="src/Logger.cpp" line="145" column="23"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/PollMultiplexor.cpp">
            <location file="src/PollMultiplexor.cpp" line="0" column="0"/>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;s&apos; can be declared as pointer to const" verbose="Parameter &apos;s&apos; can be declared as pointer to const" cwe="398" file0="src/PollMultiplexor.cpp">
            <location file="src/PollMultiplexor.cpp" line="36" column="41" info="Parameter &apos;s&apos; can be declared as pointer to const"/>
            <symbol>s</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;s&apos; can be declared as pointer to const" verbose="Parameter &apos;s&apos; can be declared as pointer to const" cwe="398" file0="src/PollMultiplexor.cpp">
            <location file="src/PollMultiplexor.cpp" line="52" column="40" info="Parameter &apos;s&apos; can be declared as pointer to const"/>
            <symbol>s</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;s&apos; can be declared as pointer to const" verbose="Parameter &apos;s&apos; can be declared as pointer to const" cwe="398" file0="src/PollMultiplexor.cpp">
            <location file="src/PollMultiplexor.cpp" line="61" column="40" info="Parameter &apos;s&apos; can be declared as pointer to const"/>
            <symbol>s</symbol>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;unistd.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Proxy.cpp" line="7" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;signal.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;signal.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Proxy.cpp" line="8" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;time.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;time.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Proxy.cpp" line="9" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;stdlib.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;stdlib.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Proxy.cpp" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/types.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/types.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Proxy.cpp" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;iostream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;iostream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Proxy.cpp" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;thread&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;thread&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Proxy.cpp" line="13" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/Proxy.cpp">
            <location file="src/Proxy.cpp" line="0" column="0"/>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;Proxy::mConf&apos; is not initialized in the constructor." verbose="Member variable &apos;Proxy::mConf&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/Proxy.cpp">
            <location file="src/Proxy.cpp" line="46" column="8"/>
            <symbol>Proxy::mConf</symbol>
        </error>
        <error id="constVariableReference" severity="style" msg="Variable &apos;ac&apos; can be declared as reference to const" verbose="Variable &apos;ac&apos; can be declared as reference to const" cwe="398" file0="src/Proxy.cpp">
            <location file="src/Proxy.cpp" line="92" column="16" info="Variable &apos;ac&apos; can be declared as reference to const"/>
            <symbol>ac</symbol>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;execinfo.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;execinfo.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Backtrace.h" line="14" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/Request.cpp">
            <location file="src/Request.cpp" line="0" column="0"/>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;r&apos; can be declared as pointer to const" verbose="Variable &apos;r&apos; can be declared as pointer to const" cwe="398" file0="src/Request.cpp">
            <location file="src/Request.cpp" line="94" column="10" info="Variable &apos;r&apos; can be declared as pointer to const"/>
            <symbol>r</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;leaderRes&apos; can be declared as pointer to const" verbose="Variable &apos;leaderRes&apos; can be declared as pointer to const" cwe="398" file0="src/Request.cpp">
            <location file="src/Request.cpp" line="350" column="27" info="Variable &apos;leaderRes&apos; can be declared as pointer to const"/>
            <symbol>leaderRes</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;leaderRes&apos; can be declared as pointer to const" verbose="Variable &apos;leaderRes&apos; can be declared as pointer to const" cwe="398" file0="src/Request.cpp">
            <location file="src/Request.cpp" line="359" column="27" info="Variable &apos;leaderRes&apos; can be declared as pointer to const"/>
            <symbol>leaderRes</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;leaderRes&apos; can be declared as pointer to const" verbose="Variable &apos;leaderRes&apos; can be declared as pointer to const" cwe="398" file0="src/Request.cpp">
            <location file="src/Request.cpp" line="380" column="27" info="Variable &apos;leaderRes&apos; can be declared as pointer to const"/>
            <symbol>leaderRes</symbol>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/RequestParser.cpp">
            <location file="src/RequestParser.cpp" line="0" column="0"/>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;RequestParser::mCmd&apos; is not initialized in the constructor." verbose="Member variable &apos;RequestParser::mCmd&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/RequestParser.cpp">
            <location file="src/RequestParser.cpp" line="9" column="16"/>
            <symbol>RequestParser::mCmd</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;SString &lt; 64 &gt;::mBuf&apos; is not initialized in the copy constructor." verbose="Member variable &apos;SString &lt; 64 &gt;::mBuf&apos; is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/RequestParser.cpp">
            <location file="src/PString.h" line="185" column="5"/>
            <symbol>SString &lt; 64 &gt;::mBuf</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;SString &lt; 16 &gt;::mBuf&apos; is not initialized in the copy constructor." verbose="Member variable &apos;SString &lt; 16 &gt;::mBuf&apos; is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/RequestParser.cpp">
            <location file="src/PString.h" line="185" column="5"/>
            <symbol>SString &lt; 16 &gt;::mBuf</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 64 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 64 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/RequestParser.cpp">
            <location file="src/PString.h" line="171" column="5"/>
            <symbol>SString &lt; 64 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 64 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 64 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/RequestParser.cpp">
            <location file="src/PString.h" line="181" column="5"/>
            <symbol>SString &lt; 64 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 64 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 64 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/RequestParser.cpp">
            <location file="src/PString.h" line="190" column="5"/>
            <symbol>SString &lt; 64 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 16 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 16 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/RequestParser.cpp">
            <location file="src/PString.h" line="171" column="5"/>
            <symbol>SString &lt; 16 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 16 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 16 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/RequestParser.cpp">
            <location file="src/PString.h" line="181" column="5"/>
            <symbol>SString &lt; 16 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 16 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 16 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/RequestParser.cpp">
            <location file="src/PString.h" line="190" column="5"/>
            <symbol>SString &lt; 16 &gt;</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;cursor&apos; can be declared as pointer to const" verbose="Variable &apos;cursor&apos; can be declared as pointer to const" cwe="398" file0="src/RequestParser.cpp">
            <location file="src/RequestParser.cpp" line="74" column="11" info="Variable &apos;cursor&apos; can be declared as pointer to const"/>
            <symbol>cursor</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;end&apos; can be declared as pointer to const" verbose="Variable &apos;end&apos; can be declared as pointer to const" cwe="398" file0="src/RequestParser.cpp">
            <location file="src/RequestParser.cpp" line="75" column="11" info="Variable &apos;end&apos; can be declared as pointer to const"/>
            <symbol>end</symbol>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/Response.cpp">
            <location file="src/Response.cpp" line="0" column="0"/>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SegmentStr &lt; Const :: MaxAddrLen + 16 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SegmentStr &lt; Const :: MaxAddrLen + 16 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/Response.cpp">
            <location file="src/Buffer.h" line="211" column="5"/>
            <symbol>SegmentStr &lt; Const :: MaxAddrLen + 16 &gt;</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;r&apos; can be declared as pointer to const" verbose="Variable &apos;r&apos; can be declared as pointer to const" cwe="398" file0="src/Response.cpp">
            <location file="src/Response.cpp" line="64" column="10" info="Variable &apos;r&apos; can be declared as pointer to const"/>
            <symbol>r</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;req&apos; can be declared as pointer to const" verbose="Parameter &apos;req&apos; can be declared as pointer to const" cwe="398" file0="src/Response.cpp">
            <location file="src/Response.cpp" line="107" column="41" info="Parameter &apos;req&apos; can be declared as pointer to const"/>
            <symbol>req</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;leader&apos; can be declared as pointer to const" verbose="Variable &apos;leader&apos; can be declared as pointer to const" cwe="398" file0="src/Response.cpp">
            <location file="src/Response.cpp" line="109" column="18" info="Variable &apos;leader&apos; can be declared as pointer to const"/>
            <symbol>leader</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;ResponseParser::mArrayNum&apos; is not initialized in the constructor." verbose="Member variable &apos;ResponseParser::mArrayNum&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/ResponseParser.cpp">
            <location file="src/ResponseParser.cpp" line="10" column="17"/>
            <symbol>ResponseParser::mArrayNum</symbol>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;ResponseParser::mElementCnt&apos; is not initialized in the constructor." verbose="Member variable &apos;ResponseParser::mElementCnt&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/ResponseParser.cpp">
            <location file="src/ResponseParser.cpp" line="10" column="17"/>
            <symbol>ResponseParser::mElementCnt</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;end&apos; can be declared as pointer to const" verbose="Variable &apos;end&apos; can be declared as pointer to const" cwe="398" file0="src/ResponseParser.cpp">
            <location file="src/ResponseParser.cpp" line="35" column="11" info="Variable &apos;end&apos; can be declared as pointer to const"/>
            <symbol>end</symbol>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/ResponseParser.cpp">
            <location file="src/ResponseParser.cpp" line="0" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;algorithm&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;algorithm&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/SentinelServerPool.cpp" line="7" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/SentinelServerPool.cpp">
            <location file="src/SentinelServerPool.cpp" line="0" column="0"/>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;SString &lt; 4 &gt;::mBuf&apos; is not initialized in the copy constructor." verbose="Member variable &apos;SString &lt; 4 &gt;::mBuf&apos; is not initialized in the copy constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/SentinelServerPool.cpp">
            <location file="src/PString.h" line="185" column="5"/>
            <symbol>SString &lt; 4 &gt;::mBuf</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;SentinelServerPool&apos; defines member function with name &apos;getServer&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;SentinelServerPool&apos; defines member function with name &apos;getServer&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/SentinelServerPool.cpp">
            <location file="src/SentinelServerPool.cpp" line="58" column="29" info="Derived function &apos;SentinelServerPool::getServer&apos;"/>
            <location file="src/ServerPool.h" line="89" column="13" info="Parent function &apos;ServerPool::getServer&apos;"/>
            <symbol>SentinelServerPool</symbol>
            <symbol>getServer</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;SentinelServerPool&apos; defines member function with name &apos;refreshRequest&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;SentinelServerPool&apos; defines member function with name &apos;refreshRequest&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/SentinelServerPool.cpp">
            <location file="src/SentinelServerPool.cpp" line="103" column="26" info="Derived function &apos;SentinelServerPool::refreshRequest&apos;"/>
            <location file="src/ServerPool.h" line="97" column="10" info="Parent function &apos;ServerPool::refreshRequest&apos;"/>
            <symbol>SentinelServerPool</symbol>
            <symbol>refreshRequest</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;SentinelServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;SentinelServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/SentinelServerPool.cpp">
            <location file="src/SentinelServerPool.cpp" line="122" column="26" info="Derived function &apos;SentinelServerPool::handleResponse&apos;"/>
            <location file="src/ServerPool.h" line="101" column="10" info="Parent function &apos;ServerPool::handleResponse&apos;"/>
            <symbol>SentinelServerPool</symbol>
            <symbol>handleResponse</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;AddrParser&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;AddrParser&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/SentinelServerPool.cpp">
            <location file="src/SentinelServerPool.cpp" line="148" column="5"/>
            <symbol>AddrParser</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SegmentStr &lt; Const :: MaxAddrLen + 32 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SegmentStr &lt; Const :: MaxAddrLen + 32 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/SentinelServerPool.cpp">
            <location file="src/Buffer.h" line="211" column="5"/>
            <symbol>SegmentStr &lt; Const :: MaxAddrLen + 32 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 4 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 4 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/SentinelServerPool.cpp">
            <location file="src/PString.h" line="171" column="5"/>
            <symbol>SString &lt; 4 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 4 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 4 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/SentinelServerPool.cpp">
            <location file="src/PString.h" line="181" column="5"/>
            <symbol>SString &lt; 4 &gt;</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;SString &lt; 4 &gt;&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;SString &lt; 4 &gt;&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/SentinelServerPool.cpp">
            <location file="src/PString.h" line="190" column="5"/>
            <symbol>SString &lt; 4 &gt;</symbol>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/SentinelServerPool.cpp">
            <location file="src/SentinelServerPool.cpp" line="337" column="22"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/SentinelServerPool.cpp">
            <location file="src/SentinelServerPool.cpp" line="411" column="22"/>
        </error>
        <error id="duplicateBreak" severity="style" msg="Consecutive return, break, continue, goto or throw statements are unnecessary." verbose="Consecutive return, break, continue, goto or throw statements are unnecessary. The second statement can never be executed, and so should be removed." cwe="561" file0="src/SentinelServerPool.cpp">
            <location file="src/SentinelServerPool.cpp" line="89" column="13"/>
        </error>
        <error id="duplicateBreak" severity="style" msg="Consecutive return, break, continue, goto or throw statements are unnecessary." verbose="Consecutive return, break, continue, goto or throw statements are unnecessary. The second statement can never be executed, and so should be removed." cwe="561" file0="src/SentinelServerPool.cpp">
            <location file="src/SentinelServerPool.cpp" line="95" column="13"/>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;h&apos; can be declared as pointer to const" verbose="Parameter &apos;h&apos; can be declared as pointer to const" cwe="398" file0="src/SentinelServerPool.cpp">
            <location file="src/SentinelServerPool.cpp" line="290" column="51" info="Parameter &apos;h&apos; can be declared as pointer to const"/>
            <symbol>h</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;req&apos; can be declared as pointer to const" verbose="Parameter &apos;req&apos; can be declared as pointer to const" cwe="398" file0="src/SentinelServerPool.cpp">
            <location file="src/SentinelServerPool.cpp" line="332" column="85" info="Parameter &apos;req&apos; can be declared as pointer to const"/>
            <symbol>req</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;req&apos; can be declared as pointer to const" verbose="Parameter &apos;req&apos; can be declared as pointer to const" cwe="398" file0="src/SentinelServerPool.cpp">
            <location file="src/SentinelServerPool.cpp" line="406" column="82" info="Parameter &apos;req&apos; can be declared as pointer to const"/>
            <symbol>req</symbol>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/Server.cpp">
            <location file="src/Server.cpp" line="0" column="0"/>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;dataCenter&apos; can be declared as pointer to const" verbose="Variable &apos;dataCenter&apos; can be declared as pointer to const" cwe="398" file0="src/Server.cpp">
            <location file="src/Server.cpp" line="31" column="14" info="Variable &apos;dataCenter&apos; can be declared as pointer to const"/>
            <symbol>dataCenter</symbol>
        </error>
        <error id="knownConditionTrueFalse" severity="style" msg="The comparison &apos;mNextActivateTime == v&apos; is always true because &apos;mNextActivateTime&apos; and &apos;v&apos; represent the same value." verbose="Finding the same expression on both sides of an operator is suspicious and might indicate a cut and paste or logic error. Please examine this code carefully to determine if it is correct." cwe="398" file0="src/Server.cpp">
            <location file="src/Server.cpp" line="47" column="12"/>
            <location file="src/Server.cpp" line="42" column="14" info="&apos;v&apos; is assigned value &apos;mNextActivateTime&apos; here."/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;algorithm&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;algorithm&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/ServerGroup.cpp" line="7" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/ServerGroup.cpp">
            <location file="src/ServerGroup.cpp" line="0" column="0"/>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;req&apos; can be declared as pointer to const" verbose="Parameter &apos;req&apos; can be declared as pointer to const" cwe="398" file0="src/ServerGroup.cpp">
            <location file="src/ServerGroup.cpp" line="46" column="53" info="Parameter &apos;req&apos; can be declared as pointer to const"/>
            <symbol>req</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;dataCenter&apos; can be declared as pointer to const" verbose="Variable &apos;dataCenter&apos; can be declared as pointer to const" cwe="398" file0="src/ServerGroup.cpp">
            <location file="src/ServerGroup.cpp" line="62" column="21" info="Variable &apos;dataCenter&apos; can be declared as pointer to const"/>
            <symbol>dataCenter</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;localDC&apos; can be declared as pointer to const" verbose="Parameter &apos;localDC&apos; can be declared as pointer to const" cwe="398" file0="src/ServerGroup.cpp">
            <location file="src/ServerGroup.cpp" line="136" column="52" info="Parameter &apos;localDC&apos; can be declared as pointer to const"/>
            <symbol>localDC</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;dc&apos; can be declared as pointer to const" verbose="Variable &apos;dc&apos; can be declared as pointer to const" cwe="398" file0="src/ServerGroup.cpp">
            <location file="src/ServerGroup.cpp" line="203" column="9" info="Variable &apos;dc&apos; can be declared as pointer to const"/>
            <symbol>dc</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;s&apos; can be declared as pointer to const" verbose="Parameter &apos;s&apos; can be declared as pointer to const" cwe="398" file0="src/ServerGroup.cpp">
            <location file="src/ServerGroup.cpp" line="298" column="34" info="Parameter &apos;s&apos; can be declared as pointer to const"/>
            <symbol>s</symbol>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/ServerPool.cpp">
            <location file="src/ServerPool.cpp" line="0" column="0"/>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;ClusterServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;ClusterServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/ServerPool.cpp">
            <location file="src/ClusterServerPool.h" line="31" column="10" info="Derived function &apos;ClusterServerPool::handleResponse&apos;"/>
            <location file="src/ServerPool.cpp" line="39" column="18" info="Parent function &apos;ServerPool::handleResponse&apos;"/>
            <symbol>ClusterServerPool</symbol>
            <symbol>handleResponse</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/ServerPool.cpp">
            <location file="src/StandaloneServerPool.h" line="28" column="10" info="Derived function &apos;StandaloneServerPool::handleResponse&apos;"/>
            <location file="src/ServerPool.cpp" line="39" column="18" info="Parent function &apos;ServerPool::handleResponse&apos;"/>
            <symbol>StandaloneServerPool</symbol>
            <symbol>handleResponse</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="knownConditionTrueFalse" severity="style" msg="The comparison &apos;mLastRefreshTime == last&apos; is always true because &apos;mLastRefreshTime&apos; and &apos;last&apos; represent the same value." verbose="Finding the same expression on both sides of an operator is suspicious and might indicate a cut and paste or logic error. Please examine this code carefully to determine if it is correct." cwe="398" file0="src/ServerPool.cpp">
            <location file="src/ServerPool.cpp" line="36" column="12"/>
            <location file="src/ServerPool.cpp" line="31" column="17" info="&apos;last&apos; is assigned value &apos;mLastRefreshTime&apos; here."/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/types.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/types.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.cpp" line="7" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/socket.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/socket.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.cpp" line="8" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/un.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/un.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.cpp" line="9" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;sys/uio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;sys/uio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.cpp" line="10" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;netinet/in.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;netinet/in.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.cpp" line="11" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;netinet/ip.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;netinet/ip.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.cpp" line="12" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;netinet/tcp.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;netinet/tcp.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.cpp" line="13" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;fcntl.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;fcntl.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.cpp" line="14" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;PString.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.cpp" line="15" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;string&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Socket.cpp" line="16" column="0"/>
        </error>
        <error id="arrayIndexOutOfBoundsCond" severity="warning" msg="Either the condition &apos;mStatus&lt;CustomStatus&apos; is redundant or the array &apos;strs[6]&apos; is accessed at index 99, which is out of bounds." verbose="Either the condition &apos;mStatus&lt;CustomStatus&apos; is redundant or the array &apos;strs[6]&apos; is accessed at index 99, which is out of bounds." cwe="788" file0="src/Socket.cpp">
            <location file="src/Socket.cpp" line="77" column="41" info="Array index out of bounds"/>
            <location file="src/Socket.cpp" line="77" column="20" info="Assuming that condition &apos;mStatus&lt;CustomStatus&apos; is not redundant"/>
        </error>
        <error id="uninitMemberVar" severity="warning" msg="Member variable &apos;Socket::mEvts&apos; is not initialized in the constructor." verbose="Member variable &apos;Socket::mEvts&apos; is not initialized in the constructor. Member variables of native types, pointers, or references are left uninitialized when the class is instantiated. That may cause bugs or undefined behavior." cwe="398" file0="src/Socket.cpp">
            <location file="src/Socket.cpp" line="30" column="9"/>
            <symbol>Socket::mEvts</symbol>
        </error>
        <error id="shadowFunction" severity="style" msg="Local variable &apos;fd&apos; shadows outer function" verbose="Local variable &apos;fd&apos; shadows outer function" cwe="398" file0="src/Socket.cpp">
            <location file="src/Socket.cpp" line="82" column="9" info="Shadow variable"/>
            <location file="src/Socket.h" line="70" column="9" info="Shadowed declaration"/>
            <symbol>fd</symbol>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;algorithm&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;algorithm&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/StandaloneServerPool.cpp" line="7" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="0" column="0"/>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;getServer&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;getServer&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="72" column="31" info="Derived function &apos;StandaloneServerPool::getServer&apos;"/>
            <location file="src/ServerPool.h" line="89" column="13" info="Parent function &apos;ServerPool::getServer&apos;"/>
            <symbol>StandaloneServerPool</symbol>
            <symbol>getServer</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;refreshRequest&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;refreshRequest&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="117" column="28" info="Derived function &apos;StandaloneServerPool::refreshRequest&apos;"/>
            <location file="src/ServerPool.h" line="97" column="10" info="Parent function &apos;ServerPool::refreshRequest&apos;"/>
            <symbol>StandaloneServerPool</symbol>
            <symbol>refreshRequest</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="duplInheritedMember" severity="warning" msg="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." verbose="The class &apos;StandaloneServerPool&apos; defines member function with name &apos;handleResponse&apos; also defined in its parent class &apos;ServerPool&apos;." cwe="398" file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="142" column="28" info="Derived function &apos;StandaloneServerPool::handleResponse&apos;"/>
            <location file="src/ServerPool.h" line="101" column="10" info="Parent function &apos;ServerPool::handleResponse&apos;"/>
            <symbol>StandaloneServerPool</symbol>
            <symbol>handleResponse</symbol>
            <symbol>ServerPool</symbol>
        </error>
        <error id="noExplicitConstructor" severity="style" msg="Class &apos;AddrParser&apos; has a constructor with 1 argument that is not explicit." verbose="Class &apos;AddrParser&apos; has a constructor with 1 argument that is not explicit. Such, so called &quot;Converting constructors&quot;, should in general be explicit for type safety reasons as that prevents unintended implicit conversions." cwe="398" file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="168" column="5"/>
            <symbol>AddrParser</symbol>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="357" column="22"/>
        </error>
        <error id="cstyleCast" severity="style" msg="C-style pointer casting" verbose="C-style pointer casting detected. C++ offers four different kinds of casts as replacements: static_cast, const_cast, dynamic_cast and reinterpret_cast. A C-style cast could evaluate to any of those automatically, thus it is considered safer if the programmer explicitly states which kind of cast is expected." cwe="398" file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="431" column="22"/>
        </error>
        <error id="duplicateBreak" severity="style" msg="Consecutive return, break, continue, goto or throw statements are unnecessary." verbose="Consecutive return, break, continue, goto or throw statements are unnecessary. The second statement can never be executed, and so should be removed." cwe="561" file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="103" column="13"/>
        </error>
        <error id="duplicateBreak" severity="style" msg="Consecutive return, break, continue, goto or throw statements are unnecessary." verbose="Consecutive return, break, continue, goto or throw statements are unnecessary. The second statement can never be executed, and so should be removed." cwe="561" file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="109" column="13"/>
        </error>
        <error id="constVariableReference" severity="style" msg="Variable &apos;sc&apos; can be declared as reference to const" verbose="Variable &apos;sc&apos; can be declared as reference to const" cwe="398" file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="37" column="20" info="Variable &apos;sc&apos; can be declared as reference to const"/>
            <symbol>sc</symbol>
        </error>
        <error id="constVariableReference" severity="style" msg="Variable &apos;gc&apos; can be declared as reference to const" verbose="Variable &apos;gc&apos; can be declared as reference to const" cwe="398" file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="47" column="16" info="Variable &apos;gc&apos; can be declared as reference to const"/>
            <symbol>gc</symbol>
        </error>
        <error id="constVariableReference" severity="style" msg="Variable &apos;sc&apos; can be declared as reference to const" verbose="Variable &apos;sc&apos; can be declared as reference to const" cwe="398" file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="51" column="20" info="Variable &apos;sc&apos; can be declared as reference to const"/>
            <symbol>sc</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;h&apos; can be declared as pointer to const" verbose="Parameter &apos;h&apos; can be declared as pointer to const" cwe="398" file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="310" column="53" info="Parameter &apos;h&apos; can be declared as pointer to const"/>
            <symbol>h</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;req&apos; can be declared as pointer to const" verbose="Parameter &apos;req&apos; can be declared as pointer to const" cwe="398" file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="352" column="87" info="Parameter &apos;req&apos; can be declared as pointer to const"/>
            <symbol>req</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;req&apos; can be declared as pointer to const" verbose="Parameter &apos;req&apos; can be declared as pointer to const" cwe="398" file0="src/StandaloneServerPool.cpp">
            <location file="src/StandaloneServerPool.cpp" line="426" column="84" info="Parameter &apos;req&apos; can be declared as pointer to const"/>
            <symbol>req</symbol>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/Subscribe.cpp">
            <location file="src/Subscribe.cpp" line="0" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;stdio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;stdio.h&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Timer.cpp" line="7" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;algorithm&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;algorithm&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Timer.cpp" line="8" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;iostream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;iostream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/Timer.cpp" line="9" column="0"/>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;p1&apos; can be declared as pointer to const" verbose="Parameter &apos;p1&apos; can be declared as pointer to const" cwe="398" file0="src/Timer.cpp">
            <location file="src/Timer.cpp" line="49" column="28" info="Parameter &apos;p1&apos; can be declared as pointer to const"/>
            <symbol>p1</symbol>
        </error>
        <error id="constParameterPointer" severity="style" msg="Parameter &apos;p2&apos; can be declared as pointer to const" verbose="Parameter &apos;p2&apos; can be declared as pointer to const" cwe="398" file0="src/Timer.cpp">
            <location file="src/Timer.cpp" line="49" column="44" info="Parameter &apos;p2&apos; can be declared as pointer to const"/>
            <symbol>p2</symbol>
        </error>
        <error id="constVariablePointer" severity="style" msg="Variable &apos;p&apos; can be declared as pointer to const" verbose="Variable &apos;p&apos; can be declared as pointer to const" cwe="398" file0="src/Timer.cpp">
            <location file="src/Timer.cpp" line="53" column="14" info="Variable &apos;p&apos; can be declared as pointer to const"/>
            <symbol>p</symbol>
        </error>
        <error id="unreadVariable" severity="style" msg="Variable &apos;lck&apos; is assigned a value that is never used." verbose="Variable &apos;lck&apos; is assigned a value that is never used." cwe="563" file0="src/Timer.cpp">
            <location file="src/Timer.cpp" line="23" column="26"/>
            <symbol>lck</symbol>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;iostream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;iostream&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/main.cpp" line="7" column="0"/>
        </error>
        <error id="missingIncludeSystem" severity="information" msg="Include file: &lt;exception&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results." verbose="Include file: &lt;exception&gt; not found. Please note: Cppcheck does not need standard library headers to get proper results.">
            <location file="src/main.cpp" line="8" column="0"/>
        </error>
        <error id="normalCheckLevelMaxBranches" severity="information" msg="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." verbose="Limiting analysis of branches. Use --check-level=exhaustive to analyze all branches." file0="src/main.cpp">
            <location file="src/main.cpp" line="0" column="0"/>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;servers&apos; is never used." verbose="The function &apos;servers&apos; is never used." cwe="561">
            <location file="src/ClusterServerPool.h" line="24" column="0"/>
            <symbol>servers</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;isMultiKey&apos; is never used." verbose="The function &apos;isMultiKey&apos; is never used." cwe="561">
            <location file="src/Command.h" line="223" column="0"/>
            <symbol>isMultiKey</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;isSMultiKey&apos; is never used." verbose="The function &apos;isSMultiKey&apos; is never used." cwe="561">
            <location file="src/Command.h" line="227" column="0"/>
            <symbol>isSMultiKey</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;isMultiKeyVal&apos; is never used." verbose="The function &apos;isMultiKeyVal&apos; is never used." cwe="561">
            <location file="src/Command.h" line="231" column="0"/>
            <symbol>isMultiKeyVal</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;isAnyMulti&apos; is never used." verbose="The function &apos;isAnyMulti&apos; is never used." cwe="561">
            <location file="src/Command.h" line="235" column="0"/>
            <symbol>isAnyMulti</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;isAuth&apos; is never used." verbose="The function &apos;isAuth&apos; is never used." cwe="561">
            <location file="src/ConnectConnection.h" line="45" column="0"/>
            <symbol>isAuth</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;readonly&apos; is never used." verbose="The function &apos;readonly&apos; is never used." cwe="561">
            <location file="src/ConnectConnection.h" line="53" column="0"/>
            <symbol>readonly</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;setReadonly&apos; is never used." verbose="The function &apos;setReadonly&apos; is never used." cwe="561">
            <location file="src/ConnectConnection.h" line="57" column="0"/>
            <symbol>setReadonly</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;pendRequestCount&apos; is never used." verbose="The function &apos;pendRequestCount&apos; is never used." cwe="561">
            <location file="src/ConnectConnection.h" line="73" column="0"/>
            <symbol>pendRequestCount</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;getReadPolicy&apos; is never used." verbose="The function &apos;getReadPolicy&apos; is never used." cwe="561">
            <location file="src/DC.h" line="44" column="0"/>
            <symbol>getReadPolicy</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;pop_back&apos; is never used." verbose="The function &apos;pop_back&apos; is never used." cwe="561">
            <location file="src/Deque.h" line="163" column="0"/>
            <symbol>pop_back</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;maxId&apos; is never used." verbose="The function &apos;maxId&apos; is never used." cwe="561">
            <location file="src/ID.h" line="45" column="0"/>
            <symbol>maxId</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;supportSubscribe&apos; is never used." verbose="The function &apos;supportSubscribe&apos; is never used." cwe="561">
            <location file="src/Proxy.h" line="60" column="0"/>
            <symbol>supportSubscribe</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;redirectCnt&apos; is never used." verbose="The function &apos;redirectCnt&apos; is never used." cwe="561">
            <location file="src/Request.h" line="145" column="0"/>
            <symbol>redirectCnt</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;command&apos; is never used." verbose="The function &apos;command&apos; is never used." cwe="561">
            <location file="src/RequestParser.h" line="77" column="0"/>
            <symbol>command</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;isInteger&apos; is never used." verbose="The function &apos;isInteger&apos; is never used." cwe="561">
            <location file="src/Response.h" line="86" column="0"/>
            <symbol>isInteger</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;isMaster&apos; is never used." verbose="The function &apos;isMaster&apos; is never used." cwe="561">
            <location file="src/Server.h" line="55" column="0"/>
            <symbol>isMaster</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;isSlave&apos; is never used." verbose="The function &apos;isSlave&apos; is never used." cwe="561">
            <location file="src/Server.h" line="59" column="0"/>
            <symbol>isSlave</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;refreshInterval&apos; is never used." verbose="The function &apos;refreshInterval&apos; is never used." cwe="561">
            <location file="src/ServerPool.h" line="55" column="0"/>
            <symbol>refreshInterval</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;inPendSub&apos; is never used." verbose="The function &apos;inPendSub&apos; is never used." cwe="561">
            <location file="src/Subscribe.h" line="26" column="0"/>
            <symbol>inPendSub</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;lock&apos; is never used." verbose="The function &apos;lock&apos; is never used." cwe="561">
            <location file="src/Sync.h" line="34" column="0"/>
            <symbol>lock</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;unlock&apos; is never used." verbose="The function &apos;unlock&apos; is never used." cwe="561">
            <location file="src/Sync.h" line="37" column="0"/>
            <symbol>unlock</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;restart&apos; is never used." verbose="The function &apos;restart&apos; is never used." cwe="561">
            <location file="src/Timer.h" line="70" column="0"/>
            <symbol>restart</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;inPendWatch&apos; is never used." verbose="The function &apos;inPendWatch&apos; is never used." cwe="561">
            <location file="src/Transaction.h" line="25" column="0"/>
            <symbol>inPendWatch</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;inWatch&apos; is never used." verbose="The function &apos;inWatch&apos; is never used." cwe="561">
            <location file="src/Transaction.h" line="42" column="0"/>
            <symbol>inWatch</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;decrWatch&apos; is never used." verbose="The function &apos;decrWatch&apos; is never used." cwe="561">
            <location file="src/Transaction.h" line="50" column="0"/>
            <symbol>decrWatch</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;inPendMulti&apos; is never used." verbose="The function &apos;inPendMulti&apos; is never used." cwe="561">
            <location file="src/Transaction.h" line="60" column="0"/>
            <symbol>inPendMulti</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;nowSec&apos; is never used." verbose="The function &apos;nowSec&apos; is never used." cwe="561">
            <location file="src/Util.h" line="60" column="0"/>
            <symbol>nowSec</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;nowMSec&apos; is never used." verbose="The function &apos;nowMSec&apos; is never used." cwe="561">
            <location file="src/Util.h" line="64" column="0"/>
            <symbol>nowMSec</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;elapsedSec&apos; is never used." verbose="The function &apos;elapsedSec&apos; is never used." cwe="561">
            <location file="src/Util.h" line="72" column="0"/>
            <symbol>elapsedSec</symbol>
        </error>
        <error id="unusedFunction" severity="style" msg="The function &apos;elapsedMSec&apos; is never used." verbose="The function &apos;elapsedMSec&apos; is never used." cwe="561">
            <location file="src/Util.h" line="76" column="0"/>
            <symbol>elapsedMSec</symbol>
        </error>
        <error id="checkersReport" severity="information" msg="Active checkers: 167/856 (use --checkers-report=&lt;filename&gt; to see details)" verbose="Active checkers: 167/856 (use --checkers-report=&lt;filename&gt; to see details)"/>
    </errors>
</results>
