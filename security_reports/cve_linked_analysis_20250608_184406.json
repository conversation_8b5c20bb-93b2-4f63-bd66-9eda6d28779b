{"scan_time": "2025-06-08T18:44:06.639465", "project": "Predixy", "cve_query_enabled": true, "findings": {"dangerous_functions": {"memcpy": {"risk_level": "MEDIUM", "issue": "内存复制操作，需要边界检查", "cve_keywords": ["memcpy buffer overflow", "memory copy vulnerability"], "locations": [{"file": "src/Socket.cpp", "line": 101, "code": "memcpy(res, &sun, *len);"}, {"file": "src/Socket.cpp", "line": 130, "code": "memcpy(res, dst->ai_addr, *len);"}, {"file": "src/Buffer.cpp", "line": 22, "code": "memcpy(mDat, oth.mDat, mLen);"}, {"file": "src/Buffer.cpp", "line": 42, "code": "memcpy(mDat, oth.mDat, mLen);"}, {"file": "src/Buffer.cpp", "line": 60, "code": "memcpy(buf->tail(), dat, len);"}, {"file": "src/Buffer.cpp", "line": 65, "code": "memcpy(buf->tail(), dat, n);"}, {"file": "src/Buffer.cpp", "line": 349, "code": "memcpy(dat, buf->data() + pos, len > num ? num : len);"}, {"file": "src/LogFileSink.cpp", "line": 146, "code": "memcpy(mFilePath, path, len);"}], "related_cves": []}}, "network_files": ["src/ConnectConnectionPool.cpp", "src/AcceptConnection.cpp", "src/Socket.cpp", "src/ConnectSocket.cpp", "src/Request.cpp", "src/Proxy.cpp", "src/Response.cpp", "src/ListenSocket.cpp", "src/Handler.cpp"], "memory_issues": [], "cve_matches": [{"cve_id": "CVE-2024-32973", "description": "Pluto is a superset of Lua 5.4 with a focus on general-purpose programming. In affected versions an attacker with the ability to actively intercept network traffic would be able to use a specifically-crafted certificate to fool <PERSON><PERSON><PERSON> into trusting it to be the intended remote for the TLS session. This results in the HTTP library and socket.starttls providing less transport integrity than expected. This issue has been patched in pull request #851 which has been included in version 0.9.3. Users are advised to upgrade. there are no known workarounds for this vulnerability.", "severity": "Unknown", "score": 0, "published": "2024-05-01T11:15:46.200", "vector": "Unknown", "references": ["https://github.com/PlutoLang/Pluto/pull/851", "https://github.com/PlutoLang/Pluto/security/advisories/GHSA-84hj-7j2v-w665"]}], "recommendations": [{"priority": "MEDIUM", "type": "CODE_REVIEW", "location": "src/Socket.cpp:101", "issue": "memcpy操作需要边界检查", "current_code": "memcpy(res, &sun, *len);", "suggested_fix": "确保复制长度不超过目标缓冲区大小", "explanation": "检查memcpy的长度参数是否安全"}, {"priority": "MEDIUM", "type": "CODE_REVIEW", "location": "src/Socket.cpp:130", "issue": "memcpy操作需要边界检查", "current_code": "memcpy(res, dst->ai_addr, *len);", "suggested_fix": "确保复制长度不超过目标缓冲区大小", "explanation": "检查memcpy的长度参数是否安全"}, {"priority": "MEDIUM", "type": "CODE_REVIEW", "location": "src/Buffer.cpp:22", "issue": "memcpy操作需要边界检查", "current_code": "memcpy(mDat, oth.mDat, mLen);", "suggested_fix": "确保复制长度不超过目标缓冲区大小", "explanation": "检查memcpy的长度参数是否安全"}, {"priority": "MEDIUM", "type": "CODE_REVIEW", "location": "src/Buffer.cpp:42", "issue": "memcpy操作需要边界检查", "current_code": "memcpy(mDat, oth.mDat, mLen);", "suggested_fix": "确保复制长度不超过目标缓冲区大小", "explanation": "检查memcpy的长度参数是否安全"}, {"priority": "MEDIUM", "type": "CODE_REVIEW", "location": "src/Buffer.cpp:60", "issue": "memcpy操作需要边界检查", "current_code": "memcpy(buf->tail(), dat, len);", "suggested_fix": "确保复制长度不超过目标缓冲区大小", "explanation": "检查memcpy的长度参数是否安全"}, {"priority": "MEDIUM", "type": "CODE_REVIEW", "location": "src/Buffer.cpp:65", "issue": "memcpy操作需要边界检查", "current_code": "memcpy(buf->tail(), dat, n);", "suggested_fix": "确保复制长度不超过目标缓冲区大小", "explanation": "检查memcpy的长度参数是否安全"}, {"priority": "MEDIUM", "type": "CODE_REVIEW", "location": "src/Buffer.cpp:349", "issue": "memcpy操作需要边界检查", "current_code": "memcpy(dat, buf->data() + pos, len > num ? num : len);", "suggested_fix": "确保复制长度不超过目标缓冲区大小", "explanation": "检查memcpy的长度参数是否安全"}, {"priority": "MEDIUM", "type": "CODE_REVIEW", "location": "src/LogFileSink.cpp:146", "issue": "memcpy操作需要边界检查", "current_code": "memcpy(mFilePath, path, len);", "suggested_fix": "确保复制长度不超过目标缓冲区大小", "explanation": "检查memcpy的长度参数是否安全"}]}, "summary": {"total_dangerous_functions": 1, "total_cves_found": 0, "total_recommendations": 8, "cve_linked_recommendations": 0}}