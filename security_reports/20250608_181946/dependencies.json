{"system_libs": ["llvm", ""], "compiler_info": {}, "build_system": "cmake", "cmake_content": "cmake_minimum_required(VERSION 3.18)\n#set (CMAKE_C_COMPILER \"/usr/bin/gcc\")\n#set (CMAKE_CXX_COMPILER \"/usr/bin/c++\")\nset(CMAKE_CXX_STANDARD 11)\n\nproject(predixy)\n\nadd_definitions(-D_PREDIXY_BACKTRACE_ -D_KQUEUE_)\nadd_compile_options(-std=c++11 -Wall -w -g -O3 )\n\n#option(MAC \"select to use specified function\" ON)\n#if(MAC)\n#    add_definitions(-D_PREDIXY_BACKTRACE_ -D_KQUEUE_)\n#    add_compile_options(-Wall -w -g -O3)\n#else()\n#endif(MAC)\n\nset(INC_DIR /usr/local/include)\n\ninclude_directories(${INC_DIR}, src)\n\nadd_executable(predixy\n        src/AcceptConnection.cpp\n        src/AcceptConnection.h\n        src/AcceptSocket.cpp\n        src/AcceptSocket.h\n        src/Alloc.cpp\n        src/Alloc.h\n        src/Auth.cpp\n        src/Auth.h\n        src/Backtrace.h\n        src/Buffer.cpp\n        src/Buffer.h\n        src/ClusterNodesParser.cpp\n        src/ClusterNodesParser.h\n        src/ClusterServerPool.cpp\n        src/ClusterServerPool.h\n        src/Command.cpp\n        src/Command.h\n        src/Common.h\n        src/Conf.cpp\n        src/Conf.h\n        src/ConfParser.cpp\n        src/ConfParser.h\n        src/ConnectConnection.cpp\n        src/ConnectConnection.h\n        src/ConnectConnectionPool.cpp\n        src/ConnectConnectionPool.h\n        src/Connection.cpp\n        src/Connection.h\n        src/ConnectSocket.cpp\n        src/ConnectSocket.h\n        src/Crc16.cpp\n        src/DC.cpp\n        src/DC.h\n        src/Deque.h\n        src/Distribution.cpp\n        src/Distribution.h\n        src/Enums.cpp\n        src/Enums.h\n#        src/EpollMultiplexor.cpp\n#        src/EpollMultiplexor.h\n        src/Exception.h\n        src/Handler.cpp\n        src/Handler.h\n        src/HashFunc.cpp\n        src/HashFunc.h\n        src/ID.h\n        src/IOVec.h\n        src/KqueueMultiplexor.cpp\n        src/KqueueMultiplexor.h\n        src/LatencyMonitor.cpp\n        src/LatencyMonitor.h\n        src/List.h\n        src/ListenSocket.cpp\n        src/ListenSocket.h\n        src/LogFileSink.cpp\n        src/LogFileSink.h\n        src/Logger.cpp\n        src/Logger.h\n        src/main.cpp\n        src/Multiplexor.h\n#        src/PollMultiplexor.cpp\n#        src/PollMultiplexor.h\n        src/Predixy.h\n        src/Proxy.cpp\n        src/Proxy.h\n        src/Reply.cpp\n        src/Reply.h\n        src/Request.cpp\n        src/Request.h\n        src/RequestParser.cpp\n        src/RequestParser.h\n        src/Response.cpp\n        src/Response.h\n        src/ResponseParser.cpp\n        src/ResponseParser.h\n#        src/SentinelServerPool.cpp\n#        src/SentinelServerPool.h\n        src/Server.cpp\n        src/Server.h\n        src/ServerGroup.cpp\n        src/ServerGroup.h\n        src/ServerPool.cpp\n        src/ServerPool.h\n        src/Socket.cpp\n        src/Socket.h\n        src/StandaloneServerPool.cpp\n        src/StandaloneServerPool.h\n        src/Stats.h\n        src/PString.h\n        src/Subscribe.cpp\n        src/Subscribe.h\n        src/Sync.h\n        src/Timer.cpp\n        src/Timer.h\n        src/Transaction.h\n        src/Util.h)\n", "compile_options": ["-std=c++11 -Wall -w -g -O3 ", "-Wall -w -g -O3"]}