{"query_time": "2025-06-08T18:20:16.908253", "keywords_searched": ["redis proxy", "c++ buffer overflow", "socket programming", "network proxy", "tcp proxy", "memory corruption"], "relevant_cves": [{"cve_id": "CVE-2020-15698", "description": "An issue was discovered in Joomla! through 3.9.19. Inadequate filtering on the system information screen could expose Redis or proxy credentials", "severity": "Unknown", "score": 0, "published": "2020-07-15T16:15:11.443", "keyword": "redis proxy"}, {"cve_id": "CVE-2021-41230", "description": "Pomerium is an open source identity-aware access proxy. In affected versions changes to the OIDC claims of a user after initial login are not reflected in policy evaluation when using `allowed_idp_claims` as part of policy. If using `allowed_idp_claims` and a user's claims are changed, Pomerium can make incorrect authorization decisions. This issue has been resolved in v0.15.6. For users unable to upgrade clear data on `databroker` service by clearing redis or restarting the in-memory databroker to force claims to be updated.", "severity": "Unknown", "score": 0, "published": "2021-11-05T23:15:08.727", "keyword": "redis proxy"}, {"cve_id": "CVE-2002-1973", "description": "Buffer overflow in CHttpServer::OnParseError in the ISAPI extension (Isapi.cpp) when built using Microsoft Foundation Class (MFC) static libraries in Visual C++ 5.0, and 6.0 before SP3, as used in multiple products including BadBlue, allows remote attackers to cause a denial of service (access violation and crash) and possibly execute arbitrary code via a long query string that causes a parsing error.", "severity": "Unknown", "score": 0, "published": "2002-12-31T05:00:00.000", "keyword": "c++ buffer overflow"}, {"cve_id": "CVE-2006-2482", "description": "Heap-based buffer overflow in the TZipTV component in (1) ZipTV for Delphi 7 2006.1.26 and for C++ Builder 2006-1.16, (2) PentaZip ********* and PentaSuite-PRO *********, and possibly other products, allows user-assisted attackers to execute arbitrary code via an ARJ archive with a long header. NOTE: the ACE archive vector is covered by CVE-2005-2856.", "severity": "Unknown", "score": 0, "published": "2006-09-08T21:04:00.000", "keyword": "c++ buffer overflow"}, {"cve_id": "CVE-2007-0468", "description": "Stack-based buffer overflow in rcdll.dll in msdev.exe in Visual C++ (MSVC) in Microsoft Visual Studio 6.0 SP6 allows user-assisted remote attackers to execute arbitrary code via a long file path in the \"1 TYPELIB MOVEABLE PURE\" option in an RC file.", "severity": "Unknown", "score": 0, "published": "2007-01-24T01:28:00.000", "keyword": "c++ buffer overflow"}, {"cve_id": "CVE-2009-1130", "description": "Heap-based buffer overflow in Microsoft Office PowerPoint 2002 SP3 and 2003 SP3, and PowerPoint in Microsoft Office 2004 for Mac, allows remote attackers to execute arbitrary code via a crafted structure in a Notes container in a PowerPoint file that causes PowerPoint to read more data than was allocated when creating a C++ object, leading to an overwrite of a function pointer, aka \"Heap Corruption Vulnerability.\"", "severity": "Unknown", "score": 0, "published": "2009-05-12T22:30:00.377", "keyword": "c++ buffer overflow"}, {"cve_id": "CVE-2011-2516", "description": "Off-by-one error in the XML signature feature in Apache XML Security for C++ 1.6.0, as used in Shibboleth before 2.4.3 and possibly other products, allows remote attackers to cause a denial of service (crash) via a signature using a large RSA key, which triggers a buffer overflow.", "severity": "Unknown", "score": 0, "published": "2011-07-11T20:55:01.380", "keyword": "c++ buffer overflow"}, {"cve_id": "CVE-2013-2154", "description": "Stack-based buffer overflow in the XML Signature Reference functionality (xsec/dsig/DSIGReference.cpp) in Apache Santuario XML Security for C++ (aka xml-security-c) before 1.7.1 allows context-dependent attackers to cause a denial of service (crash) and possibly execute arbitrary code via malformed XPointer expressions, probably related to the DSIGReference::getURIBaseTXFM function.", "severity": "Unknown", "score": 0, "published": "2013-08-20T22:55:03.787", "keyword": "c++ buffer overflow"}, {"cve_id": "CVE-2013-2156", "description": "Heap-based buffer overflow in the Exclusive Canonicalization functionality (xsec/canon/XSECC14n20010315.cpp) in Apache Santuario XML Security for C++ (aka xml-security-c) before 1.7.1 allows remote attackers to cause a denial of service (crash) and possibly execute arbitrary code via a crafted PrefixList attribute.", "severity": "Unknown", "score": 0, "published": "2013-08-20T22:55:03.847", "keyword": "c++ buffer overflow"}, {"cve_id": "CVE-2013-2210", "description": "Heap-based buffer overflow in the XML Signature Reference functionality in Apache Santuario XML Security for C++ (aka xml-security-c) before 1.7.2 allows context-dependent attackers to cause a denial of service (crash) and possibly execute arbitrary code via malformed XPointer expressions.  NOTE: this is due to an incorrect fix for CVE-2013-2154.", "severity": "Unknown", "score": 0, "published": "2013-08-20T22:55:04.120", "keyword": "c++ buffer overflow"}, {"cve_id": "CVE-2014-0774", "description": "Stack-based buffer overflow in the C++ sample client in Schneider Electric OPC Factory Server (OFS) TLXCDSUOFS33 - 3.35, TLXCDSTOFS33 - 3.35, TLXCDLUOFS33 - 3.35, TLXCDLTOFS33 - 3.35, and TLXCDLFOFS33 - 3.35 allows local users to gain privileges via vectors involving a malformed configuration file.", "severity": "Unknown", "score": 0, "published": "2014-02-28T06:18:54.277", "keyword": "c++ buffer overflow"}, {"cve_id": "CVE-2014-0993", "description": "Buffer overflow in the Vcl.Graphics.TPicture.Bitmap implementation in the Visual Component Library (VCL) in Embarcadero Delphi XE6 20.0.15596.9843 and C++ Builder XE6 20.0.15596.9843 allows remote attackers to execute arbitrary code via a crafted BMP file.", "severity": "Unknown", "score": 0, "published": "2014-09-15T14:55:10.917", "keyword": "c++ buffer overflow"}, {"cve_id": "CVE-2024-32973", "description": "Pluto is a superset of Lua 5.4 with a focus on general-purpose programming. In affected versions an attacker with the ability to actively intercept network traffic would be able to use a specifically-crafted certificate to fool <PERSON><PERSON><PERSON> into trusting it to be the intended remote for the TLS session. This results in the HTTP library and socket.starttls providing less transport integrity than expected. This issue has been patched in pull request #851 which has been included in version 0.9.3. Users are advised to upgrade. there are no known workarounds for this vulnerability.", "severity": "Unknown", "score": 0, "published": "2024-05-01T11:15:46.200", "keyword": "socket programming"}, {"cve_id": "CVE-2000-0416", "description": "NTMail 5.x allows network users to bypass the NTMail proxy restrictions by redirecting their requests to NTMail's web configuration server.", "severity": "Unknown", "score": 0, "published": "2000-05-11T04:00:00.000", "keyword": "network proxy"}, {"cve_id": "CVE-2004-1502", "description": "The Telnet proxy in 602 Lan Suite 2004.0.04.0909 and earlier allows remote attackers to cause a denial of service (socket exhaustion) via a Telnet request to an IP address of the proxy's network interface, which causes a loop.", "severity": "Unknown", "score": 0, "published": "2004-12-31T05:00:00.000", "keyword": "network proxy"}, {"cve_id": "CVE-2004-2545", "description": "Secure Computing Corporation Sidewinder G2 ******** allows remote attackers to cause a denial of service (SMTP proxy failure) via unknown attack vendors involving an \"extremely busy network.\"  NOTE: this might not be a vulnerability because the embedded monitoring sub-system automatically restarts after the failure.", "severity": "Unknown", "score": 0, "published": "2004-12-31T05:00:00.000", "keyword": "network proxy"}, {"cve_id": "CVE-2006-2112", "description": "Fuji Xerox Printing Systems (FXPS) print engine, as used in products including (1) Dell 3000cn through 5110cn and (2) Fuji Xerox DocuPrint firmware before 20060628 and Network Option Card firmware before 5.13, allows remote attackers to use the FTP printing interface as a proxy (\"FTP bounce\") by using arbitrary PORT arguments to connect to systems for which access would be otherwise restricted.", "severity": "Unknown", "score": 0, "published": "2006-08-25T01:04:00.000", "keyword": "network proxy"}, {"cve_id": "CVE-2007-5170", "description": "Unspecified vulnerability in the embedded service processor (SP) before 3.09 in Sun Fire X2100 M2 and X2200 M2 Embedded Lights Out Manager (ELOM) allows remote attackers to send arbitrary network traffic and use ELOM as a spam proxy.", "severity": "Unknown", "score": 0, "published": "2007-10-01T20:17:00.000", "keyword": "network proxy"}, {"cve_id": "CVE-2008-1744", "description": "The Certificate Authority Proxy Function (CAPF) service in Cisco Unified Communications Manager (CUCM) 4.1 before 4.1(3)SR7, 4.2 before 4.2(3)SR4, and 4.3 before 4.3(2) allows remote attackers to cause a denial of service (service crash) via malformed network traffic, aka Bug ID CSCsk46770.", "severity": "Unknown", "score": 0, "published": "2008-05-16T12:54:00.000", "keyword": "network proxy"}, {"cve_id": "CVE-2009-0468", "description": "Multiple cross-site request forgery (CSRF) vulnerabilities in ajax.html in Profense Web Application Firewall 2.6.2 and 2.6.3 allow remote attackers to hijack the authentication of administrators for requests that (1) shutdown the server, (2) send ping packets, (3) enable network services, (4) configure a proxy server, and (5) modify other settings via parameters in the query string.", "severity": "Unknown", "score": 0, "published": "2009-02-10T07:00:24.767", "keyword": "network proxy"}, {"cve_id": "CVE-2009-0630", "description": "The (1) Cisco Unified Communications Manager Express; (2) SIP Gateway Signaling Support Over Transport Layer Security (TLS) Transport; (3) Secure Signaling and Media Encryption; (4) Blocks Extensible Exchange Protocol (BEEP); (5) Network Admission Control HTTP Authentication Proxy; (6) Per-user URL Redirect for EAPoUDP, Dot1x, and MAC Authentication Bypass; (7) Distributed Director with HTTP Redirects; and (8) TCP DNS features in Cisco IOS 12.0 through 12.4 do not properly handle IP sockets, which allows remote attackers to cause a denial of service (outage or resource consumption) via a series of crafted TCP packets.", "severity": "Unknown", "score": 0, "published": "2009-03-27T16:30:02.017", "keyword": "network proxy"}, {"cve_id": "CVE-2008-6882", "description": "Live Chat (com_livechat) component 1.0 for Joomla! allows remote attackers to use the xmlhttp.php script as an open HTTP proxy to hide network scanning activities or scan internal networks via a GET request with a full URL in the query string.", "severity": "Unknown", "score": 0, "published": "2009-07-30T19:30:00.297", "keyword": "network proxy"}, {"cve_id": "CVE-2009-2936", "description": "The Command Line Interface (aka Server CLI or administration interface) in the master process in the reverse proxy server in Varnish before 2.1.0 does not require authentication for commands received through a TCP port, which allows remote attackers to (1) execute arbitrary code via a vcl.inline directive that provides a VCL configuration file containing inline C code; (2) change the ownership of the master process via param.set, stop, and start directives; (3) read the initial line of an arbitrary file via a vcl.load directive; or (4) conduct cross-site request forgery (CSRF) attacks that leverage a victim's location on a trusted network and improper input validation of directives.  NOTE: the vendor disputes this report, saying that it is \"fundamentally misguided and pointless.", "severity": "Unknown", "score": 0, "published": "2010-04-05T16:30:00.453", "keyword": "network proxy"}, {"cve_id": "CVE-2002-0778", "description": "The default configuration of the proxy for Cisco Cache Engine and Content Engine allows remote attackers to use HTTPS to make TCP connections to allowed IP addresses while hiding the actual source IP.", "severity": "Unknown", "score": 0, "published": "2002-08-12T04:00:00.000", "keyword": "tcp proxy"}, {"cve_id": "CVE-2002-1001", "description": "Buffer overflows in AnalogX Proxy before 4.12 allows remote attackers to cause a denial of service and possibly execute arbitrary code via (1) a long HTTP request to TCP port 6588 or (2) a SOCKS 4A request to TCP port 1080 with a long DNS hostname.", "severity": "Unknown", "score": 0, "published": "2002-10-04T04:00:00.000", "keyword": "tcp proxy"}, {"cve_id": "CVE-2002-1484", "description": "DB4Web server, when configured to use verbose debug messages, allows remote attackers to use DB4Web as a proxy and attempt TCP connections to other systems (port scan) via a request for a URL that specifies the target IP address and port, which produces a connection status in the resulting error message.", "severity": "Unknown", "score": 0, "published": "2003-04-22T04:00:00.000", "keyword": "tcp proxy"}, {"cve_id": "CVE-2003-1159", "description": "Plug and Play Web Server Proxy 1.0002c allows remote attackers to cause a denial of service (server crash) via an invalid URI in an HTTP GET request to TCP port 8080.", "severity": "Unknown", "score": 0, "published": "2003-10-31T05:00:00.000", "keyword": "tcp proxy"}, {"cve_id": "CVE-2004-2107", "description": "Finjan SurfinGate 6.0 and 7.0, when running in proxy mode, does not authenticate FHTTP commands on TCP port 3141, which allows remote attackers to use the finjan-parameter-type header to (1) restart the service, (2) use the getlastmsg command to view log information, or (3) use the online command to force a policy update from the database server.", "severity": "Unknown", "score": 0, "published": "2004-12-31T05:00:00.000", "keyword": "tcp proxy"}, {"cve_id": "CVE-2006-2322", "description": "The transparent proxy feature of the Cisco Application Velocity System (AVS) 3110 5.0 and 4.0 and earlier, and 3120 5.0.0 and earlier, has a default configuration that allows remote attackers to proxy arbitrary TCP connections, aka Bug ID CSCsd32143.", "severity": "Unknown", "score": 0, "published": "2006-05-12T00:02:00.000", "keyword": "tcp proxy"}, {"cve_id": "CVE-2007-4700", "description": "Unspecified vulnerability in WebKit on Apple Mac OS X 10.4 through 10.4.10 allows remote attackers to use Safari as an indirect proxy and send attacker-controlled data to arbitrary TCP ports via unknown vectors.", "severity": "Unknown", "score": 0, "published": "2007-11-15T02:46:00.000", "keyword": "tcp proxy"}, {"cve_id": "CVE-2008-1491", "description": "Stack-based buffer overflow in the DPC Proxy server (DpcProxy.exe) in ASUS Remote Console (aka ARC or ASMB3) ******** and ******** allows remote attackers to execute arbitrary code via a long string to TCP port 623.", "severity": "Unknown", "score": 0, "published": "2008-03-25T19:44:00.000", "keyword": "tcp proxy"}, {"cve_id": "CVE-2008-1741", "description": "The SIP Proxy (SIPD) service in Cisco Unified Presence before 6.0(3) allows remote attackers to cause a denial of service (core dump and service interruption) via a TCP port scan, aka Bug ID CSCsj64533.", "severity": "Unknown", "score": 0, "published": "2008-05-16T12:54:00.000", "keyword": "tcp proxy"}, {"cve_id": "CVE-2009-0057", "description": "The Certificate Authority Proxy Function (CAPF) service in Cisco Unified Communications Manager 5.x before 5.1(3e) and 6.x before 6.1(3) allows remote attackers to cause a denial of service (voice service outage) by sending malformed input over a TCP session in which the \"client terminates prematurely.\"", "severity": "Unknown", "score": 0, "published": "2009-01-22T18:30:03.813", "keyword": "tcp proxy"}, {"cve_id": "CVE-2000-0484", "description": "Small HTTP Server ver 3.06 contains a memory corruption bug causing a memory overflow. The overflowed buffer crashes into a Structured Exception Handler resulting in a Denial of Service.", "severity": "Unknown", "score": 0, "published": "2000-06-15T04:00:00.000", "keyword": "memory corruption"}, {"cve_id": "CVE-2002-0004", "description": "Heap corruption vulnerability in the \"at\" program allows local users to execute arbitrary code via a malformed execution time, which causes at to free the same memory twice.", "severity": "Unknown", "score": 0, "published": "2002-02-27T05:00:00.000", "keyword": "memory corruption"}, {"cve_id": "CVE-2003-0525", "description": "The getCanonicalPath function in Windows NT 4.0 may free memory that it does not own and cause heap corruption, which allows attackers to cause a denial of service (crash) via requests that cause a long file name to be passed to getCanonicalPath, as demonstrated on the IBM JVM using a long string to the java.io.getCanonicalPath Java method.", "severity": "Unknown", "score": 0, "published": "2003-08-27T04:00:00.000", "keyword": "memory corruption"}, {"cve_id": "CVE-2004-0714", "description": "Cisco Internetwork Operating System (IOS) 12.0S through 12.3T attempts to process SNMP solicited operations on improper ports (UDP 162 and a randomly chosen UDP port), which allows remote attackers to cause a denial of service (device reload and memory corruption).", "severity": "Unknown", "score": 0, "published": "2004-07-27T04:00:00.000", "keyword": "memory corruption"}, {"cve_id": "CVE-2004-0842", "description": "Internet Explorer 6.0 SP1 and earlier, and possibly other versions, allows remote attackers to cause a denial of service (application crash from \"memory corruption\") via certain malformed Cascading Style Sheet (CSS) elements that trigger heap-based buffer overflows, as demonstrated using the \"<STYLE>@;/*\" string, possibly due to a missing comment terminator that may cause an invalid length to trigger a large memory copy operation, aka the \"CSS Heap Memory Corruption Vulnerability.\"", "severity": "Unknown", "score": 0, "published": "2004-12-23T05:00:00.000", "keyword": "memory corruption"}, {"cve_id": "CVE-2004-2533", "description": "Serv-U FTP Server 4.1 (possibly 4.0) allows remote attackers to cause a denial of service (application crash) via a SITE CHMOD command with a \"\\\\...\\\" followed by a short string, causing partial memory corruption, a different vulnerability than CVE-2004-2111.", "severity": "Unknown", "score": 0, "published": "2004-12-31T05:00:00.000", "keyword": "memory corruption"}, {"cve_id": "CVE-2004-1012", "description": "The argument parser of the PARTIAL command in Cyrus IMAP Server 2.2.6 and earlier allows remote authenticated users to execute arbitrary code via a certain command (\"body[p\") that is treated as a different command (\"body.peek\") and causes an index increment error that leads to an out-of-bounds memory corruption.", "severity": "Unknown", "score": 0, "published": "2005-01-10T05:00:00.000", "keyword": "memory corruption"}, {"cve_id": "CVE-2004-1013", "description": "The argument parser of the FETCH command in Cyrus IMAP Server 2.2.x through 2.2.8 allows remote authenticated users to execute arbitrary code via certain commands such as (1) \"body[p\", (2) \"binary[p\", or (3) \"binary[p\") that cause an index increment error that leads to an out-of-bounds memory corruption.", "severity": "Unknown", "score": 0, "published": "2005-01-10T05:00:00.000", "keyword": "memory corruption"}, {"cve_id": "CVE-2004-0886", "description": "Multiple integer overflows in libtiff 3.6.1 and earlier allow remote attackers to cause a denial of service (crash or memory corruption) via TIFF images that lead to incorrect malloc calls.", "severity": "Unknown", "score": 0, "published": "2005-01-27T05:00:00.000", "keyword": "memory corruption"}, {"cve_id": "CVE-2005-0555", "description": "Buffer overflow in the Content Advisor in Microsoft Internet Explorer 5.01, 5.5, and 6 allows remote attackers to execute arbitrary code via a crafted Content Advisor file, aka \"Content Advisor Memory Corruption Vulnerability.\"", "severity": "Unknown", "score": 0, "published": "2005-04-12T04:00:00.000", "keyword": "memory corruption"}]}