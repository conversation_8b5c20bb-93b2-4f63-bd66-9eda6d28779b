{"os": "Darwin mac 22.6.0 Darwin Kernel Version 22.6.0: Sun Dec 17 22:18:09 PST 2023; root:xnu-8796.141.3.703.2~2/RELEASE_X86_64 x86_64\n", "gcc_version": "Apple clang version 15.0.0 (clang-1500.1.0.2.5)\nTarget: x86_64-apple-darwin22.6.0\nThread model: posix\nInstalledDir: /Library/Developer/CommandLineTools/usr/bin\n", "gpp_version": "Apple clang version 15.0.0 (clang-1500.1.0.2.5)\nTarget: x86_64-apple-darwin22.6.0\nThread model: posix\nInstalledDir: /Library/Developer/CommandLineTools/usr/bin\n", "cmake_version": null, "kernel_version": "22.6.0\n"}