1 error generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//Distribution.cpp.
/Users/<USER>/CLionProjects/predixy/src//Distribution.cpp:7:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
Found compiler error(s).
1 warning generated.
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: warning: Use of memory after it is freed [clang-analyzer-cplusplus.NewDelete]
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:13: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
            ^
/Users/<USER>/CLionProjects/predixy/src//ResponseParser.cpp:37:12: note: Assuming 'cursor' is < 'end'
    while (cursor < end && !error) {
           ^~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ResponseParser.cpp:37:12: note: Left side of '&&' is true
/Users/<USER>/CLionProjects/predixy/src//ResponseParser.cpp:37:5: note: Loop condition is true.  Entering loop body
    while (cursor < end && !error) {
    ^
/Users/<USER>/CLionProjects/predixy/src//ResponseParser.cpp:39:9: note: Control jumps to 'case Idle:'  at line 40
        switch (mState) {
        ^
/Users/<USER>/CLionProjects/predixy/src//ResponseParser.cpp:41:13: note: Calling copy assignment operator for 'SharePtr<Buffer>'
            mRes.begin().buf = buf;
            ^~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:203:9: note: Taking true branch
        if (this != &sp) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:17: note: Field 'mObj' is non-null
            if (mObj) {
                ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:13: note: Taking true branch
            if (mObj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:17: note: Assuming 'obj' is non-null
            if (obj) {
                ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:13: note: Taking true branch
            if (obj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:210:17: note: Calling 'RefCntObj::unref'
                obj->unref();
                ^~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:147:13: note: Assuming 'n' is equal to 0
        if (n == 0) {
            ^~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:147:9: note: Taking true branch
        if (n == 0) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:148:13: note: Calling 'Alloc::destroy'
            T::Allocator::destroy(static_cast<T*>(this));
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:105:13: note: Assuming 'Size' is >= 64
        if (Size < CacheSize) {
            ^~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:105:9: note: Taking false branch
        if (Size < CacheSize) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:109:13: note: 'del' is true
        if (del) {
            ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:109:9: note: Taking true branch
        if (del) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:111:13: note: Memory is released
            ::operator delete((void*)obj);
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Assuming '_lu_' is non-null
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:18: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
                 ^~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Taking true branch
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:9: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:94: note: 'del' is true
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
                                                                                             ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:65: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:94: note: '?' condition is true
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
                                                                                             ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Use of memory after it is freed
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^                                   ~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:13: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
            ^                                            ~~~~~~~~~~~
1 error generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//HashFunc.cpp.
/Users/<USER>/CLionProjects/predixy/src//HashFunc.cpp:7:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
Found compiler error(s).
20 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp.
error: too many errors emitted, stopping now [clang-diagnostic-error]
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:30:17: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                mHandler->id(), db, (int)mShareConns.size());
                ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:136:65: note: expanded from macro 'logWarn'
#define logWarn(fmt, ...)   logMacroImpl(LogLevel::Warn, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:42:19: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                  mHandler->id(), c->peer(), c->fd());
                  ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:135:67: note: expanded from macro 'logNotice'
#define logNotice(fmt, ...) logMacroImpl(LogLevel::Notice, fmt, ##__VA_ARGS__)
                                                                  ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:50:19: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                  mHandler->id(), c->peer(), c->fd());
                  ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:135:67: note: expanded from macro 'logNotice'
#define logNotice(fmt, ...) logMacroImpl(LogLevel::Notice, fmt, ##__VA_ARGS__)
                                                                  ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:67:17: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                mHandler->id(), db, (int)mPrivateConns.size());
                ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:136:65: note: expanded from macro 'logWarn'
#define logWarn(fmt, ...)   logMacroImpl(LogLevel::Warn, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:82:19: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                  mHandler->id(), c->peer(), c->fd());
                  ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:135:67: note: expanded from macro 'logNotice'
#define logNotice(fmt, ...) logMacroImpl(LogLevel::Notice, fmt, ##__VA_ARGS__)
                                                                  ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:91:19: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                  mHandler->id(), c->peer(), c->fd());
                  ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:135:67: note: expanded from macro 'logNotice'
#define logNotice(fmt, ...) logMacroImpl(LogLevel::Notice, fmt, ##__VA_ARGS__)
                                                                  ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:104:13: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
            mHandler->id(), s->peer(), s->fd());
            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:133:66: note: expanded from macro 'logDebug'
#define logDebug(fmt, ...)  logMacroImpl(LogLevel::Debug, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:110:17: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                mHandler->id(), s->peer(), s->fd(), s->db());
                ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:136:65: note: expanded from macro 'logWarn'
#define logWarn(fmt, ...)   logMacroImpl(LogLevel::Warn, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:118:17: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                mHandler->id(), c->peer(), c->fd());
                ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:136:65: note: expanded from macro 'logWarn'
#define logWarn(fmt, ...)   logMacroImpl(LogLevel::Warn, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:123:17: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                mHandler->id(), c->peer(), c->fd(), StrError());
                ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:136:65: note: expanded from macro 'logWarn'
#define logWarn(fmt, ...)   logMacroImpl(LogLevel::Warn, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:128:17: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                mHandler->id(), c->peer(), c->fd(), sp->keepalive(),StrError());
                ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:136:65: note: expanded from macro 'logWarn'
#define logWarn(fmt, ...)   logMacroImpl(LogLevel::Warn, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:131:26: error: use of undeclared identifier 'Multiplexor' [clang-diagnostic-error]
    if (!m->addSocket(c, Multiplexor::ReadEvent|Multiplexor::WriteEvent)) {
                         ^
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:131:49: error: use of undeclared identifier 'Multiplexor' [clang-diagnostic-error]
    if (!m->addSocket(c, Multiplexor::ReadEvent|Multiplexor::WriteEvent)) {
                                                ^
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:133:17: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                mHandler->id(), c->peer(), c->fd());
                ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:136:65: note: expanded from macro 'logWarn'
#define logWarn(fmt, ...)   logMacroImpl(LogLevel::Warn, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnectionPool.cpp:139:17: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                mHandler->id(), c->peer(), c->fd());
                ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:136:65: note: expanded from macro 'logWarn'
#define logWarn(fmt, ...)   logMacroImpl(LogLevel::Warn, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Handler.h:38:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* eventLoop() const
    ^
/Users/<USER>/CLionProjects/predixy/src/Handler.h:119:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* mEventLoop;
    ^
Found compiler error(s).
10 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//AcceptConnection.cpp.
/Users/<USER>/CLionProjects/predixy/src//AcceptConnection.cpp:78:21: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                    h->id(), peer(), fd(), req->id(), res->id());
                    ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:133:66: note: expanded from macro 'logDebug'
#define logDebug(fmt, ...)  logMacroImpl(LogLevel::Debug, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//AcceptConnection.cpp:86:21: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                    h->id(), peer(), fd(), req->id());
                    ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:133:66: note: expanded from macro 'logDebug'
#define logDebug(fmt, ...)  logMacroImpl(LogLevel::Debug, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//AcceptConnection.cpp:97:15: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
              h->id(), peer(), fd(), len);
              ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:133:66: note: expanded from macro 'logDebug'
#define logDebug(fmt, ...)  logMacroImpl(LogLevel::Debug, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//AcceptConnection.cpp:106:17: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                h->id(), peer(), fd(), len, StrError());
                ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:133:66: note: expanded from macro 'logDebug'
#define logDebug(fmt, ...)  logMacroImpl(LogLevel::Debug, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//AcceptConnection.cpp:123:30: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                             h->id(), peer(), fd(), req->id());
                             ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:65: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//AcceptConnection.cpp:128:29: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                            h->id(), peer(), fd(), req->id());
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:65: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Handler.h:38:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* eventLoop() const
    ^
/Users/<USER>/CLionProjects/predixy/src/Handler.h:119:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* mEventLoop;
    ^
Found compiler error(s).
18 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp.
/Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp:14:23: error: use of undeclared identifier 'Sentinel' [clang-diagnostic-error]
    ServerPoolTmpl(p, Sentinel),
                      ^
/Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp:28:22: error: no viable conversion from 'const SentinelServerPoolConf' to 'const ServerPoolConf' [clang-diagnostic-error]
    ServerPool::init(conf);
                     ^~~~
/Users/<USER>/CLionProjects/predixy/src/Conf.h:48:8: note: candidate constructor (the implicit copy constructor) not viable: cannot convert argument of incomplete type 'const SentinelServerPoolConf' to 'const ServerPoolConf &' for 1st argument
struct ServerPoolConf
       ^
/Users/<USER>/CLionProjects/predixy/src/Conf.h:48:8: note: candidate constructor (the implicit move constructor) not viable: cannot convert argument of incomplete type 'const SentinelServerPoolConf' to 'ServerPoolConf &&' for 1st argument
/Users/<USER>/CLionProjects/predixy/src/ServerPool.h:29:37: note: passing argument to parameter 'conf' here
    void init(const ServerPoolConf& conf);
                                    ^
/Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp:29:17: error: member access into incomplete type 'const SentinelServerPoolConf' [clang-diagnostic-error]
    mDist = conf.dist;
                ^
/Users/<USER>/CLionProjects/predixy/src/Predixy.h:43:8: note: forward declaration of 'SentinelServerPoolConf'
struct SentinelServerPoolConf;
       ^
/Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp:30:17: error: member access into incomplete type 'const SentinelServerPoolConf' [clang-diagnostic-error]
    mHash = conf.hash;
                ^
/Users/<USER>/CLionProjects/predixy/src/Predixy.h:43:8: note: forward declaration of 'SentinelServerPoolConf'
struct SentinelServerPoolConf;
       ^
/Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp:31:23: error: member access into incomplete type 'const SentinelServerPoolConf' [clang-diagnostic-error]
    mHashTag[0] = conf.hashTag[0];
                      ^
/Users/<USER>/CLionProjects/predixy/src/Predixy.h:43:8: note: forward declaration of 'SentinelServerPoolConf'
struct SentinelServerPoolConf;
       ^
/Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp:32:23: error: member access into incomplete type 'const SentinelServerPoolConf' [clang-diagnostic-error]
    mHashTag[1] = conf.hashTag[1];
                      ^
/Users/<USER>/CLionProjects/predixy/src/Predixy.h:43:8: note: forward declaration of 'SentinelServerPoolConf'
struct SentinelServerPoolConf;
       ^
/Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp:33:27: error: member access into incomplete type 'const SentinelServerPoolConf' [clang-diagnostic-error]
    mSentinels.resize(conf.sentinels.size());
                          ^
/Users/<USER>/CLionProjects/predixy/src/Predixy.h:43:8: note: forward declaration of 'SentinelServerPoolConf'
struct SentinelServerPoolConf;
       ^
/Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp:35:25: error: member access into incomplete type 'const SentinelServerPoolConf' [clang-diagnostic-error]
    for (auto& sc : conf.sentinels) {
                        ^
/Users/<USER>/CLionProjects/predixy/src/Predixy.h:43:8: note: forward declaration of 'SentinelServerPoolConf'
struct SentinelServerPoolConf;
       ^
/Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp:38:50: error: member access into incomplete type 'const SentinelServerPoolConf' [clang-diagnostic-error]
        s->setPassword(sc.password.empty() ? conf.sentinelPassword:sc.password);
                                                 ^
/Users/<USER>/CLionProjects/predixy/src/Predixy.h:43:8: note: forward declaration of 'SentinelServerPoolConf'
struct SentinelServerPoolConf;
       ^
/Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp:42:27: error: member access into incomplete type 'const SentinelServerPoolConf' [clang-diagnostic-error]
    mGroupPool.resize(conf.groups.size());
                          ^
/Users/<USER>/CLionProjects/predixy/src/Predixy.h:43:8: note: forward declaration of 'SentinelServerPoolConf'
struct SentinelServerPoolConf;
       ^
/Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp:44:25: error: member access into incomplete type 'const SentinelServerPoolConf' [clang-diagnostic-error]
    for (auto& gc : conf.groups) {
                        ^
/Users/<USER>/CLionProjects/predixy/src/Predixy.h:43:8: note: forward declaration of 'SentinelServerPoolConf'
struct SentinelServerPoolConf;
       ^
/Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp:49:54: error: member access into incomplete type 'const SentinelServerPoolConf' [clang-diagnostic-error]
            s->setPassword(sc.password.empty() ? conf.password : sc.password);
                                                     ^
/Users/<USER>/CLionProjects/predixy/src/Predixy.h:43:8: note: forward declaration of 'SentinelServerPoolConf'
struct SentinelServerPoolConf;
       ^
/Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp:105:50: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
    logDebug("h %d update sentinel server pool", h->id());
                                                 ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:133:66: note: expanded from macro 'logDebug'
#define logDebug(fmt, ...)  logMacroImpl(LogLevel::Debug, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//SentinelServerPool.cpp:320:25: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                        h->id(), addr.data());
                        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:135:67: note: expanded from macro 'logNotice'
#define logNotice(fmt, ...) logMacroImpl(LogLevel::Notice, fmt, ##__VA_ARGS__)
                                                                  ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Handler.h:38:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* eventLoop() const
    ^
/Users/<USER>/CLionProjects/predixy/src/Handler.h:119:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* mEventLoop;
    ^
Found compiler error(s).
1 error generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//Socket.cpp.
/Users/<USER>/CLionProjects/predixy/src//Socket.cpp:15:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
Found compiler error(s).
6 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//StandaloneServerPool.cpp.
/Users/<USER>/CLionProjects/predixy/src//StandaloneServerPool.cpp:119:52: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
    logDebug("h %d update standalone server pool", h->id());
                                                   ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:133:66: note: expanded from macro 'logDebug'
#define logDebug(fmt, ...)  logMacroImpl(LogLevel::Debug, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//StandaloneServerPool.cpp:340:25: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                        h->id(), addr.data());
                        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:135:67: note: expanded from macro 'logNotice'
#define logNotice(fmt, ...) logMacroImpl(LogLevel::Notice, fmt, ##__VA_ARGS__)
                                                                  ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Handler.h:38:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* eventLoop() const
    ^
/Users/<USER>/CLionProjects/predixy/src/Handler.h:119:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* mEventLoop;
    ^
Found compiler error(s).
2 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//DC.cpp.
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
Found compiler error(s).
1 warning generated.
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: warning: Use of memory after it is freed [clang-analyzer-cplusplus.NewDelete]
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:13: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
            ^
/Users/<USER>/CLionProjects/predixy/src//RequestParser.cpp:77:12: note: Assuming 'cursor' is < 'end'
    while (cursor < end && !error) {
           ^~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//RequestParser.cpp:77:12: note: Left side of '&&' is true
/Users/<USER>/CLionProjects/predixy/src//RequestParser.cpp:77:5: note: Loop condition is true.  Entering loop body
    while (cursor < end && !error) {
    ^
/Users/<USER>/CLionProjects/predixy/src//RequestParser.cpp:79:13: note: Assuming field 'mStatus' is equal to Normal
        if (mStatus != Normal && mByteCnt > MaxAllowInvalidByteCount) {
            ^~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//RequestParser.cpp:79:31: note: Left side of '&&' is false
        if (mStatus != Normal && mByteCnt > MaxAllowInvalidByteCount) {
                              ^
/Users/<USER>/CLionProjects/predixy/src//RequestParser.cpp:85:9: note: Control jumps to 'case KeyBody:'  at line 284
        switch (mState) {
        ^
/Users/<USER>/CLionProjects/predixy/src//RequestParser.cpp:285:17: note: Assuming field 'mArgBodyCnt' is equal to 0
            if (mArgBodyCnt == 0) {
                ^~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//RequestParser.cpp:285:13: note: Taking true branch
            if (mArgBodyCnt == 0) {
            ^
/Users/<USER>/CLionProjects/predixy/src//RequestParser.cpp:286:17: note: Calling copy assignment operator for 'SharePtr<Buffer>'
                mKey.begin().buf = buf;
                ^~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:203:9: note: Taking true branch
        if (this != &sp) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:17: note: Field 'mObj' is non-null
            if (mObj) {
                ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:13: note: Taking true branch
            if (mObj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:17: note: Assuming 'obj' is non-null
            if (obj) {
                ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:13: note: Taking true branch
            if (obj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:210:17: note: Calling 'RefCntObj::unref'
                obj->unref();
                ^~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:147:13: note: Assuming 'n' is equal to 0
        if (n == 0) {
            ^~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:147:9: note: Taking true branch
        if (n == 0) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:148:13: note: Calling 'Alloc::destroy'
            T::Allocator::destroy(static_cast<T*>(this));
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:105:13: note: Assuming 'Size' is >= 64
        if (Size < CacheSize) {
            ^~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:105:9: note: Taking false branch
        if (Size < CacheSize) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:109:13: note: 'del' is true
        if (del) {
            ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:109:9: note: Taking true branch
        if (del) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:111:13: note: Memory is released
            ::operator delete((void*)obj);
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Assuming '_lu_' is non-null
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:18: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
                 ^~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Taking true branch
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:9: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:94: note: 'del' is true
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
                                                                                             ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:65: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:94: note: '?' condition is true
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
                                                                                             ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Use of memory after it is freed
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^                                   ~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:13: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
            ^                                            ~~~~~~~~~~~
2 warnings generated.
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: warning: Use of memory after it is freed [clang-analyzer-cplusplus.NewDelete]
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:13: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
            ^
/Users/<USER>/CLionProjects/predixy/src//Request.cpp:343:18: note: 'ld' is non-null
    if (Request* ld = leader()) {
                 ^~
/Users/<USER>/CLionProjects/predixy/src//Request.cpp:343:5: note: Taking true branch
    if (Request* ld = leader()) {
    ^
/Users/<USER>/CLionProjects/predixy/src//Request.cpp:345:9: note: Control jumps to the 'default' case at line 388
        switch (mType) {
        ^
/Users/<USER>/CLionProjects/predixy/src//Request.cpp:390:13: note: Calling copy assignment operator for 'SharePtr<Response>'
            mRes = res;
            ^~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:203:9: note: Taking true branch
        if (this != &sp) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:17: note: Field 'mObj' is null
            if (mObj) {
                ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:13: note: Taking false branch
            if (mObj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:17: note: Assuming 'obj' is non-null
            if (obj) {
                ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:13: note: Taking true branch
            if (obj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:210:17: note: Calling 'RefCntObj::unref'
                obj->unref();
                ^~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:147:13: note: Assuming 'n' is equal to 0
        if (n == 0) {
            ^~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:147:9: note: Taking true branch
        if (n == 0) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:148:13: note: Calling 'Alloc::destroy'
            T::Allocator::destroy(static_cast<T*>(this));
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:105:13: note: Assuming 'Size' is >= 128
        if (Size < CacheSize) {
            ^~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:105:9: note: Taking false branch
        if (Size < CacheSize) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:109:13: note: 'del' is true
        if (del) {
            ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:109:9: note: Taking true branch
        if (del) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:111:13: note: Memory is released
            ::operator delete((void*)obj);
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Assuming '_lu_' is non-null
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:18: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
                 ^~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Taking true branch
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:9: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:94: note: 'del' is true
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
                                                                                             ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:65: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:94: note: '?' condition is true
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
                                                                                             ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Use of memory after it is freed
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^                                   ~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:13: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
            ^                                            ~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:213:17: warning: Potential leak of memory pointed to by 'obj' [clang-analyzer-cplusplus.NewDeleteLeaks]
        return *this;
                ^
/Users/<USER>/CLionProjects/predixy/src//Request.cpp:43:21: note: Calling 'Alloc::create'
    BufferPtr buf = BufferAlloc::create();
                    ^~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:56:13: note: Assuming 'Size' is <= 0
        if (Size > 0) {
            ^~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:56:9: note: Taking false branch
        if (Size > 0) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:59:13: note: 'obj' is null
        if (obj) {
            ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:59:9: note: Taking false branch
        if (obj) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:78:13: note: Assuming 'MaxMemory' is equal to 0
        if (MaxMemory == 0 || UsedMemory <= MaxMemory) {
            ^~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:78:28: note: Left side of '||' is true
        if (MaxMemory == 0 || UsedMemory <= MaxMemory) {
                           ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:79:23: note: Memory is allocated
            void* p = ::operator new(allocSize<T>(), std::nothrow);
                      ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:80:17: note: Assuming 'p' is non-null
            if (p) {
                ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:80:13: note: Taking true branch
            if (p) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:83:21: note: '_lu_' is null
                    logVerb("alloc create object with new memory %d @%p", allocSize<T>(), obj);
                    ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:18: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
                 ^~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:83:21: note: Taking false branch
                    logVerb("alloc create object with new memory %d @%p", allocSize<T>(), obj);
                    ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:9: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:83:21: note: Loop condition is false.  Exiting loop
                    logVerb("alloc create object with new memory %d @%p", allocSize<T>(), obj);
                    ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:125:5: note: expanded from macro 'logMacroImpl'
    do {                                                                      \
    ^
/Users/<USER>/CLionProjects/predixy/src//Request.cpp:43:21: note: Returned allocated memory
    BufferPtr buf = BufferAlloc::create();
                    ^~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Request.cpp:47:13: note: Assuming the condition is true
        if (buf->room() < (int)strlen(r.content)) {
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Request.cpp:47:9: note: Taking true branch
        if (buf->room() < (int)strlen(r.content)) {
        ^
/Users/<USER>/CLionProjects/predixy/src//Request.cpp:48:13: note: Calling copy assignment operator for 'SharePtr<Buffer>'
            buf = BufferAlloc::create();
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:203:9: note: Taking true branch
        if (this != &sp) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:17: note: Field 'mObj' is non-null
            if (mObj) {
                ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:13: note: Taking true branch
            if (mObj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:17: note: 'obj' is non-null
            if (obj) {
                ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:13: note: Taking true branch
            if (obj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:213:17: note: Potential leak of memory pointed to by 'obj'
        return *this;
                ^
3 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//Command.cpp.
/Users/<USER>/CLionProjects/predixy/src//Command.cpp:7:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
Found compiler error(s).
14 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//ConnectConnection.cpp.
/Users/<USER>/CLionProjects/predixy/src//ConnectConnection.cpp:32:17: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                h->id(), peer(), fd());
                ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:134:65: note: expanded from macro 'logInfo'
#define logInfo(fmt, ...)   logMacroImpl(LogLevel::Info, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnection.cpp:71:13: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
            h->id(), peer(), fd(), len);
            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:133:66: note: expanded from macro 'logDebug'
#define logDebug(fmt, ...)  logMacroImpl(LogLevel::Debug, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnection.cpp:80:17: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                h->id(), peer(), fd(), len, StrError());
                ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:136:65: note: expanded from macro 'logWarn'
#define logWarn(fmt, ...)   logMacroImpl(LogLevel::Warn, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnection.cpp:99:29: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                            h->id(), peer(), fd(), req->id());
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:65: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnection.cpp:103:29: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                            h->id(), peer(), fd(), req->id());
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:135:67: note: expanded from macro 'logNotice'
#define logNotice(fmt, ...) logMacroImpl(LogLevel::Notice, fmt, ##__VA_ARGS__)
                                                                  ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnection.cpp:123:21: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                    h->id(), peer(), fd(), n);
                    ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:65: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnection.cpp:133:25: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                        h->id(), peer(), fd(), StrError());
                        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:136:65: note: expanded from macro 'logWarn'
#define logWarn(fmt, ...)   logMacroImpl(LogLevel::Warn, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnection.cpp:178:29: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                            h->id(), peer(), fd());
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:137:66: note: expanded from macro 'logError'
#define logError(fmt, ...)  logMacroImpl(LogLevel::Error, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnection.cpp:212:17: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                h->id(), peer(), fd(), res->id(), req->id());
                ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:133:66: note: expanded from macro 'logDebug'
#define logDebug(fmt, ...)  logMacroImpl(LogLevel::Debug, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//ConnectConnection.cpp:217:17: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                h->id(), peer(), fd());
                ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:135:67: note: expanded from macro 'logNotice'
#define logNotice(fmt, ...) logMacroImpl(LogLevel::Notice, fmt, ##__VA_ARGS__)
                                                                  ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Handler.h:38:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* eventLoop() const
    ^
/Users/<USER>/CLionProjects/predixy/src/Handler.h:119:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* mEventLoop;
    ^
Found compiler error(s).
3 warnings generated.
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:193:9: warning: Initialized va_list 'ap' is leaked [clang-analyzer-valist.Unterminated]
        return vfset(buf, fmt, ap);
        ^
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:191:5: note: Initialized va_list
    va_start(ap, fmt);
    ^
/usr/local/Cellar/llvm@15/15.0.7/lib/clang/15.0.7/include/stdarg.h:17:29: note: expanded from macro 'va_start'
#define va_start(ap, param) __builtin_va_start(ap, param)
                            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:193:9: note: Initialized va_list 'ap' is leaked
        return vfset(buf, fmt, ap);
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: warning: Use of memory after it is freed [clang-analyzer-cplusplus.NewDelete]
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:13: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
            ^
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:329:5: note: Calling copy assignment operator for 'SharePtr<Buffer>'
    mCur.buf = buf;
    ^~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:203:9: note: Taking true branch
        if (this != &sp) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:17: note: Field 'mObj' is null
            if (mObj) {
                ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:13: note: Taking false branch
            if (mObj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:17: note: Assuming 'obj' is non-null
            if (obj) {
                ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:13: note: Taking true branch
            if (obj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:210:17: note: Calling 'RefCntObj::unref'
                obj->unref();
                ^~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:147:13: note: Assuming 'n' is equal to 0
        if (n == 0) {
            ^~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:147:9: note: Taking true branch
        if (n == 0) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:148:13: note: Calling 'Alloc::destroy'
            T::Allocator::destroy(static_cast<T*>(this));
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:105:13: note: Assuming 'Size' is >= 64
        if (Size < CacheSize) {
            ^~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:105:9: note: Taking false branch
        if (Size < CacheSize) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:109:13: note: 'del' is true
        if (del) {
            ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:109:9: note: Taking true branch
        if (del) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:111:13: note: Memory is released
            ::operator delete((void*)obj);
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Assuming '_lu_' is non-null
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:18: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
                 ^~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Taking true branch
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:9: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:94: note: 'del' is true
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
                                                                                             ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:65: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:94: note: '?' condition is true
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
                                                                                             ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Use of memory after it is freed
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^                                   ~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:13: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
            ^                                            ~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:193:13: warning: Use of memory after it is freed [clang-analyzer-cplusplus.NewDelete]
            mObj->unref();
            ^
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:276:9: note: Assuming 'cnt' is > 0
    if (cnt > 0) {
        ^~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:276:5: note: Taking true branch
    if (cnt > 0) {
    ^
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:277:16: note: 'cnt' is > 0
        while (cnt > 0 && mBegin.buf) {
               ^~~
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:277:16: note: Left side of '&&' is true
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:277:9: note: Loop condition is true.  Entering loop body
        while (cnt > 0 && mBegin.buf) {
        ^
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:279:24: note: '?' condition is false
            int len = (buf == mEnd.buf ? mEnd.pos : buf->length()) - mBegin.pos;
                       ^
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:280:17: note: Assuming 'len' is <= 'cnt'
            if (len <= cnt) {
                ^~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:280:13: note: Taking true branch
            if (len <= cnt) {
            ^
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:281:17: note: Taking false branch
                if (buf == mEnd.buf) {
                ^
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:288:17: note: Calling copy assignment operator for 'SharePtr<Buffer>'
                mBegin.buf = buf->next();
                ^~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:203:9: note: Taking true branch
        if (this != &sp) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:17: note: Field 'mObj' is null
            if (mObj) {
                ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:13: note: Taking false branch
            if (mObj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:17: note: 'obj' is non-null
            if (obj) {
                ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:13: note: Taking true branch
            if (obj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:210:17: note: Calling 'RefCntObj::unref'
                obj->unref();
                ^~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:147:13: note: Assuming 'n' is equal to 0
        if (n == 0) {
            ^~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:147:9: note: Taking true branch
        if (n == 0) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:148:13: note: Calling 'Alloc::destroy'
            T::Allocator::destroy(static_cast<T*>(this));
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:105:13: note: Assuming 'Size' is >= 64
        if (Size < CacheSize) {
            ^~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:105:9: note: Taking false branch
        if (Size < CacheSize) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:109:13: note: 'del' is true
        if (del) {
            ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:109:9: note: Taking true branch
        if (del) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:111:13: note: Memory is released
            ::operator delete((void*)obj);
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: '_lu_' is null
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:18: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
                 ^~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Taking false branch
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:9: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Loop condition is false.  Exiting loop
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:125:5: note: expanded from macro 'logMacroImpl'
    do {                                                                      \
    ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:148:13: note: Returning; memory was released via 1st parameter
            T::Allocator::destroy(static_cast<T*>(this));
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:210:17: note: Returning; memory was released
                obj->unref();
                ^~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:288:17: note: Returning; memory was released
                mBegin.buf = buf->next();
                ^~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:290:17: note: Taking false branch
                if (mCur.buf == buf) {
                ^
/Users/<USER>/CLionProjects/predixy/src//Buffer.cpp:300:9: note: Calling '~SharePtr'
        }
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:192:13: note: Field 'mObj' is non-null
        if (mObj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:192:9: note: Taking true branch
        if (mObj) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:193:13: note: Use of memory after it is freed
            mObj->unref();
            ^~~~
4 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//Proxy.cpp.
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Handler.h:38:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* eventLoop() const
    ^
/Users/<USER>/CLionProjects/predixy/src/Handler.h:119:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* mEventLoop;
    ^
Found compiler error(s).
1 error generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//LogFileSink.cpp.
/Users/<USER>/CLionProjects/predixy/src//LogFileSink.cpp:8:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
Found compiler error(s).
1 error generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//Enums.cpp.
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
Found compiler error(s).
4 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//ServerGroup.cpp.
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Handler.h:38:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* eventLoop() const
    ^
/Users/<USER>/CLionProjects/predixy/src/Handler.h:119:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* mEventLoop;
    ^
Found compiler error(s).
2 warnings generated.
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: warning: Use of memory after it is freed [clang-analyzer-cplusplus.NewDelete]
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:13: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
            ^
/Users/<USER>/CLionProjects/predixy/src//Response.cpp:42:21: note: Calling 'Alloc::create'
    BufferPtr buf = BufferAlloc::create();
                    ^~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:56:13: note: Assuming 'Size' is > 0
        if (Size > 0) {
            ^~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:56:9: note: Taking true branch
        if (Size > 0) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:59:13: note: Assuming 'obj' is non-null
        if (obj) {
            ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:59:9: note: Taking true branch
        if (obj) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:74:13: note: '_lu_' is null
            logVerb("alloc create object with old memory %d @%p", allocSize<T>(), obj);
            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:18: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
                 ^~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:74:13: note: Taking false branch
            logVerb("alloc create object with old memory %d @%p", allocSize<T>(), obj);
            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:9: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:74:13: note: Loop condition is false.  Exiting loop
            logVerb("alloc create object with old memory %d @%p", allocSize<T>(), obj);
            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:125:5: note: expanded from macro 'logMacroImpl'
    do {                                                                      \
    ^
/Users/<USER>/CLionProjects/predixy/src//Response.cpp:42:21: note: Returning from 'Alloc::create'
    BufferPtr buf = BufferAlloc::create();
                    ^~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Response.cpp:46:13: note: Assuming the condition is false
        if (buf->room() < (int)strlen(r.content)) {
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Response.cpp:46:9: note: Taking false branch
        if (buf->room() < (int)strlen(r.content)) {
        ^
/Users/<USER>/CLionProjects/predixy/src//Response.cpp:49:9: note: Calling copy assignment operator for 'SharePtr<Buffer>'
        buf = res->mRes.set(buf, r.content);
        ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:203:9: note: Taking true branch
        if (this != &sp) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:17: note: Field 'mObj' is null
            if (mObj) {
                ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:13: note: Taking false branch
            if (mObj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:17: note: 'obj' is non-null
            if (obj) {
                ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:13: note: Taking true branch
            if (obj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:210:17: note: Calling 'RefCntObj::unref'
                obj->unref();
                ^~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:147:13: note: Assuming 'n' is equal to 0
        if (n == 0) {
            ^~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:147:9: note: Taking true branch
        if (n == 0) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:148:13: note: Calling 'Alloc::destroy'
            T::Allocator::destroy(static_cast<T*>(this));
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:105:13: note: Assuming 'Size' is >= 64
        if (Size < CacheSize) {
            ^~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:105:9: note: Taking false branch
        if (Size < CacheSize) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:109:13: note: 'del' is true
        if (del) {
            ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:109:9: note: Taking true branch
        if (del) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:111:13: note: Memory is released
            ::operator delete((void*)obj);
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Assuming '_lu_' is non-null
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:18: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
                 ^~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Taking true branch
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:9: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:94: note: 'del' is true
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
                                                                                             ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:65: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:94: note: '?' condition is true
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
                                                                                             ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Use of memory after it is freed
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^                                   ~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:13: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
            ^                                            ~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:213:17: warning: Potential leak of memory pointed to by 'obj' [clang-analyzer-cplusplus.NewDeleteLeaks]
        return *this;
                ^
/Users/<USER>/CLionProjects/predixy/src//Response.cpp:42:21: note: Calling 'Alloc::create'
    BufferPtr buf = BufferAlloc::create();
                    ^~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:56:13: note: Assuming 'Size' is <= 0
        if (Size > 0) {
            ^~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:56:9: note: Taking false branch
        if (Size > 0) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:59:13: note: 'obj' is null
        if (obj) {
            ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:59:9: note: Taking false branch
        if (obj) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:78:13: note: Assuming 'MaxMemory' is equal to 0
        if (MaxMemory == 0 || UsedMemory <= MaxMemory) {
            ^~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:78:28: note: Left side of '||' is true
        if (MaxMemory == 0 || UsedMemory <= MaxMemory) {
                           ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:79:23: note: Memory is allocated
            void* p = ::operator new(allocSize<T>(), std::nothrow);
                      ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:80:17: note: Assuming 'p' is non-null
            if (p) {
                ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:80:13: note: Taking true branch
            if (p) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:83:21: note: '_lu_' is null
                    logVerb("alloc create object with new memory %d @%p", allocSize<T>(), obj);
                    ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:18: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
                 ^~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:83:21: note: Taking false branch
                    logVerb("alloc create object with new memory %d @%p", allocSize<T>(), obj);
                    ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:9: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:83:21: note: Loop condition is false.  Exiting loop
                    logVerb("alloc create object with new memory %d @%p", allocSize<T>(), obj);
                    ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:125:5: note: expanded from macro 'logMacroImpl'
    do {                                                                      \
    ^
/Users/<USER>/CLionProjects/predixy/src//Response.cpp:42:21: note: Returned allocated memory
    BufferPtr buf = BufferAlloc::create();
                    ^~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Response.cpp:46:13: note: Assuming the condition is true
        if (buf->room() < (int)strlen(r.content)) {
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Response.cpp:46:9: note: Taking true branch
        if (buf->room() < (int)strlen(r.content)) {
        ^
/Users/<USER>/CLionProjects/predixy/src//Response.cpp:47:13: note: Calling copy assignment operator for 'SharePtr<Buffer>'
            buf = BufferAlloc::create();
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:203:9: note: Taking true branch
        if (this != &sp) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:17: note: Field 'mObj' is non-null
            if (mObj) {
                ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:13: note: Taking true branch
            if (mObj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:17: note: 'obj' is non-null
            if (obj) {
                ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:13: note: Taking true branch
            if (obj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:213:17: note: Potential leak of memory pointed to by 'obj'
        return *this;
                ^
2 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//Conf.cpp.
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
Found compiler error(s).
5 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//ClusterServerPool.cpp.
/Users/<USER>/CLionProjects/predixy/src//ClusterServerPool.cpp:84:48: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
    logDebug("h %d update redis cluster pool", h->id());
                                               ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:133:66: note: expanded from macro 'logDebug'
#define logDebug(fmt, ...)  logMacroImpl(LogLevel::Debug, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Handler.h:38:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* eventLoop() const
    ^
/Users/<USER>/CLionProjects/predixy/src/Handler.h:119:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* mEventLoop;
    ^
Found compiler error(s).
1 error generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//ListenSocket.cpp.
/Users/<USER>/CLionProjects/predixy/src//ListenSocket.cpp:7:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
Found compiler error(s).
1 error generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//Logger.cpp.
/Users/<USER>/CLionProjects/predixy/src//Logger.cpp:9:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
Found compiler error(s).
2 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//LatencyMonitor.cpp.
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
Found compiler error(s).
1 warning generated.
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: warning: Use of memory after it is freed [clang-analyzer-cplusplus.NewDelete]
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:13: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
            ^
/Users/<USER>/CLionProjects/predixy/src//Connection.cpp:20:9: note: Assuming 'allowNew' is true
    if (allowNew && mBuf) {
        ^~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Connection.cpp:20:9: note: Left side of '&&' is true
/Users/<USER>/CLionProjects/predixy/src//Connection.cpp:20:21: note: Assuming the condition is true
    if (allowNew && mBuf) {
                    ^~~~
/Users/<USER>/CLionProjects/predixy/src//Connection.cpp:20:5: note: Taking true branch
    if (allowNew && mBuf) {
    ^
/Users/<USER>/CLionProjects/predixy/src//Connection.cpp:21:13: note: Assuming 'MaxBufListNodeNum' is <= field 'mBufCnt'
        if (mBufCnt >= Const::MaxBufListNodeNum) {
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Connection.cpp:21:9: note: Taking true branch
        if (mBufCnt >= Const::MaxBufListNodeNum) {
        ^
/Users/<USER>/CLionProjects/predixy/src//Connection.cpp:22:13: note: Calling copy assignment operator for 'SharePtr<Buffer>'
            mBuf = nullptr;
            ^~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:203:9: note: Taking true branch
        if (this != &sp) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:17: note: Field 'mObj' is null
            if (mObj) {
                ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:206:13: note: Taking false branch
            if (mObj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:17: note: 'obj' is non-null
            if (obj) {
                ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:209:13: note: Taking true branch
            if (obj) {
            ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:210:17: note: Calling 'RefCntObj::unref'
                obj->unref();
                ^~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:147:13: note: Assuming 'n' is equal to 0
        if (n == 0) {
            ^~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:147:9: note: Taking true branch
        if (n == 0) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:148:13: note: Calling 'Alloc::destroy'
            T::Allocator::destroy(static_cast<T*>(this));
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:105:13: note: Assuming 'Size' is >= 64
        if (Size < CacheSize) {
            ^~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:105:9: note: Taking false branch
        if (Size < CacheSize) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:109:13: note: 'del' is true
        if (del) {
            ^~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:109:9: note: Taking true branch
        if (del) {
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:111:13: note: Memory is released
            ::operator delete((void*)obj);
            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Assuming '_lu_' is non-null
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:18: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
                 ^~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Taking true branch
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:126:9: note: expanded from macro 'logMacroImpl'
        if (auto _lu_ = Logger::gInst->log(lvl)) {                    \
        ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:94: note: 'del' is true
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
                                                                                             ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:65: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                                                                ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:94: note: '?' condition is true
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
                                                                                             ^
/Users/<USER>/CLionProjects/predixy/src/Alloc.h:113:9: note: Use of memory after it is freed
        logVerb("alloc destroy object with size %d @%p delete %s", (int)allocSize<T>(), obj, del ? "true" : "false");
        ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:132:29: note: expanded from macro 'logVerb'
#define logVerb(fmt, ...)   logMacroImpl(LogLevel::Verb, fmt, ##__VA_ARGS__)
                            ^                                   ~~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:13: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
            ^                                            ~~~~~~~~~~~
20 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//Handler.cpp.
error: too many errors emitted, stopping now [clang-diagnostic-error]
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:27:20: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    mEventLoop(new Multiplexor()),
                   ^
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:30:32: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
    mRandSeed(time(nullptr) * (id() + 1))
                               ^~
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:34:18: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                 id(), StrError());
                 ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:137:66: note: expanded from macro 'logError'
#define logError(fmt, ...)  logMacroImpl(LogLevel::Error, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:36:18: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                 id(), StrError());
                 ^
/Users/<USER>/CLionProjects/predixy/src/Exception.h:61:53: note: expanded from macro 'Throw'
#define Throw(T, ...) throw T(__FILE__, __LINE__, ##__VA_ARGS__)
                                                    ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:76:37: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
    logNotice("handler %d stopped", id());
                                    ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:135:67: note: expanded from macro 'logNotice'
#define logNotice(fmt, ...) logMacroImpl(LogLevel::Notice, fmt, ##__VA_ARGS__)
                                                                  ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:95:17: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                id(), excp.what());
                ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:137:66: note: expanded from macro 'logError'
#define logError(fmt, ...)  logMacroImpl(LogLevel::Error, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:108:23: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                      id(), p->server()->addr().data(), excp.what());
                      ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:137:66: note: expanded from macro 'logError'
#define logError(fmt, ...)  logMacroImpl(LogLevel::Error, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:125:33: error: use of undeclared identifier 'Multiplexor' [clang-diagnostic-error]
                addPostEvent(s, Multiplexor::ErrorEvent);
                                ^
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:151:51: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
        logError("h %d unexpect socket %d ev %d", id(), s->fd(), evts);
                                                  ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:137:66: note: expanded from macro 'logError'
#define logError(fmt, ...)  logMacroImpl(LogLevel::Error, fmt, ##__VA_ARGS__)
                                                                 ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:193:34: error: use of undeclared identifier 'Multiplexor' [clang-diagnostic-error]
        if (c->good() && (evts & Multiplexor::WriteEvent)) {
                                 ^
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:198:51: error: use of undeclared identifier 'Multiplexor' [clang-diagnostic-error]
                    ret = mEventLoop->delEvent(c, Multiplexor::WriteEvent);
                                                  ^
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:203:51: error: use of undeclared identifier 'Multiplexor' [clang-diagnostic-error]
                    ret = mEventLoop->addEvent(c, Multiplexor::WriteEvent);
                                                  ^
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:210:34: error: use of undeclared identifier 'Multiplexor' [clang-diagnostic-error]
        if ((!c->good()||(evts & Multiplexor::ErrorEvent)) && c->fd() >= 0){
                                 ^
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:212:21: error: cannot initialize object parameter of type 'const ID<Handler>' with an expression of type 'Handler' [clang-diagnostic-error]
                    id(), c->peer(), c->fd(), c->status(), c->statusStr());
                    ^
/Users/<USER>/CLionProjects/predixy/src/Logger.h:135:67: note: expanded from macro 'logNotice'
#define logNotice(fmt, ...) logMacroImpl(LogLevel::Notice, fmt, ##__VA_ARGS__)
                                                                  ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src/Logger.h:127:58: note: expanded from macro 'logMacroImpl'
            _lu_->format(lvl, __FILE__, __LINE__, fmt, ##__VA_ARGS__);\
                                                         ^~~~~~~~~~~
/Users/<USER>/CLionProjects/predixy/src//Handler.cpp:217:33: error: use of undeclared identifier 'Multiplexor' [clang-diagnostic-error]
                addPostEvent(s, Multiplexor::ErrorEvent);
                                ^
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Handler.h:38:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* eventLoop() const
    ^
/Users/<USER>/CLionProjects/predixy/src/Handler.h:119:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* mEventLoop;
    ^
Found compiler error(s).
1 error generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//EpollMultiplexor.cpp.
/Users/<USER>/CLionProjects/predixy/src/EpollMultiplexor.h:11:10: error: 'sys/epoll.h' file not found [clang-diagnostic-error]
#include <sys/epoll.h>
         ^~~~~~~~~~~~~
Found compiler error(s).
4 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//main.cpp.
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Handler.h:38:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* eventLoop() const
    ^
/Users/<USER>/CLionProjects/predixy/src/Handler.h:119:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* mEventLoop;
    ^
Found compiler error(s).
3 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//ClusterNodesParser.cpp.
/Users/<USER>/CLionProjects/predixy/src//ClusterNodesParser.cpp:7:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
Found compiler error(s).
2 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//ConfParser.cpp.
/Users/<USER>/CLionProjects/predixy/src//ConfParser.cpp:9:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
Found compiler error(s).
4 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//ServerPool.cpp.
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Handler.h:38:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* eventLoop() const
    ^
/Users/<USER>/CLionProjects/predixy/src/Handler.h:119:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* mEventLoop;
    ^
Found compiler error(s).
2 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//Auth.cpp.
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
Found compiler error(s).
4 errors generated.
Error while processing /Users/<USER>/CLionProjects/predixy/src//Server.cpp.
/Users/<USER>/CLionProjects/predixy/src/ConfParser.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Enums.h:10:10: error: 'PString.h' file not found with <angled> include; use "quotes" instead [clang-diagnostic-error]
#include <PString.h>
         ^~~~~~~~~~~
         "PString.h"
/Users/<USER>/CLionProjects/predixy/src/Handler.h:38:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* eventLoop() const
    ^
/Users/<USER>/CLionProjects/predixy/src/Handler.h:119:5: error: unknown type name 'Multiplexor' [clang-diagnostic-error]
    Multiplexor* mEventLoop;
    ^
Found compiler error(s).
