{"scan_time": "2025-06-08T18:33:05.495075", "project_analysis": {"dangerous_functions": {"send": {"risk_level": "LOW", "issue_type": "网络输出", "locations": [{"file": "src/AcceptConnection.cpp", "line": 238, "code": "bool AcceptConnection::send(Handler* h, Request* req, Response* res)"}, {"file": "src/Request.cpp", "line": 303, "code": "bool Request::send(Socket* s)"}, {"file": "src/Response.cpp", "line": 145, "code": "bool Response::send(Socket* s)"}, {"file": "src/Handler.cpp", "line": 534, "code": "s->send(this, req);"}, {"file": "src/Handler.cpp", "line": 739, "code": "s->send(this, req);"}, {"file": "src/Handler.cpp", "line": 837, "code": "if (c->send(this, req, res)) {"}]}, "memcpy": {"risk_level": "MEDIUM", "issue_type": "内存操作", "locations": [{"file": "src/Socket.cpp", "line": 101, "code": "memcpy(res, &sun, *len);"}, {"file": "src/Socket.cpp", "line": 130, "code": "memcpy(res, dst->ai_addr, *len);"}, {"file": "src/Buffer.cpp", "line": 22, "code": "memcpy(mDat, oth.mDat, mLen);"}, {"file": "src/Buffer.cpp", "line": 42, "code": "memcpy(mDat, oth.mDat, mLen);"}, {"file": "src/Buffer.cpp", "line": 60, "code": "memcpy(buf->tail(), dat, len);"}, {"file": "src/Buffer.cpp", "line": 65, "code": "memcpy(buf->tail(), dat, n);"}, {"file": "src/Buffer.cpp", "line": 349, "code": "memcpy(dat, buf->data() + pos, len > num ? num : len);"}, {"file": "src/LogFileSink.cpp", "line": 146, "code": "memcpy(mFilePath, path, len);"}]}}, "network_patterns": {"socket_creation": [{"file": "src/Socket.cpp", "line": 32, "code": "mFd(Socket::socket(domain, type, protocol)),"}, {"file": "src/Socket.cpp", "line": 80, "code": "int Socket::socket(int domain, int type, int protocol)"}, {"file": "src/Socket.cpp", "line": 82, "code": "int fd = ::socket(domain, type, protocol);"}, {"file": "src/ConnectSocket.cpp", "line": 19, "code": "int fd = Socket::socket(in->sa_family, type, protocol);"}, {"file": "src/ConnectSocket.cpp", "line": 57, "code": "int fd = Socket::socket(in->sa_family, mType, mProtocol);"}, {"file": "src/ListenSocket.cpp", "line": 19, "code": "int fd = Socket::socket(in->sa_family, type, protocol);"}], "bind_operations": [{"file": "src/Proxy.cpp", "line": 105, "code": "ListenSocket* s = new ListenSocket(mConf->bind(), SOCK_STREAM);"}, {"file": "src/Proxy.cpp", "line": 112, "code": "logNotice(\"predixy listen in %s\", mConf->bind());"}, {"file": "src/Proxy.cpp", "line": 172, "code": "if (*mConf->bind() == '/') {"}, {"file": "src/Proxy.cpp", "line": 173, "code": "unlink(mConf->bind());"}, {"file": "src/ListenSocket.cpp", "line": 22, "code": "int ret = ::bind(fd, in, len);"}, {"file": "src/Handler.cpp", "line": 943, "code": "buf = buf->fappend(\"Bind:%s\\n\", mProxy->conf()->bind());"}, {"file": "src/Handler.cpp", "line": 1238, "code": "Append(\"Bind\", \"%s\", conf->bind());"}], "listen_operations": [{"file": "src/Proxy.cpp", "line": 110, "code": "s->listen();"}, {"file": "src/ListenSocket.cpp", "line": 32, "code": "void ListenSocket::listen(int backlog)"}, {"file": "src/ListenSocket.cpp", "line": 34, "code": "int ret = ::listen(fd(), backlog);"}], "accept_operations": [{"file": "src/ListenSocket.cpp", "line": 40, "code": "int ListenSocket::accept(sockaddr* addr, socklen_t* len)"}, {"file": "src/ListenSocket.cpp", "line": 43, "code": "int c = ::accept(fd(), addr, len);"}, {"file": "src/Handler.cpp", "line": 295, "code": "int fd = s->accept((sockaddr*)&addr, &len);"}], "connect_operations": [{"file": "src/ConnectConnectionPool.cpp", "line": 137, "code": "if (!c->connect()) {"}, {"file": "src/ConnectSocket.cpp", "line": 24, "code": "bool ConnectSocket::connect()"}, {"file": "src/ConnectSocket.cpp", "line": 32, "code": "int ret = ::connect(fd(), (const sockaddr*)&mPeerAddr, mPeerAddrLen);"}], "recv_operations": [], "send_operations": [{"file": "src/AcceptConnection.cpp", "line": 238, "code": "bool AcceptConnection::send(Handler* h, Request* req, Response* res)"}, {"file": "src/Request.cpp", "line": 303, "code": "bool Request::send(Socket* s)"}, {"file": "src/Response.cpp", "line": 145, "code": "bool Response::send(Socket* s)"}, {"file": "src/Handler.cpp", "line": 534, "code": "s->send(this, req);"}, {"file": "src/Handler.cpp", "line": 739, "code": "s->send(this, req);"}, {"file": "src/Handler.cpp", "line": 837, "code": "if (c->send(this, req, res)) {"}]}, "memory_operations": {"malloc_without_check": [], "free_without_null": [], "buffer_operations": [{"file": "src/Socket.cpp", "line": 93, "code": "memset(&sun, 0, sizeof(sun));", "function": "memset"}, {"file": "src/Socket.cpp", "line": 95, "code": "strncpy(sun.sun_path, addr, sizeof(sun.sun_path));", "function": "strncpy"}, {"file": "src/Socket.cpp", "line": 101, "code": "memcpy(res, &sun, *len);", "function": "memcpy"}, {"file": "src/Socket.cpp", "line": 113, "code": "memset(&hints, 0, sizeof(hints));", "function": "memset"}, {"file": "src/Socket.cpp", "line": 130, "code": "memcpy(res, dst->ai_addr, *len);", "function": "memcpy"}, {"file": "src/Buffer.cpp", "line": 22, "code": "memcpy(mDat, oth.mDat, mLen);", "function": "memcpy"}, {"file": "src/Buffer.cpp", "line": 42, "code": "memcpy(mDat, oth.mDat, mLen);", "function": "memcpy"}, {"file": "src/Buffer.cpp", "line": 60, "code": "memcpy(buf->tail(), dat, len);", "function": "memcpy"}, {"file": "src/Buffer.cpp", "line": 65, "code": "memcpy(buf->tail(), dat, n);", "function": "memcpy"}, {"file": "src/Buffer.cpp", "line": 349, "code": "memcpy(dat, buf->data() + pos, len > num ? num : len);", "function": "memcpy"}, {"file": "src/LogFileSink.cpp", "line": 146, "code": "memcpy(mFilePath, path, len);", "function": "memcpy"}, {"file": "src/ListenSocket.cpp", "line": 14, "code": "strncpy(mAddr, addr, sizeof(mAddr));", "function": "strncpy"}, {"file": "src/EpollMultiplexor.cpp", "line": 30, "code": "memset(&event, 0, sizeof(event));", "function": "memset"}, {"file": "src/EpollMultiplexor.cpp", "line": 43, "code": "memset(&event, 0, sizeof(event));", "function": "memset"}, {"file": "src/EpollMultiplexor.cpp", "line": 51, "code": "memset(&event, 0, sizeof(event));", "function": "memset"}, {"file": "src/EpollMultiplexor.cpp", "line": 75, "code": "memset(&event, 0, sizeof(event));", "function": "memset"}], "pointer_arithmetic": []}, "dependencies": {"system_includes": ["vector", "unistd.h", "poll.h", "algorithm", "map", "string", "atomic", "mutex", "execinfo.h", "sys/types.h", "sys/event.h", "sys/time.h", "limits.h", "string.h", "strings.h", "set", "bitset", "deque", "sys/uio.h", "PString.h", "fstream", "chrono", "stdio.h", "errno.h", "sys/epoll.h", "stdint.h", "unordered_map", "stdarg.h", "condition_variable", "thread", "exception", "sys/socket.h", "sys/un.h", "netinet/in.h", "netinet/ip.h", "netinet/tcp.h", "fcntl.h", "arpa/inet.h", "netdb.h", "stdlib.h", "iostream", "signal.h", "time.h", "sstream", "ctype.h", "sys/resource.h", "memory"], "third_party_libs": ["Socket.h", "List.h", "Common.h", "Buffer.h", "ConnectConnection.h", "Server.h", "Handler.h", "Request.h", "Predixy.h", "Stats.h", "LatencyMonitor.h", "ResponseParser.h", "Multiplexor.h", "Util.h", "PString.h", "Conf.h", "ServerPool.h", "Logger.h", "Distribution.h", "ConfParser.h", "Auth.h", "Command.h", "Enums.h", "KqueueMultiplexor.h", "EpollMultiplexor.h", "PollMultiplexor.h", "ConnectSocket.h", "Connection.h", "Sync.h", "AcceptSocket.h", "Transaction.h", "Subscribe.h", "RequestParser.h", "Response.h", "Exception.h", "ID.h", "HashFunc.h", "IOVec.h", "Deque.h", "Timer.h", "Alloc.h", "Reply.h", "DC.h", "ClusterServerPool.h", "StandaloneServerPool.h", "AcceptConnection.h", "ConnectConnectionPool.h", "Proxy.h", "ServerGroup.h", "SentinelServerPool.h", "ListenSocket.h", "Backtrace.h", "LogFileSink.h", "ClusterNodesParser.h"], "compiler_version": null, "build_system": "cmake"}}, "code_vulnerabilities": [{"cve_id": "CVE-2003-1307", "description": "The mod_php module for the Apache HTTP Server allows local users with write access to PHP scripts to send signals to the server's process group and use the server's file descriptors, as demonstrated by sending a STOP signal, then intercepting incoming connections on the server's TCP port.  NOTE: the PHP developer has disputed this vulnerability, saying \"The opened file descriptors are opened by Apache. It is the job of Apache to protect them ... Not a bug in PHP.", "severity": "Unknown", "score": 0, "published": "2003-12-31T05:00:00.000", "vector": "Unknown", "relevance_reason": "项目使用了网络编程", "affected_code": {"socket_creation": [{"file": "src/Socket.cpp", "line": 32, "code": "mFd(Socket::socket(domain, type, protocol)),"}, {"file": "src/Socket.cpp", "line": 80, "code": "int Socket::socket(int domain, int type, int protocol)"}, {"file": "src/Socket.cpp", "line": 82, "code": "int fd = ::socket(domain, type, protocol);"}, {"file": "src/ConnectSocket.cpp", "line": 19, "code": "int fd = Socket::socket(in->sa_family, type, protocol);"}, {"file": "src/ConnectSocket.cpp", "line": 57, "code": "int fd = Socket::socket(in->sa_family, mType, mProtocol);"}, {"file": "src/ListenSocket.cpp", "line": 19, "code": "int fd = Socket::socket(in->sa_family, type, protocol);"}], "bind_operations": [{"file": "src/Proxy.cpp", "line": 105, "code": "ListenSocket* s = new ListenSocket(mConf->bind(), SOCK_STREAM);"}, {"file": "src/Proxy.cpp", "line": 112, "code": "logNotice(\"predixy listen in %s\", mConf->bind());"}, {"file": "src/Proxy.cpp", "line": 172, "code": "if (*mConf->bind() == '/') {"}, {"file": "src/Proxy.cpp", "line": 173, "code": "unlink(mConf->bind());"}, {"file": "src/ListenSocket.cpp", "line": 22, "code": "int ret = ::bind(fd, in, len);"}, {"file": "src/Handler.cpp", "line": 943, "code": "buf = buf->fappend(\"Bind:%s\\n\", mProxy->conf()->bind());"}, {"file": "src/Handler.cpp", "line": 1238, "code": "Append(\"Bind\", \"%s\", conf->bind());"}], "listen_operations": [{"file": "src/Proxy.cpp", "line": 110, "code": "s->listen();"}, {"file": "src/ListenSocket.cpp", "line": 32, "code": "void ListenSocket::listen(int backlog)"}, {"file": "src/ListenSocket.cpp", "line": 34, "code": "int ret = ::listen(fd(), backlog);"}], "accept_operations": [{"file": "src/ListenSocket.cpp", "line": 40, "code": "int ListenSocket::accept(sockaddr* addr, socklen_t* len)"}, {"file": "src/ListenSocket.cpp", "line": 43, "code": "int c = ::accept(fd(), addr, len);"}, {"file": "src/Handler.cpp", "line": 295, "code": "int fd = s->accept((sockaddr*)&addr, &len);"}], "connect_operations": [{"file": "src/ConnectConnectionPool.cpp", "line": 137, "code": "if (!c->connect()) {"}, {"file": "src/ConnectSocket.cpp", "line": 24, "code": "bool ConnectSocket::connect()"}, {"file": "src/ConnectSocket.cpp", "line": 32, "code": "int ret = ::connect(fd(), (const sockaddr*)&mPeerAddr, mPeerAddrLen);"}], "recv_operations": [], "send_operations": [{"file": "src/AcceptConnection.cpp", "line": 238, "code": "bool AcceptConnection::send(Handler* h, Request* req, Response* res)"}, {"file": "src/Request.cpp", "line": 303, "code": "bool Request::send(Socket* s)"}, {"file": "src/Response.cpp", "line": 145, "code": "bool Response::send(Socket* s)"}, {"file": "src/Handler.cpp", "line": 534, "code": "s->send(this, req);"}, {"file": "src/Handler.cpp", "line": 739, "code": "s->send(this, req);"}, {"file": "src/Handler.cpp", "line": 837, "code": "if (c->send(this, req, res)) {"}]}}, {"cve_id": "CVE-2004-1080", "description": "The WINS service (wins.exe) on Microsoft Windows NT Server 4.0, Windows 2000 Server, and Windows Server 2003 allows remote attackers to write to arbitrary memory locations and possibly execute arbitrary code via a modified memory pointer in a WINS replication packet to TCP port 42, aka the \"Association Context Vulnerability.\"", "severity": "Unknown", "score": 0, "published": "2005-01-10T05:00:00.000", "vector": "Unknown", "relevance_reason": "项目使用了网络编程", "affected_code": {"socket_creation": [{"file": "src/Socket.cpp", "line": 32, "code": "mFd(Socket::socket(domain, type, protocol)),"}, {"file": "src/Socket.cpp", "line": 80, "code": "int Socket::socket(int domain, int type, int protocol)"}, {"file": "src/Socket.cpp", "line": 82, "code": "int fd = ::socket(domain, type, protocol);"}, {"file": "src/ConnectSocket.cpp", "line": 19, "code": "int fd = Socket::socket(in->sa_family, type, protocol);"}, {"file": "src/ConnectSocket.cpp", "line": 57, "code": "int fd = Socket::socket(in->sa_family, mType, mProtocol);"}, {"file": "src/ListenSocket.cpp", "line": 19, "code": "int fd = Socket::socket(in->sa_family, type, protocol);"}], "bind_operations": [{"file": "src/Proxy.cpp", "line": 105, "code": "ListenSocket* s = new ListenSocket(mConf->bind(), SOCK_STREAM);"}, {"file": "src/Proxy.cpp", "line": 112, "code": "logNotice(\"predixy listen in %s\", mConf->bind());"}, {"file": "src/Proxy.cpp", "line": 172, "code": "if (*mConf->bind() == '/') {"}, {"file": "src/Proxy.cpp", "line": 173, "code": "unlink(mConf->bind());"}, {"file": "src/ListenSocket.cpp", "line": 22, "code": "int ret = ::bind(fd, in, len);"}, {"file": "src/Handler.cpp", "line": 943, "code": "buf = buf->fappend(\"Bind:%s\\n\", mProxy->conf()->bind());"}, {"file": "src/Handler.cpp", "line": 1238, "code": "Append(\"Bind\", \"%s\", conf->bind());"}], "listen_operations": [{"file": "src/Proxy.cpp", "line": 110, "code": "s->listen();"}, {"file": "src/ListenSocket.cpp", "line": 32, "code": "void ListenSocket::listen(int backlog)"}, {"file": "src/ListenSocket.cpp", "line": 34, "code": "int ret = ::listen(fd(), backlog);"}], "accept_operations": [{"file": "src/ListenSocket.cpp", "line": 40, "code": "int ListenSocket::accept(sockaddr* addr, socklen_t* len)"}, {"file": "src/ListenSocket.cpp", "line": 43, "code": "int c = ::accept(fd(), addr, len);"}, {"file": "src/Handler.cpp", "line": 295, "code": "int fd = s->accept((sockaddr*)&addr, &len);"}], "connect_operations": [{"file": "src/ConnectConnectionPool.cpp", "line": 137, "code": "if (!c->connect()) {"}, {"file": "src/ConnectSocket.cpp", "line": 24, "code": "bool ConnectSocket::connect()"}, {"file": "src/ConnectSocket.cpp", "line": 32, "code": "int ret = ::connect(fd(), (const sockaddr*)&mPeerAddr, mPeerAddrLen);"}], "recv_operations": [], "send_operations": [{"file": "src/AcceptConnection.cpp", "line": 238, "code": "bool AcceptConnection::send(Handler* h, Request* req, Response* res)"}, {"file": "src/Request.cpp", "line": 303, "code": "bool Request::send(Socket* s)"}, {"file": "src/Response.cpp", "line": 145, "code": "bool Response::send(Socket* s)"}, {"file": "src/Handler.cpp", "line": 534, "code": "s->send(this, req);"}, {"file": "src/Handler.cpp", "line": 739, "code": "s->send(this, req);"}, {"file": "src/Handler.cpp", "line": 837, "code": "if (c->send(this, req, res)) {"}]}}, {"cve_id": "CVE-2005-0688", "description": "Windows Server 2003 and XP SP2, with Windows Firewall turned off, allows remote attackers to cause a denial of service (CPU consumption) via a TCP packet with the SYN flag set and the same destination and source address and port, aka a reoccurrence of the \"Land\" vulnerability (CVE-1999-0016).", "severity": "Unknown", "score": 0, "published": "2005-03-05T05:00:00.000", "vector": "Unknown", "relevance_reason": "项目使用了网络编程", "affected_code": {"socket_creation": [{"file": "src/Socket.cpp", "line": 32, "code": "mFd(Socket::socket(domain, type, protocol)),"}, {"file": "src/Socket.cpp", "line": 80, "code": "int Socket::socket(int domain, int type, int protocol)"}, {"file": "src/Socket.cpp", "line": 82, "code": "int fd = ::socket(domain, type, protocol);"}, {"file": "src/ConnectSocket.cpp", "line": 19, "code": "int fd = Socket::socket(in->sa_family, type, protocol);"}, {"file": "src/ConnectSocket.cpp", "line": 57, "code": "int fd = Socket::socket(in->sa_family, mType, mProtocol);"}, {"file": "src/ListenSocket.cpp", "line": 19, "code": "int fd = Socket::socket(in->sa_family, type, protocol);"}], "bind_operations": [{"file": "src/Proxy.cpp", "line": 105, "code": "ListenSocket* s = new ListenSocket(mConf->bind(), SOCK_STREAM);"}, {"file": "src/Proxy.cpp", "line": 112, "code": "logNotice(\"predixy listen in %s\", mConf->bind());"}, {"file": "src/Proxy.cpp", "line": 172, "code": "if (*mConf->bind() == '/') {"}, {"file": "src/Proxy.cpp", "line": 173, "code": "unlink(mConf->bind());"}, {"file": "src/ListenSocket.cpp", "line": 22, "code": "int ret = ::bind(fd, in, len);"}, {"file": "src/Handler.cpp", "line": 943, "code": "buf = buf->fappend(\"Bind:%s\\n\", mProxy->conf()->bind());"}, {"file": "src/Handler.cpp", "line": 1238, "code": "Append(\"Bind\", \"%s\", conf->bind());"}], "listen_operations": [{"file": "src/Proxy.cpp", "line": 110, "code": "s->listen();"}, {"file": "src/ListenSocket.cpp", "line": 32, "code": "void ListenSocket::listen(int backlog)"}, {"file": "src/ListenSocket.cpp", "line": 34, "code": "int ret = ::listen(fd(), backlog);"}], "accept_operations": [{"file": "src/ListenSocket.cpp", "line": 40, "code": "int ListenSocket::accept(sockaddr* addr, socklen_t* len)"}, {"file": "src/ListenSocket.cpp", "line": 43, "code": "int c = ::accept(fd(), addr, len);"}, {"file": "src/Handler.cpp", "line": 295, "code": "int fd = s->accept((sockaddr*)&addr, &len);"}], "connect_operations": [{"file": "src/ConnectConnectionPool.cpp", "line": 137, "code": "if (!c->connect()) {"}, {"file": "src/ConnectSocket.cpp", "line": 24, "code": "bool ConnectSocket::connect()"}, {"file": "src/ConnectSocket.cpp", "line": 32, "code": "int ret = ::connect(fd(), (const sockaddr*)&mPeerAddr, mPeerAddrLen);"}], "recv_operations": [], "send_operations": [{"file": "src/AcceptConnection.cpp", "line": 238, "code": "bool AcceptConnection::send(Handler* h, Request* req, Response* res)"}, {"file": "src/Request.cpp", "line": 303, "code": "bool Request::send(Socket* s)"}, {"file": "src/Response.cpp", "line": 145, "code": "bool Response::send(Socket* s)"}, {"file": "src/Handler.cpp", "line": 534, "code": "s->send(this, req);"}, {"file": "src/Handler.cpp", "line": 739, "code": "s->send(this, req);"}, {"file": "src/Handler.cpp", "line": 837, "code": "if (c->send(this, req, res)) {"}]}}], "dependency_risks": [], "actionable_recommendations": []}