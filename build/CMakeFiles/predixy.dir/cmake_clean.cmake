file(REMOVE_RECURSE
  "CMakeFiles/predixy.dir/src/AcceptConnection.cpp.o"
  "CMakeFiles/predixy.dir/src/AcceptSocket.cpp.o"
  "CMakeFiles/predixy.dir/src/Alloc.cpp.o"
  "CMakeFiles/predixy.dir/src/Auth.cpp.o"
  "CMakeFiles/predixy.dir/src/Buffer.cpp.o"
  "CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.o"
  "CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.o"
  "CMakeFiles/predixy.dir/src/Command.cpp.o"
  "CMakeFiles/predixy.dir/src/Conf.cpp.o"
  "CMakeFiles/predixy.dir/src/ConfParser.cpp.o"
  "CMakeFiles/predixy.dir/src/ConnectConnection.cpp.o"
  "CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.o"
  "CMakeFiles/predixy.dir/src/ConnectSocket.cpp.o"
  "CMakeFiles/predixy.dir/src/Connection.cpp.o"
  "CMakeFiles/predixy.dir/src/Crc16.cpp.o"
  "CMakeFiles/predixy.dir/src/DC.cpp.o"
  "CMakeFiles/predixy.dir/src/Distribution.cpp.o"
  "CMakeFiles/predixy.dir/src/Enums.cpp.o"
  "CMakeFiles/predixy.dir/src/Handler.cpp.o"
  "CMakeFiles/predixy.dir/src/HashFunc.cpp.o"
  "CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.o"
  "CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.o"
  "CMakeFiles/predixy.dir/src/ListenSocket.cpp.o"
  "CMakeFiles/predixy.dir/src/LogFileSink.cpp.o"
  "CMakeFiles/predixy.dir/src/Logger.cpp.o"
  "CMakeFiles/predixy.dir/src/Proxy.cpp.o"
  "CMakeFiles/predixy.dir/src/Reply.cpp.o"
  "CMakeFiles/predixy.dir/src/Request.cpp.o"
  "CMakeFiles/predixy.dir/src/RequestParser.cpp.o"
  "CMakeFiles/predixy.dir/src/Response.cpp.o"
  "CMakeFiles/predixy.dir/src/ResponseParser.cpp.o"
  "CMakeFiles/predixy.dir/src/Server.cpp.o"
  "CMakeFiles/predixy.dir/src/ServerGroup.cpp.o"
  "CMakeFiles/predixy.dir/src/ServerPool.cpp.o"
  "CMakeFiles/predixy.dir/src/Socket.cpp.o"
  "CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.o"
  "CMakeFiles/predixy.dir/src/Subscribe.cpp.o"
  "CMakeFiles/predixy.dir/src/Timer.cpp.o"
  "CMakeFiles/predixy.dir/src/main.cpp.o"
  "predixy"
  "predixy.pdb"
)

# Per-language clean rules from dependency scanning.
foreach(lang CXX)
  include(CMakeFiles/predixy.dir/cmake_clean_${lang}.cmake OPTIONAL)
endforeach()
