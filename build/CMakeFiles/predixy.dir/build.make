# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.18

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Disable VCS-based implicit rules.
% : %,v


# Disable VCS-based implicit rules.
% : RCS/%


# Disable VCS-based implicit rules.
% : RCS/%,v


# Disable VCS-based implicit rules.
% : SCCS/s.%


# Disable VCS-based implicit rules.
% : s.%


.SUFFIXES: .hpux_make_needs_suffix_list


# Command-line flag to silence nested $(MAKE).
$(VERBOSE)MAKESILENT = -s

#Suppress display of executed commands.
$(VERBOSE).SILENT:

# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/local/Cellar/cmake/3.18.2/bin/cmake

# The command to remove a file.
RM = /usr/local/Cellar/cmake/3.18.2/bin/cmake -E rm -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /Users/<USER>/CLionProjects/predixy

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /Users/<USER>/CLionProjects/predixy/build

# Include any dependencies generated for this target.
include CMakeFiles/predixy.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/predixy.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/predixy.dir/flags.make

CMakeFiles/predixy.dir/src/AcceptConnection.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/AcceptConnection.cpp.o: ../src/AcceptConnection.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building CXX object CMakeFiles/predixy.dir/src/AcceptConnection.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/AcceptConnection.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/AcceptConnection.cpp

CMakeFiles/predixy.dir/src/AcceptConnection.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/AcceptConnection.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/AcceptConnection.cpp > CMakeFiles/predixy.dir/src/AcceptConnection.cpp.i

CMakeFiles/predixy.dir/src/AcceptConnection.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/AcceptConnection.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/AcceptConnection.cpp -o CMakeFiles/predixy.dir/src/AcceptConnection.cpp.s

CMakeFiles/predixy.dir/src/AcceptSocket.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/AcceptSocket.cpp.o: ../src/AcceptSocket.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building CXX object CMakeFiles/predixy.dir/src/AcceptSocket.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/AcceptSocket.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/AcceptSocket.cpp

CMakeFiles/predixy.dir/src/AcceptSocket.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/AcceptSocket.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/AcceptSocket.cpp > CMakeFiles/predixy.dir/src/AcceptSocket.cpp.i

CMakeFiles/predixy.dir/src/AcceptSocket.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/AcceptSocket.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/AcceptSocket.cpp -o CMakeFiles/predixy.dir/src/AcceptSocket.cpp.s

CMakeFiles/predixy.dir/src/Alloc.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Alloc.cpp.o: ../src/Alloc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building CXX object CMakeFiles/predixy.dir/src/Alloc.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Alloc.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Alloc.cpp

CMakeFiles/predixy.dir/src/Alloc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Alloc.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Alloc.cpp > CMakeFiles/predixy.dir/src/Alloc.cpp.i

CMakeFiles/predixy.dir/src/Alloc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Alloc.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Alloc.cpp -o CMakeFiles/predixy.dir/src/Alloc.cpp.s

CMakeFiles/predixy.dir/src/Auth.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Auth.cpp.o: ../src/Auth.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Building CXX object CMakeFiles/predixy.dir/src/Auth.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Auth.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Auth.cpp

CMakeFiles/predixy.dir/src/Auth.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Auth.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Auth.cpp > CMakeFiles/predixy.dir/src/Auth.cpp.i

CMakeFiles/predixy.dir/src/Auth.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Auth.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Auth.cpp -o CMakeFiles/predixy.dir/src/Auth.cpp.s

CMakeFiles/predixy.dir/src/Buffer.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Buffer.cpp.o: ../src/Buffer.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_5) "Building CXX object CMakeFiles/predixy.dir/src/Buffer.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Buffer.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Buffer.cpp

CMakeFiles/predixy.dir/src/Buffer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Buffer.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Buffer.cpp > CMakeFiles/predixy.dir/src/Buffer.cpp.i

CMakeFiles/predixy.dir/src/Buffer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Buffer.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Buffer.cpp -o CMakeFiles/predixy.dir/src/Buffer.cpp.s

CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.o: ../src/ClusterNodesParser.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_6) "Building CXX object CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/ClusterNodesParser.cpp

CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/ClusterNodesParser.cpp > CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.i

CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/ClusterNodesParser.cpp -o CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.s

CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.o: ../src/ClusterServerPool.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_7) "Building CXX object CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/ClusterServerPool.cpp

CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/ClusterServerPool.cpp > CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.i

CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/ClusterServerPool.cpp -o CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.s

CMakeFiles/predixy.dir/src/Command.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Command.cpp.o: ../src/Command.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_8) "Building CXX object CMakeFiles/predixy.dir/src/Command.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Command.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Command.cpp

CMakeFiles/predixy.dir/src/Command.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Command.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Command.cpp > CMakeFiles/predixy.dir/src/Command.cpp.i

CMakeFiles/predixy.dir/src/Command.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Command.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Command.cpp -o CMakeFiles/predixy.dir/src/Command.cpp.s

CMakeFiles/predixy.dir/src/Conf.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Conf.cpp.o: ../src/Conf.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_9) "Building CXX object CMakeFiles/predixy.dir/src/Conf.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Conf.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Conf.cpp

CMakeFiles/predixy.dir/src/Conf.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Conf.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Conf.cpp > CMakeFiles/predixy.dir/src/Conf.cpp.i

CMakeFiles/predixy.dir/src/Conf.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Conf.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Conf.cpp -o CMakeFiles/predixy.dir/src/Conf.cpp.s

CMakeFiles/predixy.dir/src/ConfParser.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/ConfParser.cpp.o: ../src/ConfParser.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_10) "Building CXX object CMakeFiles/predixy.dir/src/ConfParser.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/ConfParser.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/ConfParser.cpp

CMakeFiles/predixy.dir/src/ConfParser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/ConfParser.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/ConfParser.cpp > CMakeFiles/predixy.dir/src/ConfParser.cpp.i

CMakeFiles/predixy.dir/src/ConfParser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/ConfParser.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/ConfParser.cpp -o CMakeFiles/predixy.dir/src/ConfParser.cpp.s

CMakeFiles/predixy.dir/src/ConnectConnection.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/ConnectConnection.cpp.o: ../src/ConnectConnection.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_11) "Building CXX object CMakeFiles/predixy.dir/src/ConnectConnection.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/ConnectConnection.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/ConnectConnection.cpp

CMakeFiles/predixy.dir/src/ConnectConnection.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/ConnectConnection.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/ConnectConnection.cpp > CMakeFiles/predixy.dir/src/ConnectConnection.cpp.i

CMakeFiles/predixy.dir/src/ConnectConnection.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/ConnectConnection.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/ConnectConnection.cpp -o CMakeFiles/predixy.dir/src/ConnectConnection.cpp.s

CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.o: ../src/ConnectConnectionPool.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_12) "Building CXX object CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/ConnectConnectionPool.cpp

CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/ConnectConnectionPool.cpp > CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.i

CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/ConnectConnectionPool.cpp -o CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.s

CMakeFiles/predixy.dir/src/Connection.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Connection.cpp.o: ../src/Connection.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_13) "Building CXX object CMakeFiles/predixy.dir/src/Connection.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Connection.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Connection.cpp

CMakeFiles/predixy.dir/src/Connection.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Connection.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Connection.cpp > CMakeFiles/predixy.dir/src/Connection.cpp.i

CMakeFiles/predixy.dir/src/Connection.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Connection.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Connection.cpp -o CMakeFiles/predixy.dir/src/Connection.cpp.s

CMakeFiles/predixy.dir/src/ConnectSocket.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/ConnectSocket.cpp.o: ../src/ConnectSocket.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_14) "Building CXX object CMakeFiles/predixy.dir/src/ConnectSocket.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/ConnectSocket.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/ConnectSocket.cpp

CMakeFiles/predixy.dir/src/ConnectSocket.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/ConnectSocket.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/ConnectSocket.cpp > CMakeFiles/predixy.dir/src/ConnectSocket.cpp.i

CMakeFiles/predixy.dir/src/ConnectSocket.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/ConnectSocket.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/ConnectSocket.cpp -o CMakeFiles/predixy.dir/src/ConnectSocket.cpp.s

CMakeFiles/predixy.dir/src/Crc16.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Crc16.cpp.o: ../src/Crc16.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_15) "Building CXX object CMakeFiles/predixy.dir/src/Crc16.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Crc16.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Crc16.cpp

CMakeFiles/predixy.dir/src/Crc16.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Crc16.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Crc16.cpp > CMakeFiles/predixy.dir/src/Crc16.cpp.i

CMakeFiles/predixy.dir/src/Crc16.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Crc16.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Crc16.cpp -o CMakeFiles/predixy.dir/src/Crc16.cpp.s

CMakeFiles/predixy.dir/src/DC.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/DC.cpp.o: ../src/DC.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_16) "Building CXX object CMakeFiles/predixy.dir/src/DC.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/DC.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/DC.cpp

CMakeFiles/predixy.dir/src/DC.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/DC.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/DC.cpp > CMakeFiles/predixy.dir/src/DC.cpp.i

CMakeFiles/predixy.dir/src/DC.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/DC.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/DC.cpp -o CMakeFiles/predixy.dir/src/DC.cpp.s

CMakeFiles/predixy.dir/src/Distribution.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Distribution.cpp.o: ../src/Distribution.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_17) "Building CXX object CMakeFiles/predixy.dir/src/Distribution.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Distribution.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Distribution.cpp

CMakeFiles/predixy.dir/src/Distribution.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Distribution.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Distribution.cpp > CMakeFiles/predixy.dir/src/Distribution.cpp.i

CMakeFiles/predixy.dir/src/Distribution.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Distribution.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Distribution.cpp -o CMakeFiles/predixy.dir/src/Distribution.cpp.s

CMakeFiles/predixy.dir/src/Enums.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Enums.cpp.o: ../src/Enums.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_18) "Building CXX object CMakeFiles/predixy.dir/src/Enums.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Enums.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Enums.cpp

CMakeFiles/predixy.dir/src/Enums.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Enums.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Enums.cpp > CMakeFiles/predixy.dir/src/Enums.cpp.i

CMakeFiles/predixy.dir/src/Enums.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Enums.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Enums.cpp -o CMakeFiles/predixy.dir/src/Enums.cpp.s

CMakeFiles/predixy.dir/src/Handler.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Handler.cpp.o: ../src/Handler.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_19) "Building CXX object CMakeFiles/predixy.dir/src/Handler.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Handler.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Handler.cpp

CMakeFiles/predixy.dir/src/Handler.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Handler.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Handler.cpp > CMakeFiles/predixy.dir/src/Handler.cpp.i

CMakeFiles/predixy.dir/src/Handler.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Handler.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Handler.cpp -o CMakeFiles/predixy.dir/src/Handler.cpp.s

CMakeFiles/predixy.dir/src/HashFunc.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/HashFunc.cpp.o: ../src/HashFunc.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_20) "Building CXX object CMakeFiles/predixy.dir/src/HashFunc.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/HashFunc.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/HashFunc.cpp

CMakeFiles/predixy.dir/src/HashFunc.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/HashFunc.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/HashFunc.cpp > CMakeFiles/predixy.dir/src/HashFunc.cpp.i

CMakeFiles/predixy.dir/src/HashFunc.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/HashFunc.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/HashFunc.cpp -o CMakeFiles/predixy.dir/src/HashFunc.cpp.s

CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.o: ../src/KqueueMultiplexor.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_21) "Building CXX object CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/KqueueMultiplexor.cpp

CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/KqueueMultiplexor.cpp > CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.i

CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/KqueueMultiplexor.cpp -o CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.s

CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.o: ../src/LatencyMonitor.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_22) "Building CXX object CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/LatencyMonitor.cpp

CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/LatencyMonitor.cpp > CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.i

CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/LatencyMonitor.cpp -o CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.s

CMakeFiles/predixy.dir/src/ListenSocket.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/ListenSocket.cpp.o: ../src/ListenSocket.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_23) "Building CXX object CMakeFiles/predixy.dir/src/ListenSocket.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/ListenSocket.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/ListenSocket.cpp

CMakeFiles/predixy.dir/src/ListenSocket.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/ListenSocket.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/ListenSocket.cpp > CMakeFiles/predixy.dir/src/ListenSocket.cpp.i

CMakeFiles/predixy.dir/src/ListenSocket.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/ListenSocket.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/ListenSocket.cpp -o CMakeFiles/predixy.dir/src/ListenSocket.cpp.s

CMakeFiles/predixy.dir/src/LogFileSink.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/LogFileSink.cpp.o: ../src/LogFileSink.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_24) "Building CXX object CMakeFiles/predixy.dir/src/LogFileSink.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/LogFileSink.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/LogFileSink.cpp

CMakeFiles/predixy.dir/src/LogFileSink.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/LogFileSink.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/LogFileSink.cpp > CMakeFiles/predixy.dir/src/LogFileSink.cpp.i

CMakeFiles/predixy.dir/src/LogFileSink.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/LogFileSink.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/LogFileSink.cpp -o CMakeFiles/predixy.dir/src/LogFileSink.cpp.s

CMakeFiles/predixy.dir/src/Logger.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Logger.cpp.o: ../src/Logger.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_25) "Building CXX object CMakeFiles/predixy.dir/src/Logger.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Logger.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Logger.cpp

CMakeFiles/predixy.dir/src/Logger.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Logger.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Logger.cpp > CMakeFiles/predixy.dir/src/Logger.cpp.i

CMakeFiles/predixy.dir/src/Logger.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Logger.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Logger.cpp -o CMakeFiles/predixy.dir/src/Logger.cpp.s

CMakeFiles/predixy.dir/src/main.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/main.cpp.o: ../src/main.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_26) "Building CXX object CMakeFiles/predixy.dir/src/main.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/main.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/main.cpp

CMakeFiles/predixy.dir/src/main.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/main.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/main.cpp > CMakeFiles/predixy.dir/src/main.cpp.i

CMakeFiles/predixy.dir/src/main.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/main.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/main.cpp -o CMakeFiles/predixy.dir/src/main.cpp.s

CMakeFiles/predixy.dir/src/Proxy.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Proxy.cpp.o: ../src/Proxy.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_27) "Building CXX object CMakeFiles/predixy.dir/src/Proxy.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Proxy.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Proxy.cpp

CMakeFiles/predixy.dir/src/Proxy.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Proxy.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Proxy.cpp > CMakeFiles/predixy.dir/src/Proxy.cpp.i

CMakeFiles/predixy.dir/src/Proxy.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Proxy.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Proxy.cpp -o CMakeFiles/predixy.dir/src/Proxy.cpp.s

CMakeFiles/predixy.dir/src/Reply.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Reply.cpp.o: ../src/Reply.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_28) "Building CXX object CMakeFiles/predixy.dir/src/Reply.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Reply.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Reply.cpp

CMakeFiles/predixy.dir/src/Reply.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Reply.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Reply.cpp > CMakeFiles/predixy.dir/src/Reply.cpp.i

CMakeFiles/predixy.dir/src/Reply.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Reply.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Reply.cpp -o CMakeFiles/predixy.dir/src/Reply.cpp.s

CMakeFiles/predixy.dir/src/Request.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Request.cpp.o: ../src/Request.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_29) "Building CXX object CMakeFiles/predixy.dir/src/Request.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Request.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Request.cpp

CMakeFiles/predixy.dir/src/Request.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Request.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Request.cpp > CMakeFiles/predixy.dir/src/Request.cpp.i

CMakeFiles/predixy.dir/src/Request.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Request.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Request.cpp -o CMakeFiles/predixy.dir/src/Request.cpp.s

CMakeFiles/predixy.dir/src/RequestParser.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/RequestParser.cpp.o: ../src/RequestParser.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_30) "Building CXX object CMakeFiles/predixy.dir/src/RequestParser.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/RequestParser.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/RequestParser.cpp

CMakeFiles/predixy.dir/src/RequestParser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/RequestParser.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/RequestParser.cpp > CMakeFiles/predixy.dir/src/RequestParser.cpp.i

CMakeFiles/predixy.dir/src/RequestParser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/RequestParser.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/RequestParser.cpp -o CMakeFiles/predixy.dir/src/RequestParser.cpp.s

CMakeFiles/predixy.dir/src/Response.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Response.cpp.o: ../src/Response.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_31) "Building CXX object CMakeFiles/predixy.dir/src/Response.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Response.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Response.cpp

CMakeFiles/predixy.dir/src/Response.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Response.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Response.cpp > CMakeFiles/predixy.dir/src/Response.cpp.i

CMakeFiles/predixy.dir/src/Response.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Response.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Response.cpp -o CMakeFiles/predixy.dir/src/Response.cpp.s

CMakeFiles/predixy.dir/src/ResponseParser.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/ResponseParser.cpp.o: ../src/ResponseParser.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_32) "Building CXX object CMakeFiles/predixy.dir/src/ResponseParser.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/ResponseParser.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/ResponseParser.cpp

CMakeFiles/predixy.dir/src/ResponseParser.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/ResponseParser.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/ResponseParser.cpp > CMakeFiles/predixy.dir/src/ResponseParser.cpp.i

CMakeFiles/predixy.dir/src/ResponseParser.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/ResponseParser.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/ResponseParser.cpp -o CMakeFiles/predixy.dir/src/ResponseParser.cpp.s

CMakeFiles/predixy.dir/src/Server.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Server.cpp.o: ../src/Server.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_33) "Building CXX object CMakeFiles/predixy.dir/src/Server.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Server.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Server.cpp

CMakeFiles/predixy.dir/src/Server.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Server.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Server.cpp > CMakeFiles/predixy.dir/src/Server.cpp.i

CMakeFiles/predixy.dir/src/Server.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Server.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Server.cpp -o CMakeFiles/predixy.dir/src/Server.cpp.s

CMakeFiles/predixy.dir/src/ServerGroup.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/ServerGroup.cpp.o: ../src/ServerGroup.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_34) "Building CXX object CMakeFiles/predixy.dir/src/ServerGroup.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/ServerGroup.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/ServerGroup.cpp

CMakeFiles/predixy.dir/src/ServerGroup.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/ServerGroup.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/ServerGroup.cpp > CMakeFiles/predixy.dir/src/ServerGroup.cpp.i

CMakeFiles/predixy.dir/src/ServerGroup.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/ServerGroup.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/ServerGroup.cpp -o CMakeFiles/predixy.dir/src/ServerGroup.cpp.s

CMakeFiles/predixy.dir/src/ServerPool.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/ServerPool.cpp.o: ../src/ServerPool.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_35) "Building CXX object CMakeFiles/predixy.dir/src/ServerPool.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/ServerPool.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/ServerPool.cpp

CMakeFiles/predixy.dir/src/ServerPool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/ServerPool.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/ServerPool.cpp > CMakeFiles/predixy.dir/src/ServerPool.cpp.i

CMakeFiles/predixy.dir/src/ServerPool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/ServerPool.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/ServerPool.cpp -o CMakeFiles/predixy.dir/src/ServerPool.cpp.s

CMakeFiles/predixy.dir/src/Socket.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Socket.cpp.o: ../src/Socket.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_36) "Building CXX object CMakeFiles/predixy.dir/src/Socket.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Socket.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Socket.cpp

CMakeFiles/predixy.dir/src/Socket.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Socket.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Socket.cpp > CMakeFiles/predixy.dir/src/Socket.cpp.i

CMakeFiles/predixy.dir/src/Socket.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Socket.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Socket.cpp -o CMakeFiles/predixy.dir/src/Socket.cpp.s

CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.o: ../src/StandaloneServerPool.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_37) "Building CXX object CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/StandaloneServerPool.cpp

CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/StandaloneServerPool.cpp > CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.i

CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/StandaloneServerPool.cpp -o CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.s

CMakeFiles/predixy.dir/src/Subscribe.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Subscribe.cpp.o: ../src/Subscribe.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_38) "Building CXX object CMakeFiles/predixy.dir/src/Subscribe.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Subscribe.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Subscribe.cpp

CMakeFiles/predixy.dir/src/Subscribe.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Subscribe.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Subscribe.cpp > CMakeFiles/predixy.dir/src/Subscribe.cpp.i

CMakeFiles/predixy.dir/src/Subscribe.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Subscribe.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Subscribe.cpp -o CMakeFiles/predixy.dir/src/Subscribe.cpp.s

CMakeFiles/predixy.dir/src/Timer.cpp.o: CMakeFiles/predixy.dir/flags.make
CMakeFiles/predixy.dir/src/Timer.cpp.o: ../src/Timer.cpp
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_39) "Building CXX object CMakeFiles/predixy.dir/src/Timer.cpp.o"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -o CMakeFiles/predixy.dir/src/Timer.cpp.o -c /Users/<USER>/CLionProjects/predixy/src/Timer.cpp

CMakeFiles/predixy.dir/src/Timer.cpp.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing CXX source to CMakeFiles/predixy.dir/src/Timer.cpp.i"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -E /Users/<USER>/CLionProjects/predixy/src/Timer.cpp > CMakeFiles/predixy.dir/src/Timer.cpp.i

CMakeFiles/predixy.dir/src/Timer.cpp.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling CXX source to assembly CMakeFiles/predixy.dir/src/Timer.cpp.s"
	/Library/Developer/CommandLineTools/usr/bin/c++ $(CXX_DEFINES) $(CXX_INCLUDES) $(CXX_FLAGS) -S /Users/<USER>/CLionProjects/predixy/src/Timer.cpp -o CMakeFiles/predixy.dir/src/Timer.cpp.s

# Object files for target predixy
predixy_OBJECTS = \
"CMakeFiles/predixy.dir/src/AcceptConnection.cpp.o" \
"CMakeFiles/predixy.dir/src/AcceptSocket.cpp.o" \
"CMakeFiles/predixy.dir/src/Alloc.cpp.o" \
"CMakeFiles/predixy.dir/src/Auth.cpp.o" \
"CMakeFiles/predixy.dir/src/Buffer.cpp.o" \
"CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.o" \
"CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.o" \
"CMakeFiles/predixy.dir/src/Command.cpp.o" \
"CMakeFiles/predixy.dir/src/Conf.cpp.o" \
"CMakeFiles/predixy.dir/src/ConfParser.cpp.o" \
"CMakeFiles/predixy.dir/src/ConnectConnection.cpp.o" \
"CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.o" \
"CMakeFiles/predixy.dir/src/Connection.cpp.o" \
"CMakeFiles/predixy.dir/src/ConnectSocket.cpp.o" \
"CMakeFiles/predixy.dir/src/Crc16.cpp.o" \
"CMakeFiles/predixy.dir/src/DC.cpp.o" \
"CMakeFiles/predixy.dir/src/Distribution.cpp.o" \
"CMakeFiles/predixy.dir/src/Enums.cpp.o" \
"CMakeFiles/predixy.dir/src/Handler.cpp.o" \
"CMakeFiles/predixy.dir/src/HashFunc.cpp.o" \
"CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.o" \
"CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.o" \
"CMakeFiles/predixy.dir/src/ListenSocket.cpp.o" \
"CMakeFiles/predixy.dir/src/LogFileSink.cpp.o" \
"CMakeFiles/predixy.dir/src/Logger.cpp.o" \
"CMakeFiles/predixy.dir/src/main.cpp.o" \
"CMakeFiles/predixy.dir/src/Proxy.cpp.o" \
"CMakeFiles/predixy.dir/src/Reply.cpp.o" \
"CMakeFiles/predixy.dir/src/Request.cpp.o" \
"CMakeFiles/predixy.dir/src/RequestParser.cpp.o" \
"CMakeFiles/predixy.dir/src/Response.cpp.o" \
"CMakeFiles/predixy.dir/src/ResponseParser.cpp.o" \
"CMakeFiles/predixy.dir/src/Server.cpp.o" \
"CMakeFiles/predixy.dir/src/ServerGroup.cpp.o" \
"CMakeFiles/predixy.dir/src/ServerPool.cpp.o" \
"CMakeFiles/predixy.dir/src/Socket.cpp.o" \
"CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.o" \
"CMakeFiles/predixy.dir/src/Subscribe.cpp.o" \
"CMakeFiles/predixy.dir/src/Timer.cpp.o"

# External object files for target predixy
predixy_EXTERNAL_OBJECTS =

predixy: CMakeFiles/predixy.dir/src/AcceptConnection.cpp.o
predixy: CMakeFiles/predixy.dir/src/AcceptSocket.cpp.o
predixy: CMakeFiles/predixy.dir/src/Alloc.cpp.o
predixy: CMakeFiles/predixy.dir/src/Auth.cpp.o
predixy: CMakeFiles/predixy.dir/src/Buffer.cpp.o
predixy: CMakeFiles/predixy.dir/src/ClusterNodesParser.cpp.o
predixy: CMakeFiles/predixy.dir/src/ClusterServerPool.cpp.o
predixy: CMakeFiles/predixy.dir/src/Command.cpp.o
predixy: CMakeFiles/predixy.dir/src/Conf.cpp.o
predixy: CMakeFiles/predixy.dir/src/ConfParser.cpp.o
predixy: CMakeFiles/predixy.dir/src/ConnectConnection.cpp.o
predixy: CMakeFiles/predixy.dir/src/ConnectConnectionPool.cpp.o
predixy: CMakeFiles/predixy.dir/src/Connection.cpp.o
predixy: CMakeFiles/predixy.dir/src/ConnectSocket.cpp.o
predixy: CMakeFiles/predixy.dir/src/Crc16.cpp.o
predixy: CMakeFiles/predixy.dir/src/DC.cpp.o
predixy: CMakeFiles/predixy.dir/src/Distribution.cpp.o
predixy: CMakeFiles/predixy.dir/src/Enums.cpp.o
predixy: CMakeFiles/predixy.dir/src/Handler.cpp.o
predixy: CMakeFiles/predixy.dir/src/HashFunc.cpp.o
predixy: CMakeFiles/predixy.dir/src/KqueueMultiplexor.cpp.o
predixy: CMakeFiles/predixy.dir/src/LatencyMonitor.cpp.o
predixy: CMakeFiles/predixy.dir/src/ListenSocket.cpp.o
predixy: CMakeFiles/predixy.dir/src/LogFileSink.cpp.o
predixy: CMakeFiles/predixy.dir/src/Logger.cpp.o
predixy: CMakeFiles/predixy.dir/src/main.cpp.o
predixy: CMakeFiles/predixy.dir/src/Proxy.cpp.o
predixy: CMakeFiles/predixy.dir/src/Reply.cpp.o
predixy: CMakeFiles/predixy.dir/src/Request.cpp.o
predixy: CMakeFiles/predixy.dir/src/RequestParser.cpp.o
predixy: CMakeFiles/predixy.dir/src/Response.cpp.o
predixy: CMakeFiles/predixy.dir/src/ResponseParser.cpp.o
predixy: CMakeFiles/predixy.dir/src/Server.cpp.o
predixy: CMakeFiles/predixy.dir/src/ServerGroup.cpp.o
predixy: CMakeFiles/predixy.dir/src/ServerPool.cpp.o
predixy: CMakeFiles/predixy.dir/src/Socket.cpp.o
predixy: CMakeFiles/predixy.dir/src/StandaloneServerPool.cpp.o
predixy: CMakeFiles/predixy.dir/src/Subscribe.cpp.o
predixy: CMakeFiles/predixy.dir/src/Timer.cpp.o
predixy: CMakeFiles/predixy.dir/build.make
predixy: CMakeFiles/predixy.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/Users/<USER>/CLionProjects/predixy/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_40) "Linking CXX executable predixy"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/predixy.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/predixy.dir/build: predixy

.PHONY : CMakeFiles/predixy.dir/build

CMakeFiles/predixy.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/predixy.dir/cmake_clean.cmake
.PHONY : CMakeFiles/predixy.dir/clean

CMakeFiles/predixy.dir/depend:
	cd /Users/<USER>/CLionProjects/predixy/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /Users/<USER>/CLionProjects/predixy /Users/<USER>/CLionProjects/predixy /Users/<USER>/CLionProjects/predixy/build /Users/<USER>/CLionProjects/predixy/build /Users/<USER>/CLionProjects/predixy/build/CMakeFiles/predixy.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/predixy.dir/depend

