## Authority control
## Authority {
##     Auth [password] {
##         Mode read|write|admin
##         [KeyPrefix Prefix1 Prefix2...]
##         [ReadKeyPrefix Prefix1 Prefix2...]
##         [WriteKeyPrefix Prefix1 Prefix2...]
##     }...
## }

## Example:
# Authority {
##------------------------------------------------------------------------
#     Auth {
#         Mode read
#     }
####  password is empty, this Auth is default auth
####  Mode read, client connection is readonly,
####  No KeyPrefix or ReadKeyPrefix defined, all key can be visit
##------------------------------------------------------------------------     
#     Auth abc {
#         Mode write
#     }
####  password is "abc", the client must send command Auth abc
####  Mode write, client connection can read and write
####  No KeyPrefix, ReadKeyPrefix, WriteKeyPrefix define, all key can be visit
##------------------------------------------------------------------------     
#     Auth bcd {
#         Mode admin
#     }
####  password is "bcd", the client must send command Auth bcd
####  Mode admin, client connection can read and write and admin,
####  the CONFIG command need admin permission
####  No KeyPrefix, ReadKeyPrefix, WriteKeyPrefix define, all key can be visit
##------------------------------------------------------------------------     
#     Auth cde {
#         Mode read
#         KeyPrefix User
#     }
####  password is "cde", the client must send command Auth cde
####  Mode read, client connection is readonly,
####  KeyPrefix User, client can read UserXXX key, eg: GET User.123,
####  if client request GET hello, will be deny
##------------------------------------------------------------------------     
#     Auth def {
#         Mode write
#         ReadKeyPrefix User Stats
#         WriteKeyPrefix User
#     }
####  password is "def", the client must send command Auth def
####  Mode read, client connection can read and write, but read and write
####  keyspace is diffrent, client can GET User.123 and also
####  SET User.123 SomeValue, but SET Stats.123 will be deny
##------------------------------------------------------------------------     
# }
## if no Authority spcified, equality below Authority
# Authority {
#     Auth {
#         Mode admin
#     }
# }

Authority {
    Auth {
        Mode write
    }
    Auth "#a complex password#" {
        Mode admin
    }
}

