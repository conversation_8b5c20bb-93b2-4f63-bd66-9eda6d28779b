## redis sentinel server pool define

##SentinelServerPool {
##    [Password xxx]                        #default no
##    [Databases number]                    #default 1
##    Hash atol|crc16
##    [HashTag "xx"]                        #default no
##    Distribution modula|random
##    [MasterReadPriority [0-100]]          #default 50
##    [StaticSlaveReadPriority [0-100]]     #default 0
##    [DynamicSlaveReadPriority [0-100]]    #default 0
##    [RefreshInterval number[s|ms|us]]     #default 1, means 1 second
##    [ServerTimeout number[s|ms|us]]       #default 0, server connection socket read/write timeout
##    [ServerFailureLimit number]           #default 10
##    [ServerRetryTimeout number[s|ms|us]]  #default 1
##    [KeepAlive seconds]                   #default 0, server connection tcp keepalive
##    Sentinels {
##        + addr
##        ...
##    }
##    Group xxx {
##        [+ addr]
##        ...
##    }
##}


## Examples:
#SentinelServerPool {
#    Databases 16
#    Hash crc16
#    HashTag "{}"
#    Distribution modula
#    MasterReadPriority 60
#    StaticSlaveReadPriority 50
#    DynamicSlaveReadPriority 50
#    RefreshInterval 1
#    ServerTimeout 1
#    ServerFailureLimit 10
#    ServerRetryTimeout 1
#    KeepAlive 120
#    Sentinels {
#        + 10.2.2.2:7500
#        + 10.2.2.3:7500
#        + 10.2.2.4:7500
#    }
#    Group shard001 {
#    }
#    Group shard002 {
#    }
#}
