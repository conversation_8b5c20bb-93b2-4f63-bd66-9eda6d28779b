============================================================
🔒 Predixy安全扫描报告
============================================================
扫描时间: 2025-06-12 15:39:57
项目目录: /Users/<USER>/CLionProjects/predixy

🔍 CVE数据库搜索结果
------------------------------
总计找到: 10 个相关CVE

1. CVE-2018-5241 (HIGH)
   评分: 7.5
   发布时间: 2018-05-29T13:29:00.617
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: Symantec Advanced Secure Gateway (ASG) 6.6 and 6.7, and ProxySG 6.5, 6.6, and 6.7 are susceptible to a SAML authentication bypass vulnerability. The products can be configured with a SAML authentication realm to authenticate network users in intercepted proxy traffic. When parsing SAML responses, ASG and ProxySG incorrectly handle XML nodes with comments. A remote attacker can modify a valid SAML response without invalidating its cryptographic signature. This may allow the attacker to bypass user authentication security controls in ASG and ProxySG. This vulnerability only affects authentication of network users in intercepted traffic. It does not affect administrator user authentication for the ASG and ProxySG management consoles.
   参考链接: http://www.securityfocus.com/bid/104282

2. CVE-2009-0630 (HIGH)
   评分: 7.1
   发布时间: 2009-03-27T16:30:02.017
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: The (1) Cisco Unified Communications Manager Express; (2) SIP Gateway Signaling Support Over Transport Layer Security (TLS) Transport; (3) Secure Signaling and Media Encryption; (4) Blocks Extensible Exchange Protocol (BEEP); (5) Network Admission Control HTTP Authentication Proxy; (6) Per-user URL Redirect for EAPoUDP, Dot1x, and MAC Authentication Bypass; (7) Distributed Director with HTTP Redirects; and (8) TCP DNS features in Cisco IOS 12.0 through 12.4 do not properly handle IP sockets, which allows remote attackers to cause a denial of service (outage or resource consumption) via a series of crafted TCP packets.
   参考链接: http://secunia.com/advisories/34438

3. CVE-2020-6750 (MEDIUM)
   评分: 4.3
   发布时间: 2020-01-09T20:15:11.567
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: GSocketClient in GNOME GLib through 2.62.4 may occasionally connect directly to a target address instead of connecting via a proxy server when configured to do so, because the proxy_addr field is mishandled. This bug is timing-dependent and may occur only sporadically depending on network delays. The greatest security relevance is in use cases where a proxy is used to help with privacy/anonymity, even though there is no technical barrier to a direct connection. NOTE: versions before 2.60 are unaffected.
   参考链接: https://bugzilla.suse.com/show_bug.cgi?id=1160668

4. CVE-2020-2035 (LOW)
   评分: 3.5
   发布时间: 2020-08-12T17:15:12.537
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: When SSL/TLS Forward Proxy Decryption mode has been configured to decrypt the web transactions, the PAN-OS URL filtering feature inspects the HTTP Host and URL path headers for policy enforcement on the decrypted HTTPS web transactions but does not consider Server Name Indication (SNI) field within the TLS Client Hello handshake. This allows a compromised host in a protected network to evade any security policy that uses URL filtering on a firewall configured with SSL Decryption in the Forward Proxy mode. A malicious actor can then use this technique to evade detection of communication on the TLS handshake phase between a compromised host and a remote malicious server. This technique does not increase the risk of a host being compromised in the network. It does not impact the confidentiality or availability of a firewall. This is considered to have a low impact on the integrity of the firewall because the firewall fails to enforce a policy on certain traffic that should have been blocked. This issue does not impact the URL filtering policy enforcement on clear text or encrypted web transactions. This technique can be used only after a malicious actor has compromised a host in the protected network and the TLS/SSL Decryption feature is enabled for the traffic that the attacker controls. Palo Alto Networks is not aware of any malware that uses this technique to exfiltrate data. This issue is applicable to all current versions of PAN-OS. This issue does not impact Panorama or WF-500 appliances.
   参考链接: https://security.paloaltonetworks.com/CVE-2020-2035

5. CVE-2023-42261 (Unknown)
   评分: 0
   发布时间: 2023-09-21T22:15:11.823
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: Mobile Security Framework (MobSF) <=v3.7.8 Beta is vulnerable to Insecure Permissions. NOTE: the vendor's position is that authentication is intentionally not implemented because the product is not intended for an untrusted network environment. Use cases requiring authentication could, for example, use a reverse proxy server.
   参考链接: https://github.com/MobSF/Mobile-Security-Framework-MobSF/blob/abb47659a19ac772765934f184c65fe16cb3bee7/docker-compose.yml#L30-L31

6. CVE-2023-41332 (Unknown)
   评分: 0
   发布时间: 2023-09-27T15:19:30.023
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: Cilium is a networking, observability, and security solution with an eBPF-based dataplane. In Cilium clusters where Cilium's Layer 7 proxy has been disabled, creating workloads with `policy.cilium.io/proxy-visibility` annotations (in Cilium >= v1.13) or `io.cilium.proxy-visibility` annotations (in Cilium <= v1.12) causes the Cilium agent to segfault on the node to which the workload is assigned. Existing traffic on the affected node will continue to flow, but the Cilium agent on the node will not able to process changes to workloads running on the node. This will also prevent workloads from being able to start on the affected node. The denial of service will be limited to the node on which the workload is scheduled, however an attacker may be able to schedule workloads on the node of their choosing, which could lead to targeted attacks. This issue has been resolved in Cilium versions 1.14.2, 1.13.7, and 1.12.14. Users unable to upgrade can avoid this denial of service attack by enabling the Layer 7 proxy.


   参考链接: https://github.com/cilium/cilium/pull/27597

7. CVE-2024-2056 (Unknown)
   评分: 0
   发布时间: 2024-03-05T20:16:01.703
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: Services that are running and bound to the loopback interface on the Artica Proxy are accessible through the proxy service. In particular, the "tailon" service is running, running as the root user, is bound to the loopback interface, and is listening on TCP port 7050. Security issues associated with exposing this network service are documented at gvalkov's 'tailon' GitHub repo. Using the tailon service, the contents of any file on the Artica Proxy can be viewed.
   参考链接: http://seclists.org/fulldisclosure/2024/Mar/14

8. CVE-2024-28249 (Unknown)
   评分: 0
   发布时间: 2024-03-18T22:15:08.503
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: Cilium is a networking, observability, and security solution with an eBPF-based dataplane. Prior to versions 1.13.13, 1.14.8, and 1.15.2, in Cilium clusters with IPsec enabled and traffic matching Layer 7 policies, IPsec-eligible traffic between a node's Envoy proxy and pods on other nodes is sent unencrypted and IPsec-eligible traffic between a node's DNS proxy and pods on other nodes is sent unencrypted. This issue has been resolved in Cilium 1.15.2, 1.14.8, and 1.13.13. There is no known workaround for this issue.
   参考链接: https://github.com/cilium/cilium/releases/tag/v1.13.13

9. CVE-2024-28250 (Unknown)
   评分: 0
   发布时间: 2024-03-18T22:15:08.750
   搜索关键词: network proxy security
   相关性评分: 4/10
   描述: Cilium is a networking, observability, and security solution with an eBPF-based dataplane. Starting in version 1.14.0 and prior to versions 1.14.8 and 1.15.2, In Cilium clusters with WireGuard enabled and traffic matching Layer 7 policies Wireguard-eligible traffic that is sent between a node's Envoy proxy and pods on other nodes is sent unencrypted and Wireguard-eligible traffic that is sent between a node's DNS proxy and pods on other nodes is sent unencrypted. This issue has been resolved in Cilium 1.14.8 and 1.15.2 in in native routing mode (`routingMode=native`) and in Cilium 1.14.4 in tunneling mode (`routingMode=tunnel`). Not that in tunneling mode, `encryption.wireguard.encapsulate` must be set to `true`. There is no known workaround for this issue.
   参考链接: https://github.com/cilium/cilium/releases/tag/v1.13.13

10. CVE-2023-36456 (Unknown)
   评分: 0
   发布时间: 2023-07-06T19:15:10.633
   搜索关键词: network proxy security
   相关性评分: 1/10
   描述: authentik is an open-source Identity Provider. Prior to versions 2023.4.3 and 2023.5.5, authentik does not verify the source of the X-Forwarded-For and X-Real-IP headers, both in the Python code and the go code. Only authentik setups that are directly accessible by users without a reverse proxy are susceptible to this. Possible spoofing of IP addresses in logs, downstream applications proxied by (built in) outpost, IP bypassing in custom flows if used.

This poses a possible security risk when someone has flows or policies that check the user's IP address, e.g. when they want to ignore the user's 2 factor authentication when the user is connected to the company network. A second security risk is that the IP addresses in the logfiles and user sessions are not reliable anymore. Anybody can spoof this address and one cannot verify that the user has logged in from the IP address that is in their account's log. A third risk is that this header is passed on to the proxied application behind an outpost. The application may do any kind of verification, logging, blocking or rate limiting based on the IP address, and this IP address can be overridden by anybody that want to.

Versions 2023.4.3 and 2023.5.5 contain a patch for this issue.

   参考链接: https://github.com/goauthentik/authentik/commit/15026748d19d490eb2baf9a9566ead4f805f7dff

🔧 CppCheck代码扫描结果
------------------------------
未发现代码问题

💡 修复建议
---------------
无需修复建议

📦 检测到的依赖库版本
-------------------------
总计检测到: 1 个依赖库

📚 openssl:
   版本: 3.4.0
   检测方式: system_command
   详细信息: OpenSSL 3.4.0 22 Oct 2024 (Library: OpenSSL 3.4.0 22 Oct 2024)

🔍 依赖库CVE检查结果
-------------------------
未发现版本相关的依赖库CVE

📊 扫描总结
---------------
CVE搜索结果: 10 个
CppCheck问题: 0 个
依赖库CVE: 0 个
修复建议: 0 条

风险评估:
高风险CVE: 2 个
高风险代码问题: 0 个
内存安全问题: 0 个
⚠️ 发现 2 个安全问题，建议优先处理

============================================================
报告生成完成
